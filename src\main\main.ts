import { app, BrowserWindow, ipcMain, dialog } from 'electron';
import * as path from 'path';
import { ProjectService } from './services/ProjectService';
import { DatabaseService } from './services/DatabaseService';
import { FileSystemService } from './services/FileSystemService';
import { GitService } from './services/GitService';
import { ProcessService } from './services/ProcessService';
import { DependencyService } from './services/DependencyService';
import { VirtualEnvironmentService } from './services/VirtualEnvironmentService';
import { ConfigurationService } from './services/ConfigurationService';
import { RepositoryService } from './services/RepositoryService';
import { ExecutionHistoryService } from './services/ExecutionHistoryService';
import { ProjectLogService } from './services/ProjectLogService';
import { GitConfigService } from './services/GitConfigService';
import { GitRepositoryService } from './services/GitRepositoryService';
import { ProjectAnalysisService } from './services/ProjectAnalysisService';
import { WalletConfigService } from './services/WalletConfigService';
import { GlobalWalletService } from './services/GlobalWalletService';
import { ProxyConfigService } from './services/ProxyConfigService';
import { ProxyManagerService } from './services/ProxyManagerService';
import { PerformanceMonitorService } from './services/PerformanceMonitorService';
import { BatchOperationService } from './services/BatchOperationService';
import { GitHubService } from './services/GitHubService';
import { ProjectDetectionService } from './services/ProjectDetectionService';
import { PseudoTerminalService } from './services/PseudoTerminalService';
import { TerminalLogService } from './services/TerminalLogService';

// 添加全局异常处理器，防止 EPIPE 等错误导致应用崩溃
process.on('uncaughtException', (error: Error) => {
  console.error('Uncaught Exception:', error.message);
  if (error.message.includes('EPIPE') || error.message.includes('ECONNRESET')) {
    console.log('Pipe error caught, continuing execution...');
  } else {
    console.error('Critical error, stack trace:', error.stack);
  }
});

process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  if (reason && reason.message && (reason.message.includes('EPIPE') || reason.message.includes('ECONNRESET'))) {
    console.log('Pipe rejection caught, continuing execution...');
  }
});

class MainProcess {
  private mainWindow: BrowserWindow | null = null;
  private projectService: ProjectService;
  private databaseService: DatabaseService;
  private fileSystemService: FileSystemService;
  private gitService: GitService;
  private processService: ProcessService;
  private dependencyService: DependencyService;
  private virtualEnvironmentService: VirtualEnvironmentService;
  private configurationService: ConfigurationService;
  private repositoryService: RepositoryService;
  private executionHistoryService: ExecutionHistoryService;
  private projectLogService: ProjectLogService;
  private gitConfigService: GitConfigService;
  private gitRepositoryService: GitRepositoryService;
  private projectAnalysisService: ProjectAnalysisService;
  private walletConfigService: WalletConfigService;
  private globalWalletService: GlobalWalletService;
  private proxyConfigService: ProxyConfigService;
  private proxyManagerService: ProxyManagerService;
  private performanceMonitorService: PerformanceMonitorService;
  private batchOperationService: BatchOperationService;
  private gitHubService: GitHubService;
  private projectDetectionService: ProjectDetectionService;
  private pseudoTerminalService: PseudoTerminalService;
  private terminalLogService: TerminalLogService;

  constructor() {
    this.databaseService = new DatabaseService();
    this.fileSystemService = new FileSystemService();
    this.gitService = new GitService();
    this.processService = new ProcessService();
    this.dependencyService = new DependencyService();
    this.virtualEnvironmentService = new VirtualEnvironmentService();
    this.configurationService = new ConfigurationService();
    this.repositoryService = new RepositoryService();
    this.executionHistoryService = new ExecutionHistoryService(this.databaseService);
    this.projectLogService = new ProjectLogService(this.databaseService);
    this.gitConfigService = new GitConfigService(this.databaseService);
    this.projectAnalysisService = new ProjectAnalysisService();
    this.walletConfigService = new WalletConfigService();
    this.globalWalletService = new GlobalWalletService();
    this.proxyConfigService = new ProxyConfigService();
    this.proxyManagerService = new ProxyManagerService();
    this.performanceMonitorService = new PerformanceMonitorService();
    this.gitHubService = new GitHubService();
    this.projectDetectionService = new ProjectDetectionService();
    this.pseudoTerminalService = new PseudoTerminalService();
    this.terminalLogService = new TerminalLogService(this.databaseService);

    // 先初始化 ProjectService，因为其他服务依赖它
    this.projectService = new ProjectService(
      this.databaseService,
      this.fileSystemService,
      this.gitService,
      this.processService
    );

    // 然后初始化依赖 ProjectService 的服务
    this.gitRepositoryService = new GitRepositoryService(this.gitConfigService, this.projectService);
    this.batchOperationService = new BatchOperationService(
      this.projectService,
      this.gitService,
      this.processService,
      this.dependencyService
    );
  }

  async initialize() {
    try {
      console.log('Initializing MainProcess...');

      // 首先初始化数据库
      console.log('Initializing database...');
      await this.databaseService.initialize();

      // 然后初始化执行历史服务
      console.log('Initializing execution history service...');
      await this.executionHistoryService.initialize();

      // 初始化项目日志服务
      console.log('Initializing project log service...');
      await this.projectLogService.initialize();

      // 初始化Git配置服务
      console.log('Initializing git config service...');
      await this.gitConfigService.initialize();

      // 初始化终端日志服务
      console.log('Initializing terminal log service...');
      await this.terminalLogService.initialize();

      // 设置服务引用（在所有服务初始化完成后）
      console.log('Setting up service references...');
      this.projectService.setProjectLogService(this.projectLogService);
      this.projectService.setExecutionHistoryService(this.executionHistoryService);
      this.pseudoTerminalService.setTerminalLogService(this.terminalLogService);

      // 加载项目
      console.log('Loading projects...');
      await this.projectService.loadProjects();

      // 迁移Python项目配置
      await this.projectService.migratePythonProjectConfigs();

      // 设置IPC处理器
      console.log('Setting up IPC handlers...');
      this.setupIpcHandlers();

      // 设置进程事件转发
      console.log('Setting up process event forwarding...');
      this.setupProcessEventForwarding();

      // 设置性能监控
      console.log('Setting up performance monitoring...');
      this.setupPerformanceMonitoring();

      // 设置性能监控内部事件处理
      this.setupPerformanceMonitoringEvents();

      // 设置批量操作事件转发
      console.log('Setting up batch operation event forwarding...');
      this.setupBatchOperationEventForwarding();

      // 设置项目日志事件转发
      console.log('Setting up project log event forwarding...');
      this.setupProjectLogEventForwarding();

      // 设置伪终端事件转发
      console.log('Setting up pseudo terminal event forwarding...');
      this.setupPseudoTerminalEventForwarding();

      // 设置终端日志事件转发
      console.log('Setting up terminal log event forwarding...');
      this.setupTerminalLogEventForwarding();

      // 创建窗口
      console.log('Creating main window...');
      this.createWindow();

      console.log('MainProcess initialized successfully');
    } catch (error) {
      console.error('Failed to initialize MainProcess:', error);
      throw error;
    }
  }

  private createWindow() {
    const isWindows = process.platform === 'win32';
    const isDev = process.env.NODE_ENV === 'development';

    this.mainWindow = new BrowserWindow({
      width: 1400,
      height: 900,
      minWidth: 1200,
      minHeight: 700,
      frame: false, // 去掉原生边框
      titleBarStyle: 'hidden', // 隐藏标题栏
      transparent: true, // 启用透明背景
      backgroundColor: '#00000000', // 完全透明背景
      hasShadow: false, // 禁用窗口阴影
      ...(process.platform === 'darwin' && {
        vibrancy: 'under-window',
        visualEffectState: 'active'
      }), // macOS毛玻璃效果
      ...(isWindows && {
        backgroundMaterial: 'none' // 禁用Windows背景材质
      }),
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, '../preload/preload.js'),
        devTools: isDev, // 开发模式下启用开发者工具
        experimentalFeatures: true, // 启用实验性功能
      },
      show: false,
    });

    // 根据环境加载不同的URL
    if (isDev) {
      // 开发模式：连接到Vite开发服务器
      const devServerUrl = process.env.VITE_DEV_SERVER_URL || 'http://localhost:3000';
      this.mainWindow.loadURL(devServerUrl);

      // 开发模式下打开开发者工具
      this.mainWindow.webContents.openDevTools();

      // Vite插件会自动处理热重载，不需要手动设置
      console.log('🔥 Vite hot reload enabled');
    } else {
      // 生产模式：加载打包后的文件
      this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    }

    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show();
    });

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // 监听窗口关闭事件，确保应用完全退出
    this.mainWindow.on('close', () => {
      // 在开发模式下，确保所有服务都被清理
      if (process.env.NODE_ENV === 'development') {
        this.cleanup();
      }
    });
  }

  private setupIpcHandlers() {
    // 项目管理
    ipcMain.handle('project:list', () => this.projectService.listProjects());
    ipcMain.handle('project:add', (_, project) => this.projectService.addProject(project));
    ipcMain.handle('project:update', (_, id, updates) => this.projectService.updateProject(id, updates));
    ipcMain.handle('project:delete', (_, id, deleteFiles) => this.projectService.deleteProject(id, deleteFiles));
    ipcMain.handle('project:start', (_, id, profileId) => this.projectService.startProject(id, profileId));
    ipcMain.handle('project:stop', (_, id) => this.projectService.stopProject(id));
    ipcMain.handle('project:restart', (_, id) => this.projectService.restartProject(id));
    ipcMain.handle('project:refresh-status', (_, projectId) => this.projectService.refreshProjectStatus(projectId));
    ipcMain.handle('project:refresh-all-status', () => this.projectService.refreshAllProjectsStatus());

    // 项目扫描
    ipcMain.handle('project:scan', (_, directories) => this.projectService.scanProjects(directories));
    ipcMain.handle('project:scan-with-progress', (_, directories) => this.projectService.scanProjectsWithProgress(directories));
    ipcMain.handle('project:detect-type', (_, path) => this.projectService.detectProjectType(path));

    // 项目检测
    ipcMain.handle('project:detect-project', (_, path) => this.projectDetectionService.detectProject(path));

    // 伪终端
    ipcMain.handle('terminal:create', (_, id, cwd, title) => this.pseudoTerminalService.createTerminal(id, cwd, title));
    ipcMain.handle('terminal:write', (_, id, data) => this.pseudoTerminalService.writeToTerminal(id, data));
    ipcMain.handle('terminal:resize', (_, id, cols, rows) => this.pseudoTerminalService.resizeTerminal(id, cols, rows));
    ipcMain.handle('terminal:close', (_, id) => this.pseudoTerminalService.closeTerminal(id));
    ipcMain.handle('terminal:minimize', (_, id) => this.pseudoTerminalService.minimizeTerminal(id));
    ipcMain.handle('terminal:restore', (_, id) => this.pseudoTerminalService.restoreTerminal(id));
    ipcMain.handle('terminal:get-session', (_, id) => this.pseudoTerminalService.getSession(id));
    ipcMain.handle('terminal:get-all-sessions', () => this.pseudoTerminalService.getAllSessions());
    ipcMain.handle('terminal:get-logs', (_, id, limit) => this.pseudoTerminalService.getTerminalLogs(id, limit));
    ipcMain.handle('terminal:clear-logs', (_, id) => this.pseudoTerminalService.clearTerminalLogs(id));

    // 进程管理
    ipcMain.handle('process:send-input', (_, pid, input) => this.processService.sendInput(pid, input));
    ipcMain.handle('process:kill', (_, pid) => this.processService.killProcess(pid));
    ipcMain.handle('process:get-running', () => this.processService.getRunningProcesses());

    // 文件系统操作
    ipcMain.handle('fs:select-directory', () => this.fileSystemService.selectDirectory());
    ipcMain.handle('fs:open-directory', (_, path) => this.fileSystemService.openDirectory(path));
    ipcMain.handle('fs:open-terminal', (_, path) => this.fileSystemService.openTerminal(path));
    ipcMain.handle('fs:read-file', (_, filePath) => this.fileSystemService.readFile(filePath));
    ipcMain.handle('fs:write-file', (_, filePath, content) => this.fileSystemService.writeFile(filePath, content));
    ipcMain.handle('fs:file-exists', (_, filePath) => this.fileSystemService.fileExists(filePath));

    // Git操作
    ipcMain.handle('git:status', (_, path) => this.gitService.getStatus(path));
    ipcMain.handle('git:pull', (_, path) => this.gitService.pull(path));
    ipcMain.handle('git:clone', (_, url, targetPath) => this.gitService.clone(url, targetPath));

    // 系统信息
    ipcMain.handle('system:info', () => this.getSystemInfo());
    ipcMain.handle('system:resources', (_, pid) => this.processService.getProcessResources(pid));

    // 依赖管理
    ipcMain.handle('deps:check', (_, projectPath, projectType) =>
      this.dependencyService.checkDependencies(projectPath, projectType));
    ipcMain.handle('deps:install', (_, projectPath, projectType, dependencyName, version) =>
      this.dependencyService.installDependency(projectPath, projectType, dependencyName, version));
    ipcMain.handle('deps:install-all', (_, projectPath, projectType) =>
      this.dependencyService.installAllDependencies(projectPath, projectType));
    ipcMain.handle('deps:uninstall', (_, projectPath, projectType, dependencyName) =>
      this.dependencyService.uninstallDependency(projectPath, projectType, dependencyName));
    ipcMain.handle('deps:clean-all', (_, projectPath, projectType) =>
      this.dependencyService.cleanAllDependencies(projectPath, projectType));

    // 虚拟环境管理
    ipcMain.handle('venv:check-python', () =>
      this.virtualEnvironmentService.checkPythonEnvironment());
    ipcMain.handle('venv:list', (_, projectPath) =>
      this.virtualEnvironmentService.listVirtualEnvironments(projectPath));
    ipcMain.handle('venv:create', (_, projectPath, envName, pythonVersion) =>
      this.virtualEnvironmentService.createVirtualEnvironment(projectPath, envName, pythonVersion));
    ipcMain.handle('venv:activate', (_, projectPath, envName) =>
      this.virtualEnvironmentService.activateVirtualEnvironment(projectPath, envName));
    ipcMain.handle('venv:delete', (_, projectPath, envName) =>
      this.virtualEnvironmentService.deleteVirtualEnvironment(projectPath, envName));
    ipcMain.handle('venv:install-package', (_, projectPath, envName, packageName, version) =>
      this.virtualEnvironmentService.installPackageInVenv(projectPath, envName, packageName, version));
    ipcMain.handle('venv:get-packages', (_, projectPath, envName) =>
      this.virtualEnvironmentService.getInstalledPackages(projectPath, envName));

    // 配置管理
    ipcMain.handle('config:get', (_, projectPath) =>
      this.configurationService.getProjectConfiguration(projectPath));
    ipcMain.handle('config:save', (_, projectPath, configuration) =>
      this.configurationService.saveProjectConfiguration(projectPath, configuration));
    ipcMain.handle('config:create-profile', (_, projectPath, profile) =>
      this.configurationService.createProfile(projectPath, profile));
    ipcMain.handle('config:update-profile', (_, projectPath, profileId, updates) =>
      this.configurationService.updateProfile(projectPath, profileId, updates));
    ipcMain.handle('config:delete-profile', (_, projectPath, profileId) =>
      this.configurationService.deleteProfile(projectPath, profileId));
    ipcMain.handle('config:get-default-profile', (_, projectPath) =>
      this.configurationService.getDefaultProfile(projectPath));
    ipcMain.handle('config:duplicate-profile', (_, projectPath, profileId, newName) =>
      this.configurationService.duplicateProfile(projectPath, profileId, newName));
    ipcMain.handle('config:export', (_, projectPath) =>
      this.configurationService.exportConfiguration(projectPath));
    ipcMain.handle('config:import', (_, projectPath, configJson) =>
      this.configurationService.importConfiguration(projectPath, configJson));

    // 仓库服务
    ipcMain.handle('repo:get-user-repos', (_, platform, username, token) =>
      this.repositoryService.getUserRepositories(platform, username, token));
    ipcMain.handle('repo:pull-repos', (_, repositories, targetDirectory, onProgress) =>
      this.repositoryService.pullRepositories(repositories, targetDirectory, onProgress));
    ipcMain.handle('repo:clone', (_, repository, targetDirectory) =>
      this.repositoryService.cloneRepository(repository, targetDirectory));
    ipcMain.handle('repo:filter', (_, repositories, filters) =>
      this.repositoryService.filterRepositories(repositories, filters));
    ipcMain.handle('repo:validate-git', () =>
      this.repositoryService.validateGitAvailability());
    ipcMain.handle('repo:validate-network', (_, platform) =>
      this.repositoryService.validateNetworkConnection(platform));
    ipcMain.handle('repo:get-details', (_, platform, fullName, token) =>
      this.repositoryService.getRepositoryDetails(platform, fullName, token));

    // 执行历史
    ipcMain.handle('history:get', (_, filter) =>
      this.executionHistoryService.getExecutionHistory(filter));
    ipcMain.handle('history:get-record', (_, executionId) =>
      this.executionHistoryService.getExecutionRecord(executionId));
    ipcMain.handle('history:get-stats', (_, projectId) =>
      this.executionHistoryService.getExecutionStats(projectId));
    ipcMain.handle('history:delete', (_, executionId) =>
      this.executionHistoryService.deleteExecutionRecord(executionId));
    ipcMain.handle('history:delete-project', (_, projectId) =>
      this.executionHistoryService.deleteProjectExecutionHistory(projectId));
    ipcMain.handle('history:cleanup', (_, daysToKeep) =>
      this.executionHistoryService.cleanupOldRecords(daysToKeep));
    ipcMain.handle('history:get-running', () =>
      this.executionHistoryService.getRunningExecutions());

    // 项目日志
    ipcMain.handle('logs:get', (_, projectId, limit) =>
      this.projectLogService.getProjectLogs(projectId, limit));
    ipcMain.handle('logs:clear', (_, projectId) =>
      this.projectLogService.clearProjectLogs(projectId));
    ipcMain.handle('logs:cleanup', (_, projectId, keepCount) =>
      this.projectLogService.cleanupOldLogs(projectId, keepCount));

    // Git配置相关
    ipcMain.handle('git-config:add', (_, config) =>
      this.gitConfigService.addGitConfig(config));
    ipcMain.handle('git-config:update', (_, id, updates) =>
      this.gitConfigService.updateGitConfig(id, updates));
    ipcMain.handle('git-config:get-all', (_, filter) =>
      this.gitConfigService.getGitConfigs(filter));
    ipcMain.handle('git-config:get', (_, id) =>
      this.gitConfigService.getGitConfig(id));
    ipcMain.handle('git-config:get-default', () =>
      this.gitConfigService.getDefaultGitConfig());
    ipcMain.handle('git-config:delete', (_, id) =>
      this.gitConfigService.deleteGitConfig(id));
    ipcMain.handle('git-config:set-default', (_, id) =>
      this.gitConfigService.setDefaultGitConfig(id));

    // 用户名历史记录
    ipcMain.handle('git-config:save-username-history', (_, username, provider) =>
      this.gitConfigService.saveUsernameHistory(username, provider));
    ipcMain.handle('git-config:get-username-history', (_, provider, limit) =>
      this.gitConfigService.getUsernameHistory(provider, limit));
    ipcMain.handle('git-config:cleanup-username-history', (_, keepCount) =>
      this.gitConfigService.cleanupUsernameHistory(keepCount));

    // 令牌历史记录
    ipcMain.handle('git-config:save-token-history', (_, provider, token, tokenName) =>
      this.gitConfigService.saveTokenHistory(provider, token, tokenName));
    ipcMain.handle('git-config:get-token-history', (_, provider, limit) =>
      this.gitConfigService.getTokenHistory(provider, limit));
    ipcMain.handle('git-config:delete-token-history', (_, provider, token) =>
      this.gitConfigService.deleteTokenHistory(provider, token));
    ipcMain.handle('git-config:cleanup-token-history', (_, keepCount) =>
      this.gitConfigService.cleanupTokenHistory(keepCount));

    // 目录历史记录
    ipcMain.handle('git-config:save-directory-history', (_, directoryPath) =>
      this.gitConfigService.saveDirectoryHistory(directoryPath));
    ipcMain.handle('git-config:get-directory-history', (_, limit) =>
      this.gitConfigService.getDirectoryHistory(limit));
    ipcMain.handle('git-config:get-last-used-directory', () =>
      this.gitConfigService.getLastUsedDirectory());
    ipcMain.handle('git-config:cleanup-directory-history', (_, keepCount) =>
      this.gitConfigService.cleanupDirectoryHistory(keepCount));

    // GitHub API服务
    ipcMain.handle('github:get-user-repositories', (_, username, token) =>
      this.gitHubService.getUserRepositories(username, token));
    ipcMain.handle('github:get-gitee-repositories', (_, username, token) =>
      this.gitHubService.getGiteeUserRepositories(username, token));
    ipcMain.handle('github:get-repositories-by-platform', (_, platform, username, token) =>
      this.gitHubService.getRepositoriesByPlatform(platform, username, token));
    ipcMain.handle('github:get-repositories-by-token', (_, platform, token) =>
      this.gitHubService.getRepositoriesByToken(platform, token));
    ipcMain.handle('github:clone-repository', (_, repository, targetPath, useSSH) =>
      this.gitHubService.cloneRepository(repository, targetPath, useSSH));

    // Git仓库相关
    ipcMain.handle('git-repo:get-repositories', (_, configId, filter) =>
      this.gitRepositoryService.getRepositories(configId, filter));
    ipcMain.handle('git-repo:pull-projects', async (_, request) => {
      const results: any[] = [];

      await this.gitRepositoryService.pullProjects(request, (progress) => {
        // 发送进度更新到渲染进程
        this.mainWindow?.webContents.send('git-pull:progress', progress);
      });

      return results;
    });

    // 项目分析
    ipcMain.handle('analysis:files', (_, projectPath) =>
      this.projectAnalysisService.analyzeProjectFiles(projectPath));
    ipcMain.handle('analysis:environment', (_, projectPath) =>
      this.projectAnalysisService.getProjectEnvironment(projectPath));
    ipcMain.handle('analysis:git', (_, projectPath) =>
      this.projectAnalysisService.getGitInfo(projectPath));
    ipcMain.handle('analysis:detect-type', (_, projectPath) =>
      this.projectAnalysisService.detectProjectType(projectPath));

    // 钱包配置
    ipcMain.handle('wallet:get-config', (_, projectPath) =>
      this.walletConfigService.getProjectWalletConfig(projectPath));
    ipcMain.handle('wallet:save-config', (_, projectPath, config) =>
      this.walletConfigService.saveProjectWalletConfig(projectPath, config));
    ipcMain.handle('wallet:add', (_, projectPath, wallet) =>
      this.walletConfigService.addWallet(projectPath, wallet));
    ipcMain.handle('wallet:update', (_, projectPath, walletId, updates) =>
      this.walletConfigService.updateWallet(projectPath, walletId, updates));
    ipcMain.handle('wallet:delete', (_, projectPath, walletId) =>
      this.walletConfigService.deleteWallet(projectPath, walletId));
    ipcMain.handle('wallet:get-default', (_, projectPath) =>
      this.walletConfigService.getDefaultWallet(projectPath));
    ipcMain.handle('wallet:validate', (_, wallet) =>
      this.walletConfigService.validateWalletConnection(wallet));

    // 全局钱包管理
    ipcMain.handle('global-wallet:get-config', () =>
      this.globalWalletService.getGlobalWalletConfig());
    ipcMain.handle('global-wallet:save-config', (_, config) =>
      this.globalWalletService.saveGlobalWalletConfig(config));
    ipcMain.handle('global-wallet:add', (_, wallet) =>
      this.globalWalletService.addWallet(wallet));
    ipcMain.handle('global-wallet:add-batch', (_, wallets) =>
      this.globalWalletService.addWallets(wallets));
    ipcMain.handle('global-wallet:update', (_, walletId, updates) =>
      this.globalWalletService.updateWallet(walletId, updates));
    ipcMain.handle('global-wallet:delete', (_, walletId) =>
      this.globalWalletService.deleteWallet(walletId));
    ipcMain.handle('global-wallet:delete-batch', (_, walletIds) =>
      this.globalWalletService.deleteWallets(walletIds));
    ipcMain.handle('global-wallet:import-from-file', (_, filePath, chain) =>
      this.globalWalletService.importWalletsFromFile(filePath, chain));
    ipcMain.handle('global-wallet:create-batch', (_, count, chain) =>
      this.globalWalletService.createWallets(count, chain));
    ipcMain.handle('global-wallet:get-address-from-mnemonic', (_, mnemonic, chain, derivationPath) =>
      this.globalWalletService.getAddressFromMnemonic(mnemonic, chain, derivationPath));

    // 代理配置
    ipcMain.handle('proxy:get-config', (_, projectPath) =>
      this.proxyConfigService.getProjectProxyConfig(projectPath));
    ipcMain.handle('proxy:save-config', (_, projectPath, config) =>
      this.proxyConfigService.saveProjectProxyConfig(projectPath, config));
    ipcMain.handle('proxy:add', (_, projectPath, proxy) =>
      this.proxyConfigService.addProxy(projectPath, proxy));
    ipcMain.handle('proxy:update', (_, projectPath, proxyId, updates) =>
      this.proxyConfigService.updateProxy(projectPath, proxyId, updates));
    ipcMain.handle('proxy:delete', (_, projectPath, proxyId) =>
      this.proxyConfigService.deleteProxy(projectPath, proxyId));
    ipcMain.handle('proxy:get-default', (_, projectPath) =>
      this.proxyConfigService.getDefaultProxy(projectPath));
    ipcMain.handle('proxy:test', (_, proxy) =>
      this.proxyConfigService.testProxyConnection(proxy));
    ipcMain.handle('proxy:get-system', () =>
      this.proxyConfigService.getSystemProxySettings());

    // 代理管理
    ipcMain.handle('proxy-manager:get-config', () =>
      this.proxyManagerService.getProxyConfig());
    ipcMain.handle('proxy-manager:save-config', (_, config) =>
      this.proxyManagerService.saveProxyConfig(config));
    ipcMain.handle('proxy-manager:fetch-all', () =>
      this.proxyManagerService.fetchProxiesFromAllSources());
    ipcMain.handle('proxy-manager:test-proxy', (_, proxy, testUrl, timeout) =>
      this.proxyManagerService.testProxy(proxy, testUrl, timeout));
    ipcMain.handle('proxy-manager:test-batch', async (event, proxies, maxConcurrent) => {
      // 创建进度回调函数
      const progressCallback = (progress: any) => {
        event.sender.send('proxy-test-progress', progress);
      };
      return this.proxyManagerService.testProxiesBatch(proxies, maxConcurrent, progressCallback);
    });
    ipcMain.handle('proxy-manager:delete', (_, proxyIds) =>
      this.proxyManagerService.deleteProxies(proxyIds));

    // 性能监控
    ipcMain.handle('performance:start', () =>
      this.performanceMonitorService.startMonitoring());
    ipcMain.handle('performance:stop', () =>
      this.performanceMonitorService.stopMonitoring());
    ipcMain.handle('performance:get-system', () =>
      this.performanceMonitorService.getSystemMetrics());
    ipcMain.handle('performance:get-process', async (_, pid) => {
      try {
        return await this.performanceMonitorService.getProcessMetrics(pid);
      } catch (error) {
        // 静默处理进程不存在的错误，避免大量错误日志
        return null;
      }
    });
    ipcMain.handle('performance:get-history', (_, limit) =>
      this.performanceMonitorService.getMetricsHistory(limit));
    ipcMain.handle('performance:get-process-history', (_, pid, limit) =>
      this.performanceMonitorService.getProcessMetricsHistory(pid, limit));
    ipcMain.handle('performance:get-alerts', (_, limit) =>
      this.performanceMonitorService.getAlerts(limit));
    ipcMain.handle('performance:clear-alerts', () =>
      this.performanceMonitorService.clearAlerts());
    ipcMain.handle('performance:set-thresholds', (_, thresholds) =>
      this.performanceMonitorService.setThresholds(thresholds));
    ipcMain.handle('performance:get-thresholds', () =>
      this.performanceMonitorService.getThresholds());
    ipcMain.handle('performance:start-process', (_, pid, projectId) =>
      this.performanceMonitorService.startProcessMonitoring(pid, projectId));
    ipcMain.handle('performance:stop-process', (_, pid) =>
      this.performanceMonitorService.stopProcessMonitoring(pid));

    // 批量操作
    ipcMain.handle('batch:execute', (_, type, projectIds, options) =>
      this.batchOperationService.executeBatchOperation(type, projectIds, options));
    ipcMain.handle('batch:cancel', (_, operationId) =>
      this.batchOperationService.cancelBatchOperation(operationId));
    ipcMain.handle('batch:get', (_, operationId) =>
      this.batchOperationService.getBatchOperation(operationId));
    ipcMain.handle('batch:get-all', () =>
      this.batchOperationService.getAllBatchOperations());
    ipcMain.handle('batch:cleanup', () =>
      this.batchOperationService.cleanupCompletedOperations());
    ipcMain.handle('batch:get-stats', () =>
      this.batchOperationService.getOperationStats());
    ipcMain.handle('batch:estimate-time', (_, type, projectCount) =>
      this.batchOperationService.estimateOperationTime(type, projectCount));
    ipcMain.handle('batch:validate', (_, type, projectIds) =>
      this.batchOperationService.validateBatchOperation(type, projectIds));

    // 窗口控制
    ipcMain.handle('window:minimize', () => {
      if (this.mainWindow) {
        this.mainWindow.minimize();
      }
    });

    ipcMain.handle('window:maximize', () => {
      if (this.mainWindow) {
        if (this.mainWindow.isMaximized()) {
          this.mainWindow.unmaximize();
        } else {
          this.mainWindow.maximize();
        }
      }
    });

    ipcMain.handle('window:unmaximize', () => {
      if (this.mainWindow) {
        this.mainWindow.unmaximize();
      }
    });

    ipcMain.handle('window:close', () => {
      if (this.mainWindow) {
        this.mainWindow.close();
      }
    });

    ipcMain.handle('window:is-maximized', () => {
      return this.mainWindow ? this.mainWindow.isMaximized() : false;
    });

    // 对话框
    ipcMain.handle('dialog:showOpenDialog', async (_, options) => {
      if (this.mainWindow) {
        return await dialog.showOpenDialog(this.mainWindow, options);
      }
      return { canceled: true, filePaths: [] };
    });

    ipcMain.handle('dialog:showSaveDialog', async (_, options) => {
      if (this.mainWindow) {
        return await dialog.showSaveDialog(this.mainWindow, options);
      }
      return { canceled: true, filePath: '' };
    });

    ipcMain.handle('dialog:showMessageBox', async (_, options) => {
      if (this.mainWindow) {
        return await dialog.showMessageBox(this.mainWindow, options);
      }
      return { response: 0, checkboxChecked: false };
    });

    // 文件系统临时文件
    ipcMain.handle('fs:create-temp-file', async (_, content: string) => {
      const os = require('os');
      const path = require('path');
      const fs = require('fs');

      const tempDir = os.tmpdir();
      const tempFileName = `wallet_import_${Date.now()}.txt`;
      const tempFilePath = path.join(tempDir, tempFileName);

      fs.writeFileSync(tempFilePath, content, 'utf-8');
      return tempFilePath;
    });
  }

  private setupProcessEventForwarding() {
    // 转发进程输出事件到渲染进程
    this.processService.on('process-output', (pid: number, output: string, type: 'stdout' | 'stderr') => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('process:output', pid, output, type);
      }
    });

    // 转发进程资源使用事件到渲染进程
    this.processService.on('process-resources', (pid: number, resources: any) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('process:resources', pid, resources);
      }
    });

    // 转发进程退出事件到渲染进程
    this.processService.on('process-exit', (pid: number, code: number, signal: string) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('process:exit', pid, code, signal);
      }
    });

    // 转发进程错误事件到渲染进程
    this.processService.on('process-error', (pid: number, error: Error) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('process:error', pid, error.message);
      }
    });
  }

  private setupPerformanceMonitoring() {
    // 转发性能监控事件到渲染进程
    this.performanceMonitorService.on('system-metrics', (metrics) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('performance:system-metrics', metrics);
      }
    });

    this.performanceMonitorService.on('process-metrics', (metrics) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('performance:process-metrics', metrics);
      }
    });

    this.performanceMonitorService.on('performance-alert', (alert) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('performance:alert', alert);
      }
    });

    // 启动性能监控
    this.performanceMonitorService.startMonitoring();
  }

  private setupPerformanceMonitoringEvents() {
    // 监听来自ProjectService的性能监控事件
    ipcMain.on('performance:start-process-monitoring', (_, pid: number, projectId: string) => {
      try {
        this.performanceMonitorService.startProcessMonitoring(pid, projectId);
      } catch (error) {
        console.error('Failed to start performance monitoring:', error);
      }
    });

    ipcMain.on('performance:stop-process-monitoring', (_, pid: number) => {
      try {
        this.performanceMonitorService.stopProcessMonitoring(pid);
      } catch (error) {
        console.error('Failed to stop performance monitoring:', error);
      }
    });

    ipcMain.on('performance:process-ended', (_, pid: number, projectId: string) => {
      try {
        // 通知项目服务更新项目状态
        this.projectService.handleProcessEnded(pid, projectId);
      } catch (error) {
        console.error('Failed to handle process ended:', error);
      }
    });
  }

  private setupBatchOperationEventForwarding() {
    // 转发批量操作事件到渲染进程
    this.batchOperationService.on('operation-created', (operation) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('batch:operation-created', operation);
      }
    });

    this.batchOperationService.on('operation-started', (operation) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('batch:operation-started', operation);
      }
    });

    this.batchOperationService.on('operation-progress', (operation) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('batch:operation-progress', operation);
      }
    });

    this.batchOperationService.on('operation-completed', (operation) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('batch:operation-completed', operation);
      }
    });
  }

  private setupProjectLogEventForwarding() {
    // 转发项目日志事件到渲染进程
    this.projectLogService.on('log-added', (logEntry) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('project:log-added', logEntry);
      }
    });

    this.batchOperationService.on('operation-failed', (operation, error) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('batch:operation-failed', operation, error);
      }
    });

    this.batchOperationService.on('operation-cancelled', (operation) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('batch:operation-cancelled', operation);
      }
    });
  }

  private setupPseudoTerminalEventForwarding() {
    // 转发伪终端事件到渲染进程
    this.pseudoTerminalService.on('terminal-output', (id, data) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('terminal:output', { id, data });
      }
    });

    this.pseudoTerminalService.on('terminal-exit', (id, exitCode) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('terminal:exit', { id, exitCode });
      }
    });

    this.pseudoTerminalService.on('terminal-error', (id, error) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('terminal:error', { id, error });
      }
    });
  }

  private getSystemInfo() {
    return {
      platform: process.platform,
      arch: process.arch,
      nodeVersion: process.version,
      electronVersion: process.versions.electron,
    };
  }

  getMainWindow() {
    return this.mainWindow;
  }

  private setupTerminalLogEventForwarding() {
    // 转发终端日志事件到渲染进程
    this.terminalLogService.on('terminal-log-added', (logEntry) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send('terminal:log-added', logEntry);
      }
    });
  }

  cleanup() {
    // 停止性能监控
    this.performanceMonitorService.stopMonitoring();

    // 清理进程服务
    this.processService.destroy();

    // 关闭数据库连接
    this.databaseService.close().catch(console.error);
  }


}

const mainProcess = new MainProcess();

app.whenReady().then(() => {
  mainProcess.initialize();
});

app.on('window-all-closed', () => {
  // 在所有平台上都退出应用（包括macOS）
  mainProcess.cleanup();
  app.quit();
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    mainProcess.initialize();
  }
});

app.on('before-quit', () => {
  // 应用退出前清理资源
  mainProcess.cleanup();
});

export { mainProcess };

import React, { useState, useEffect } from 'react';
import {
  IoClose,
  IoDocument,
  IoSave,
  IoGlobe,
  IoFolder,
  IoRefresh,
  IoCheckbox,
  IoSquareOutline,
  IoCheckboxOutline,
  IoCopy
} from 'react-icons/io5';
import { useNotification } from '../../contexts/NotificationContext';
import { Project } from '@shared/types/project';
import Modal from '../Modal';
import './ProxyFileEditDialog.css';

interface ProxyInfo {
  id: string;
  host: string;
  port: number;
  type: string;
  country?: string;
  speed?: number;
  uptime?: number;
  lastChecked?: Date;
  isWorking?: boolean;
}

interface ProxyFileEditDialogProps {
  project: Project;
  onClose: () => void;
}

const ProxyFileEditDialog: React.FC<ProxyFileEditDialogProps> = ({ project, onClose }) => {
  const { showSuccess, showError, showWarning } = useNotification();
  const [selectedFile, setSelectedFile] = useState<string>('');
  const [fileContent, setFileContent] = useState<string>('');
  const [originalContent, setOriginalContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [globalProxies, setGlobalProxies] = useState<ProxyInfo[]>([]);
  const [showProxyList, setShowProxyList] = useState(false);
  const [selectedProxies, setSelectedProxies] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadGlobalProxies();
  }, []);

  const loadGlobalProxies = async () => {
    try {
      const config = await window.electronAPI.proxyManager.getConfig();
      setGlobalProxies(config.proxies || []);
    } catch (error) {
      console.error('加载全局代理失败:', error);
    }
  };

  const handleCopyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      showSuccess(`${type}已复制到剪贴板`);
    } catch (error) {
      showError('复制失败', error instanceof Error ? error.message : '未知错误');
    }
  };

  const handleSelectFile = async () => {
    try {
      const result = await window.electronAPI.dialog.showOpenDialog({
        title: '选择代理文件',
        defaultPath: project.path,
        filters: [
          { name: '文本文件', extensions: ['txt'] },
          { name: '配置文件', extensions: ['conf', 'config', 'ini'] },
          { name: 'JSON文件', extensions: ['json'] },
          { name: '所有文件', extensions: ['*'] }
        ],
        properties: ['openFile']
      });

      if (!result.canceled && result.filePaths.length > 0) {
        const filePath = result.filePaths[0];
        setSelectedFile(filePath);
        await loadFileContent(filePath);
      }
    } catch (error) {
      showError('选择文件失败', error instanceof Error ? error.message : '未知错误');
    }
  };

  const loadFileContent = async (filePath: string) => {
    try {
      setIsLoading(true);
      const content = await window.electronAPI.fs.readFile(filePath);
      setFileContent(content);
      setOriginalContent(content);
    } catch (error) {
      showError('读取文件失败', error instanceof Error ? error.message : '未知错误');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveFile = async () => {
    if (!selectedFile) {
      showWarning('请先选择文件');
      return;
    }

    try {
      setIsLoading(true);
      await window.electronAPI.fs.writeFile(selectedFile, fileContent);
      setOriginalContent(fileContent);
      showSuccess('文件保存成功');
    } catch (error) {
      showError('保存文件失败', error instanceof Error ? error.message : '未知错误');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefreshFile = async () => {
    if (!selectedFile) {
      showWarning('请先选择文件');
      return;
    }

    if (fileContent !== originalContent) {
      const confirmed = window.confirm('文件已修改，刷新将丢失未保存的更改。确定要继续吗？');
      if (!confirmed) return;
    }

    await loadFileContent(selectedFile);
  };

  const toggleProxySelection = (proxyId: string) => {
    const newSelected = new Set(selectedProxies);
    if (newSelected.has(proxyId)) {
      newSelected.delete(proxyId);
    } else {
      newSelected.add(proxyId);
    }
    setSelectedProxies(newSelected);
  };

  const selectAllProxies = () => {
    const workingProxies = globalProxies.filter(p => p.isWorking);
    if (selectedProxies.size === workingProxies.length) {
      setSelectedProxies(new Set());
    } else {
      setSelectedProxies(new Set(workingProxies.map(p => p.id)));
    }
  };

  const insertProxy = (proxy: ProxyInfo) => {
    const textarea = document.getElementById('proxy-editor') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const proxyString = `${proxy.host}:${proxy.port}`;
    const newContent = fileContent.substring(0, start) + proxyString + fileContent.substring(end);

    setFileContent(newContent);

    // 重新聚焦并设置光标位置
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + proxyString.length, start + proxyString.length);
    }, 0);
  };

  const insertSelectedProxies = () => {
    const selectedProxiesList = globalProxies.filter(p => selectedProxies.has(p.id));
    if (selectedProxiesList.length === 0) {
      showWarning('请选择要插入的代理');
      return;
    }

    const textarea = document.getElementById('proxy-editor') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const proxyStrings = selectedProxiesList.map(p => `${p.host}:${p.port}`).join('\n');
    const newContent = fileContent.substring(0, start) + proxyStrings + fileContent.substring(end);

    setFileContent(newContent);
    setSelectedProxies(new Set());
    setShowProxyList(false);

    // 重新聚焦并设置光标位置
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + proxyStrings.length, start + proxyStrings.length);
    }, 0);

    showSuccess(`已插入 ${selectedProxiesList.length} 个代理`);
  };

  const getFileName = () => {
    if (!selectedFile) return '未选择文件';
    return selectedFile.split(/[/\\]/).pop() || selectedFile;
  };

  const hasUnsavedChanges = fileContent !== originalContent;

  const getProxyStatusColor = (proxy: ProxyInfo) => {
    if (proxy.isWorking === true) return '#28a745';
    if (proxy.isWorking === false) return '#dc3545';
    return '#6c757d';
  };

  const getCountryFlag = (country?: string) => {
    if (!country) return '🌍';
    const flags: { [key: string]: string } = {
      'US': '🇺🇸', 'CN': '🇨🇳', 'JP': '🇯🇵', 'KR': '🇰🇷', 'DE': '🇩🇪',
      'FR': '🇫🇷', 'GB': '🇬🇧', 'CA': '🇨🇦', 'AU': '🇦🇺', 'RU': '🇷🇺'
    };
    return flags[country.toUpperCase()] || '🌍';
  };

  return (
    <Modal onClose={onClose} className="proxy-file-edit-dialog">
      <div className="dialog-header">
        <div className="header-left">
          <IoDocument className="dialog-icon" />
          <div>
            <h2 className="dialog-title">代理文件编辑</h2>
            <p className="dialog-subtitle">{project.name}</p>
          </div>
        </div>
        <button className="dialog-close" onClick={onClose}>
          <IoClose />
        </button>
      </div>

      <div className="dialog-body">
        <div className="file-selector">
          <div className="file-info">
            <span className="file-name">{getFileName()}</span>
            {hasUnsavedChanges && <span className="unsaved-indicator">●</span>}
          </div>
          <div className="file-actions">
            <button
              className="btn btn-outline btn-sm"
              onClick={handleSelectFile}
              title="选择文件"
            >
              <IoFolder />
              选择文件
            </button>
            {selectedFile && (
              <button
                className="btn btn-outline btn-sm"
                onClick={handleRefreshFile}
                title="刷新文件"
                disabled={isLoading}
              >
                <IoRefresh />
                刷新
              </button>
            )}
          </div>
        </div>

        {selectedFile && (
          <div className="editor-container">
            <div className="editor-toolbar">
              <button
                className="btn btn-primary btn-sm"
                onClick={() => setShowProxyList(!showProxyList)}
                title="插入代理"
              >
                <IoGlobe />
                插入代理
              </button>
              <button
                className="btn btn-success btn-sm"
                onClick={handleSaveFile}
                disabled={isLoading || !hasUnsavedChanges}
                title="保存文件"
              >
                <IoSave />
                保存
              </button>
            </div>

            {showProxyList && (
              <div className="proxy-list-panel">
                <div className="proxy-list-header">
                  <h4>选择代理</h4>
                  <div className="proxy-list-actions">
                    <button
                      className="btn btn-sm btn-outline"
                      onClick={selectAllProxies}
                      title={selectedProxies.size > 0 ? '取消全选' : '全选'}
                    >
                      {selectedProxies.size > 0 ? <IoCheckboxOutline /> : <IoSquareOutline />}
                      {selectedProxies.size > 0 ? '取消全选' : '全选'}
                    </button>
                    {selectedProxies.size > 0 && (
                      <button
                        className="btn btn-sm btn-primary"
                        onClick={insertSelectedProxies}
                        title="批量插入选中的代理"
                      >
                        <IoGlobe />
                        批量插入 ({selectedProxies.size})
                      </button>
                    )}
                  </div>
                </div>
                <div className="proxy-item-list">
                  {globalProxies.filter(p => p.isWorking).map((proxy) => (
                    <div key={proxy.id} className="proxy-item">
                      <div className="proxy-selector">
                        <button
                          className="checkbox-btn"
                          onClick={() => toggleProxySelection(proxy.id)}
                          title={selectedProxies.has(proxy.id) ? '取消选择' : '选择'}
                        >
                          {selectedProxies.has(proxy.id) ? <IoCheckbox /> : <IoSquareOutline />}
                        </button>
                      </div>
                      <div className="proxy-info">
                        <div className="proxy-address-line">
                          <span className="proxy-address">{proxy.host}:{proxy.port}</span>
                          <div className="proxy-meta">
                            <span className="proxy-country">{getCountryFlag(proxy.country)}</span>
                            <span
                              className="proxy-status"
                              style={{ color: getProxyStatusColor(proxy) }}
                            >
                              {proxy.isWorking ? '✓' : proxy.isWorking === false ? '✗' : '?'}
                            </span>
                            {proxy.speed && (
                              <span className="proxy-speed">{proxy.speed}ms</span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="proxy-actions">
                        <button
                          className="btn btn-sm btn-outline"
                          onClick={() => handleCopyToClipboard(`${proxy.host}:${proxy.port}`, '代理地址')}
                          title="复制代理地址"
                        >
                          <IoCopy />
                        </button>
                        <button
                          className="btn btn-sm btn-primary"
                          onClick={() => insertProxy(proxy)}
                          title="插入代理"
                        >
                          <IoGlobe />
                          插入
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <textarea
              id="proxy-editor"
              className="proxy-editor"
              value={fileContent}
              onChange={(e) => setFileContent(e.target.value)}
              placeholder="选择代理文件后在此编辑内容..."
              disabled={isLoading}
            />
          </div>
        )}

        {!selectedFile && (
          <div className="empty-state">
            <IoDocument className="empty-icon" />
            <h3>选择代理文件</h3>
            <p>点击"选择文件"按钮选择要编辑的代理文件</p>
            <button className="btn btn-primary" onClick={handleSelectFile}>
              <IoFolder />
              选择文件
            </button>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default ProxyFileEditDialog;

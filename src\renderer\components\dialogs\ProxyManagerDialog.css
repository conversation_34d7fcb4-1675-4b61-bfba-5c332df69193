.proxy-manager-dialog {
  width: 95vw;
  max-width: 1400px;
  height: 90vh;
  max-height: 900px;
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.dialog-icon {
  font-size: var(--font-size-2xl);
  color: var(--primary-color);
}

.dialog-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.dialog-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0;
}

.dialog-close {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
}

.dialog-close:hover {
  background-color: var(--gray-200);
  color: var(--text-primary);
}

.dialog-body {
  flex: 1;
  padding: var(--spacing-lg);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 工具栏 */
.proxy-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.toolbar-left h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.test-progress,
.fetch-progress {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--bg-secondary);
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.fetch-progress {
  background: linear-gradient(135deg, #20c997, #17a085);
  color: white;
}

.current-proxy {
  font-family: monospace;
  font-size: var(--font-size-xs);
  background: rgba(0, 0, 0, 0.1);
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
}

.auto-clean {
  font-size: var(--font-size-xs);
  background: var(--danger-color);
  color: white;
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
  margin-left: auto;
}

.toolbar-center {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.filters {
  display: flex;
  gap: var(--spacing-sm);
}

.filter-select {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
}

.toolbar-right {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

/* 代理表格 */
.proxy-table-container {
  flex: 1;
  overflow: auto;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background: var(--bg-primary);
}

.proxy-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-sm);
  min-width: 1200px;
}

.proxy-table th,
.proxy-table td {
  padding: var(--spacing-sm);
  text-align: left;
  border-bottom: 1px solid var(--border-color);
  vertical-align: middle;
}

.proxy-table th {
  background: var(--bg-secondary);
  font-weight: 600;
  color: var(--text-primary);
  position: sticky;
  top: 0;
  z-index: 10;
  cursor: pointer;
  user-select: none;
}

.proxy-table th:hover {
  background: var(--gray-300);
}

.proxy-table tbody tr:hover {
  background: var(--bg-secondary);
}

.proxy-table tbody tr.selected {
  background: rgba(0, 122, 204, 0.1);
}

/* 列宽设置 */
.select-column,
.select-cell {
  width: 40px;
  text-align: center;
}

.status-column,
.status-cell {
  width: 60px;
  text-align: center;
}

.address-column,
.address-cell {
  width: 150px;
  min-width: 120px;
}

.type-column,
.type-cell {
  width: 80px;
  text-align: center;
}

.location-column,
.location-cell {
  width: 120px;
  min-width: 100px;
}

.anonymity-column,
.anonymity-cell {
  width: 80px;
  text-align: center;
}

.speed-column,
.speed-cell {
  width: 80px;
  text-align: center;
}

.uptime-column,
.uptime-cell {
  width: 80px;
  text-align: center;
}

.source-column,
.source-cell {
  width: 80px;
  min-width: 70px;
  text-align: center;
}

.checked-column,
.checked-cell {
  width: 140px;
  min-width: 120px;
}

.actions-column,
.actions-cell {
  width: 60px;
  text-align: center;
}

/* 单元格内容样式 */
.checkbox-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px;
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-fast);
}

.checkbox-btn:hover {
  background-color: var(--gray-200);
}

.checkbox-icon {
  font-size: var(--font-size-base);
  color: var(--text-muted);
}

.checkbox-icon.checked {
  color: var(--primary-color);
}

.status-icon {
  font-size: var(--font-size-lg);
}

.status-icon.working {
  color: var(--success-color);
}

.status-icon.failed {
  color: var(--danger-color);
}

.status-icon.unknown {
  color: var(--text-muted);
}

.proxy-address {
  font-family: monospace;
  font-size: var(--font-size-xs);
  color: var(--text-primary);
  word-break: break-all;
}

.proxy-type {
  padding: 2px 6px;
  background: var(--primary-color);
  color: white;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.location-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.country,
.region,
.city {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.country {
  font-weight: 500;
  color: var(--text-primary);
}

.anonymity-badge {
  padding: 2px 6px;
  color: white;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
}

.speed-value,
.uptime-value {
  font-family: monospace;
  font-size: var(--font-size-xs);
  color: var(--text-primary);
}

.speed-value.working {
  color: var(--success-color);
  font-weight: 600;
}

.speed-value.failed {
  color: var(--danger-color);
  font-weight: 600;
}

.speed-value.unknown {
  color: var(--text-secondary);
  font-style: italic;
}

.source-name {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  background: var(--bg-secondary);
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
  font-weight: 500;
  cursor: help;
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.checked-time {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

/* 加载和空状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  gap: var(--spacing-md);
  color: var(--text-secondary);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  gap: var(--spacing-md);
  color: var(--text-secondary);
  text-align: center;
}

.empty-icon {
  font-size: 3rem;
  color: var(--text-muted);
  opacity: 0.5;
}

.empty-container h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  color: var(--text-primary);
}

.empty-container p {
  margin: 0;
  font-size: var(--font-size-sm);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .proxy-manager-dialog {
    width: 98vw;
    height: 95vh;
  }
  
  .proxy-table {
    min-width: 1000px;
  }
  
  .location-column,
  .location-cell,
  .source-column,
  .source-cell {
    display: none;
  }
}

@media (max-width: 768px) {
  .proxy-toolbar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .toolbar-right {
    justify-content: center;
  }
  
  .proxy-table {
    min-width: 800px;
  }
  
  .uptime-column,
  .uptime-cell,
  .checked-column,
  .checked-cell {
    display: none;
  }
}

/* 暗色主题适配 */
[data-theme="dark"] .proxy-manager-dialog {
  background: var(--bg-primary);
  border-color: var(--gray-600);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 10px 20px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .dialog-header {
  background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
  border-bottom-color: var(--gray-600);
}

[data-theme="dark"] .dialog-close:hover {
  background-color: var(--gray-700);
}

[data-theme="dark"] .proxy-toolbar {
  border-bottom-color: var(--gray-600);
}

[data-theme="dark"] .filter-select {
  background: var(--bg-secondary);
  border-color: var(--gray-600);
  color: var(--text-primary);
}

[data-theme="dark"] .proxy-table-container {
  border-color: var(--gray-600);
  background: var(--bg-primary);
}

[data-theme="dark"] .proxy-table th {
  background: var(--bg-secondary);
  border-bottom-color: var(--gray-600);
}

[data-theme="dark"] .proxy-table th:hover {
  background: var(--gray-700);
}

[data-theme="dark"] .proxy-table td {
  border-bottom-color: var(--gray-600);
}

[data-theme="dark"] .proxy-table tbody tr:hover {
  background: var(--gray-800);
}

[data-theme="dark"] .proxy-table tbody tr.selected {
  background: rgba(0, 122, 204, 0.15);
}

[data-theme="dark"] .checkbox-btn:hover {
  background-color: var(--gray-700);
}

[data-theme="dark"] .test-progress {
  background: var(--gray-800);
}

[data-theme="dark"] .loading-spinner {
  border-color: var(--gray-600);
  border-top-color: var(--primary-color);
}

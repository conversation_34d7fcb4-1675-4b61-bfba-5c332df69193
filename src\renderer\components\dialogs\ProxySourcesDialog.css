.proxy-sources-dialog {
  width: 90vw;
  max-width: 1000px;
  height: 80vh;
  max-height: 700px;
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.dialog-icon {
  font-size: var(--font-size-2xl);
  color: var(--primary-color);
}

.dialog-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.dialog-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0;
}

.dialog-close {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
}

.dialog-close:hover {
  background-color: var(--gray-200);
  color: var(--text-primary);
}

.dialog-body {
  flex: 1;
  padding: var(--spacing-lg);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.sources-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.toolbar-left h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.toolbar-right {
  display: flex;
  gap: var(--spacing-sm);
}

.sources-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.source-item {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  transition: all var(--transition-fast);
}

.source-item.enabled {
  border-color: var(--success-color);
  background: rgba(40, 167, 69, 0.05);
}

.source-item.disabled {
  opacity: 0.6;
  background: var(--gray-100);
}

.source-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.toggle-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-btn:hover {
  background-color: var(--gray-200);
}

.toggle-icon {
  font-size: var(--font-size-lg);
}

.toggle-icon.enabled {
  color: var(--success-color);
}

.toggle-icon.disabled {
  color: var(--text-muted);
}

.source-name {
  flex: 1;
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  font-weight: 500;
  background: var(--bg-primary);
  color: var(--text-primary);
}

.source-config {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.config-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.config-row label {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-secondary);
  min-width: 40px;
}

.source-url {
  flex: 1;
  min-width: 300px;
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  font-family: monospace;
  background: var(--bg-primary);
  color: var(--text-primary);
}

.source-format,
.source-parser {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
  min-width: 120px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  gap: var(--spacing-md);
  color: var(--text-secondary);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .proxy-sources-dialog {
    width: 95vw;
    height: 90vh;
  }
  
  .config-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .config-row label {
    min-width: auto;
  }
  
  .source-url {
    min-width: auto;
  }
  
  .toolbar-right {
    flex-direction: column;
  }
}

/* 暗色主题适配 */
[data-theme="dark"] .proxy-sources-dialog {
  background: var(--bg-primary);
  border-color: var(--gray-600);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 10px 20px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .dialog-header {
  background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
  border-bottom-color: var(--gray-600);
}

[data-theme="dark"] .dialog-close:hover {
  background-color: var(--gray-700);
}

[data-theme="dark"] .sources-toolbar {
  border-bottom-color: var(--gray-600);
}

[data-theme="dark"] .source-item {
  background: var(--bg-secondary);
  border-color: var(--gray-600);
}

[data-theme="dark"] .source-item.enabled {
  background: rgba(40, 167, 69, 0.1);
}

[data-theme="dark"] .source-item.disabled {
  background: var(--gray-800);
}

[data-theme="dark"] .toggle-btn:hover {
  background-color: var(--gray-700);
}

[data-theme="dark"] .source-name,
[data-theme="dark"] .source-url,
[data-theme="dark"] .source-format,
[data-theme="dark"] .source-parser {
  background: var(--bg-primary);
  border-color: var(--gray-600);
  color: var(--text-primary);
}

[data-theme="dark"] .dialog-footer {
  background: var(--bg-secondary);
  border-top-color: var(--gray-600);
}

[data-theme="dark"] .loading-spinner {
  border-color: var(--gray-600);
  border-top-color: var(--primary-color);
}

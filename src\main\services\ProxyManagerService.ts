import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import { app } from 'electron';
import axios from 'axios';
import { HttpsProxyAgent } from 'https-proxy-agent';
import { SocksProxyAgent } from 'socks-proxy-agent';

export interface ProxyInfo {
  id: string;
  host: string;
  port: number;
  type: 'http' | 'https' | 'socks4' | 'socks5';
  username?: string;
  password?: string;
  country?: string;
  region?: string;
  city?: string;
  isp?: string;
  anonymity?: 'transparent' | 'anonymous' | 'elite';
  speed?: number; // ms
  uptime?: number; // percentage
  lastChecked?: Date;
  isWorking?: boolean;
  source: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProxySource {
  name: string;
  url: string;
  format: 'json' | 'txt' | 'csv';
  parser: string;
  enabled: boolean;
}

export interface ProxyConfig {
  proxies: ProxyInfo[];
  sources: ProxySource[];
  settings: {
    testTimeout: number;
    testUrl: string;
    autoRefresh: boolean;
    refreshInterval: number;
    maxConcurrentTests: number;
  };
}

export class ProxyManagerService {
  private readonly configDir: string;
  private readonly configPath: string;
  private readonly encryptionKey: string;

  // 2025年6月GitHub最新最快的免费HTTP代理源 (基于GitHub搜索结果)
  private readonly defaultSources: ProxySource[] = [
    {
      name: 'TheSpeedX (4.4k⭐ 每日更新)',
      url: 'https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt',
      format: 'txt',
      parser: 'simple',
      enabled: true
    },
    {
      name: 'ErcinDedeoglu (每日更新+测试)',
      url: 'https://raw.githubusercontent.com/ErcinDedeoglu/proxies/main/proxies/http.txt',
      format: 'txt',
      parser: 'simple',
      enabled: true
    },
    {
      name: 'Monosans (每小时更新)',
      url: 'https://raw.githubusercontent.com/monosans/proxy-list/main/proxies/http.txt',
      format: 'txt',
      parser: 'simple',
      enabled: true
    },
    {
      name: 'Clarketm (每日更新)',
      url: 'https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt',
      format: 'txt',
      parser: 'simple',
      enabled: true
    },
    {
      name: 'Anonym0usWork1221 (每2小时更新)',
      url: 'https://raw.githubusercontent.com/Anonym0usWork1221/Free-Proxies/main/proxy_files/http_proxies.txt',
      format: 'txt',
      parser: 'simple',
      enabled: true
    },
    {
      name: 'ProxyScrape API (高速)',
      url: 'https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=2000&country=all&ssl=all&anonymity=elite,anonymous&format=textplain',
      format: 'txt',
      parser: 'simple',
      enabled: true
    }
  ];

  constructor() {
    this.configDir = path.join(app.getPath('userData'), 'proxy-config');
    this.configPath = path.join(this.configDir, 'proxy-manager.json');
    this.encryptionKey = this.getOrCreateEncryptionKey();
    
    // 确保配置目录存在
    if (!fs.existsSync(this.configDir)) {
      fs.mkdirSync(this.configDir, { recursive: true });
    }
  }

  /**
   * 获取代理配置
   */
  async getProxyConfig(): Promise<ProxyConfig> {
    try {
      if (!fs.existsSync(this.configPath)) {
        return this.createDefaultConfig();
      }

      const encryptedContent = fs.readFileSync(this.configPath, 'utf-8');
      const decryptedContent = this.decrypt(encryptedContent);
      const config = JSON.parse(decryptedContent);
      
      return this.validateAndFixConfig(config);
    } catch (error) {
      console.error('获取代理配置失败:', error);
      return this.createDefaultConfig();
    }
  }

  /**
   * 保存代理配置
   */
  async saveProxyConfig(config: ProxyConfig): Promise<void> {
    try {
      // 更新时间戳
      config.proxies.forEach(proxy => {
        proxy.updatedAt = new Date();
      });

      const configJson = JSON.stringify(config, null, 2);
      const encryptedContent = this.encrypt(configJson);
      fs.writeFileSync(this.configPath, encryptedContent, 'utf-8');
      
      console.log('代理配置已保存');
    } catch (error) {
      console.error('保存代理配置失败:', error);
      throw new Error(`保存代理配置失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 从所有源拉取代理 - 增量添加，不覆盖现有代理
   */
  async fetchProxiesFromAllSources(): Promise<ProxyInfo[]> {
    try {
      const config = await this.getProxyConfig();
      const enabledSources = config.sources.filter(source => source.enabled);
      const maxProxiesPerSource = 30;

      console.log(`开始并发拉取 ${enabledSources.length} 个源的高质量代理 (每源最多${maxProxiesPerSource}个)...`);

      // 获取现有代理列表
      const existingProxies = config.proxies || [];
      console.log(`当前已有 ${existingProxies.length} 个代理`);

      // 并发拉取所有代理源
      const sourcePromises = enabledSources.map(async (source) => {
        try {
          console.log(`正在从 ${source.name} 拉取代理...`);
          const rawProxies = await this.fetchProxiesFromSource(source);
          const qualityProxies = this.selectBestProxies(rawProxies, maxProxiesPerSource);
          console.log(`从 ${source.name} 获取到 ${qualityProxies.length} 个优质代理`);
          return qualityProxies;
        } catch (error) {
          console.error(`从 ${source.name} 拉取代理失败:`, error);
          return [];
        }
      });

      // 等待所有源完成拉取
      const allSourceResults = await Promise.all(sourcePromises);
      const newProxies = allSourceResults.flat();

      // 合并现有代理和新代理，然后去重
      const allProxies = [...existingProxies, ...newProxies];
      const uniqueProxies = this.deduplicateProxies(allProxies);

      console.log(`新拉取 ${newProxies.length} 个代理，合并后共 ${allProxies.length} 个，去重后 ${uniqueProxies.length} 个`);

      // 按质量排序
      const sortedProxies = this.sortProxiesByQuality(uniqueProxies);

      // 跳过自动测试，提高拉取速度
      console.log(`⚡ 跳过自动测试，直接保存代理列表以提高速度`);
      console.log(`💡 用户可以手动选择代理进行测试`);

      // 保存到配置
      config.proxies = sortedProxies;
      await this.saveProxyConfig(config);

      const workingCount = sortedProxies.filter(p => p.isWorking === true).length;
      const unknownCount = sortedProxies.filter(p => p.isWorking === undefined).length;
      const newCount = newProxies.length;

      console.log(`拉取完成! 新增 ${newCount} 个代理，总计 ${sortedProxies.length} 个 (已验证可用: ${workingCount}, 待测试: ${unknownCount})`);

      return sortedProxies;
    } catch (error) {
      console.error('拉取代理失败:', error);
      throw new Error(`拉取代理失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 从单个源拉取代理
   */
  private async fetchProxiesFromSource(source: ProxySource): Promise<ProxyInfo[]> {
    try {
      // 随机选择User-Agent，避免被识别为爬虫
      const userAgents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
      ];

      const randomUserAgent = userAgents[Math.floor(Math.random() * userAgents.length)];

      const response = await axios.get(source.url, {
        timeout: 8000, // 进一步减少超时时间到8秒
        headers: {
          'User-Agent': randomUserAgent,
          'Accept': '*/*',
          'Accept-Language': 'en-US,en;q=0.9',
          'Accept-Encoding': 'gzip, deflate',
          'Connection': 'close', // 使用短连接，提高速度
          'Cache-Control': 'no-cache'
        },
        validateStatus: (status) => status < 500,
      });

      if (response.status !== 200) {
        console.warn(`从 ${source.name} 获取到非200状态码: ${response.status}`);
        return [];
      }

      return this.parseProxyData(response.data, source);
    } catch (error: any) {
      if (error.response?.status === 403) {
        console.warn(`从 ${source.name} 拉取代理被拒绝 (403 Forbidden)，可能被反爬虫保护`);
      } else if (error.response?.status === 429) {
        console.warn(`从 ${source.name} 拉取代理被限流 (429 Too Many Requests)`);
      } else {
        console.error(`从 ${source.name} 拉取代理失败:`, error.message);
      }
      return [];
    }
  }

  /**
   * 解析代理数据
   */
  private parseProxyData(data: any, source: ProxySource): ProxyInfo[] {
    const proxies: ProxyInfo[] = [];

    try {
      switch (source.parser) {
        case 'geonode':
          if (data.data && Array.isArray(data.data)) {
            data.data.forEach((item: any) => {
              proxies.push({
                id: this.generateProxyId(),
                host: item.ip,
                port: parseInt(item.port),
                type: item.protocols?.[0] || 'http',
                country: item.country,
                region: item.region,
                city: item.city,
                isp: item.org,
                anonymity: item.anonymityLevel,
                uptime: item.upTime,
                source: source.name,
                createdAt: new Date(),
                updatedAt: new Date()
              });
            });
          }
          break;

        case 'simple':
          this.parseSimpleFormat(data, 'http', source.name, proxies);
          break;

        case 'freeproxyworld':
          if (data.data && Array.isArray(data.data)) {
            data.data.forEach((item: any) => {
              proxies.push({
                id: this.generateProxyId(),
                host: item.ip,
                port: parseInt(item.port),
                type: item.type?.toLowerCase() || 'http',
                country: item.country,
                anonymity: item.anonymity?.toLowerCase(),
                source: source.name,
                createdAt: new Date(),
                updatedAt: new Date()
              });
            });
          }
          break;

        case 'proxy11':
          if (data.data && Array.isArray(data.data)) {
            data.data.forEach((item: any) => {
              proxies.push({
                id: this.generateProxyId(),
                host: item.ip,
                port: parseInt(item.port),
                type: item.type?.toLowerCase() || 'http',
                country: item.country_code,
                region: item.region,
                city: item.city,
                anonymity: item.anonymity_level?.toLowerCase(),
                speed: item.response_time,
                uptime: item.uptime_percentage,
                source: source.name,
                createdAt: new Date(),
                updatedAt: new Date()
              });
            });
          }
          break;

        case 'proxifly':
          if (data.data && Array.isArray(data.data)) {
            data.data.forEach((item: any) => {
              // Proxifly提供高质量代理，通常已经过测试
              proxies.push({
                id: this.generateProxyId(),
                host: item.ip,
                port: parseInt(item.port),
                type: item.protocol?.toLowerCase() || 'http',
                country: item.country,
                region: item.region,
                city: item.city,
                anonymity: item.anonymity?.toLowerCase(),
                speed: item.response_time,
                uptime: item.uptime,
                isWorking: item.working === true, // Proxifly已测试过
                source: source.name,
                createdAt: new Date(),
                updatedAt: new Date()
              });
            });
          }
          break;

        default:
          console.warn(`未知的解析器类型: ${source.parser}`);
          break;
      }
    } catch (error) {
      console.error(`解析代理数据失败:`, error);
    }

    return proxies;
  }

  /**
   * 解析简单格式的代理数据 (host:port)
   */
  private parseSimpleFormat(data: string, type: string, sourceName: string, proxies: ProxyInfo[]): void {
    const lines = data.split('\n').filter((line: string) => line.trim());
    lines.forEach((line: string) => {
      const [host, port] = line.trim().split(':');
      if (host && port && !isNaN(parseInt(port))) {
        proxies.push({
          id: this.generateProxyId(),
          host: host.trim(),
          port: parseInt(port),
          type: type as any,
          source: sourceName,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
    });
  }

  /**
   * 测试HTTP代理 - 优化版本，快速简单可靠
   */
  async testProxy(proxy: ProxyInfo, testUrl: string = 'http://httpbin.org/ip', timeout: number = 5000): Promise<boolean> {
    const startTime = Date.now();

    try {
      // 只测试HTTP代理，跳过SOCKS代理
      if (proxy.type === 'socks4' || proxy.type === 'socks5') {
        proxy.isWorking = false;
        proxy.lastChecked = new Date();
        console.debug(`⏭️ 跳过SOCKS代理 ${proxy.host}:${proxy.port}`);
        return false;
      }

      // 使用简单可靠的测试URL，避免被阻塞
      const testUrls = [
        'http://httpbin.org/ip',
        'http://icanhazip.com',
        'http://checkip.amazonaws.com'
      ];

      // 只测试第一个URL，避免过度延迟
      const targetUrl = testUrls[0];

      // 构建HTTP代理URL
      const proxyUrl = `http://${proxy.host}:${proxy.port}`;
      const agent = new HttpsProxyAgent(proxyUrl);

      // 简化的测试请求
      const response = await axios.get(targetUrl, {
        httpsAgent: agent,
        httpAgent: agent,
        timeout: timeout,
        validateStatus: (status) => status >= 200 && status < 300,
        headers: {
          'User-Agent': 'curl/7.68.0',
          'Accept': '*/*'
        },
        maxRedirects: 0 // 禁用重定向，提高速度
      });

      // 检查响应
      if (response.status === 200 && response.data) {
        const endTime = Date.now();
        proxy.speed = endTime - startTime;
        proxy.isWorking = true;
        proxy.lastChecked = new Date();
        console.debug(`✅ 代理 ${proxy.host}:${proxy.port} 可用，速度: ${proxy.speed}ms`);
        return true;
      }

      // 响应异常
      const endTime = Date.now();
      proxy.speed = undefined;
      proxy.isWorking = false;
      proxy.lastChecked = new Date();
      return false;

    } catch (error: any) {
      const endTime = Date.now();
      proxy.speed = undefined;
      proxy.isWorking = false;
      proxy.lastChecked = new Date();

      // 简化错误日志
      if (error.code === 'ECONNREFUSED') {
        console.debug(`❌ 代理 ${proxy.host}:${proxy.port} 连接被拒绝`);
      } else if (error.code === 'ETIMEDOUT') {
        console.debug(`⏰ 代理 ${proxy.host}:${proxy.port} 连接超时`);
      } else if (error.response?.status === 407) {
        console.debug(`🔐 代理 ${proxy.host}:${proxy.port} 需要认证`);
      }

      return false;
    }
  }



  /**
   * 批量测试代理 - 优化版本，控制并发避免卡顿
   */
  async testProxiesBatch(proxies: ProxyInfo[], maxConcurrent: number = 10, progressCallback?: (progress: any) => void): Promise<ProxyInfo[]> {
    const timeout = 5000; // 5秒超时，更宽松
    const maxSpeed = 8000; // 最大允许速度8秒

    console.log(`🚀 开始批量测试 ${proxies.length} 个代理，并发数: ${maxConcurrent}`);

    let completedCount = 0;
    const startTime = Date.now();
    const results: ProxyInfo[] = [];

    // 使用信号量控制并发数，避免网络拥塞
    const semaphore = new Array(maxConcurrent).fill(null);
    let semaphoreIndex = 0;

    const testWithSemaphore = async (proxy: ProxyInfo): Promise<ProxyInfo> => {
      // 等待信号量
      const currentIndex = semaphoreIndex % maxConcurrent;
      semaphoreIndex++;

      try {
        const isWorking = await this.testProxy(proxy, 'http://httpbin.org/ip', timeout);

        completedCount++;

        // 实时更新进度
        if (progressCallback) {
          progressCallback({
            completed: completedCount,
            total: proxies.length,
            current: proxy,
            elapsed: ((Date.now() - startTime) / 1000).toFixed(1)
          });
        }

        if (completedCount % 10 === 0) {
          const elapsed = ((Date.now() - startTime) / 1000).toFixed(1);
          console.log(`⚡ 测试进度: ${completedCount}/${proxies.length} (${elapsed}s)`);
        }

        return proxy;
      } catch (error) {
        completedCount++;
        proxy.isWorking = false;
        proxy.lastChecked = new Date();
        return proxy;
      }
    };

    // 分批处理，避免同时发起太多请求
    const batchSize = maxConcurrent;
    for (let i = 0; i < proxies.length; i += batchSize) {
      const batch = proxies.slice(i, i + batchSize);
      console.log(`测试批次 ${Math.floor(i / batchSize) + 1}/${Math.ceil(proxies.length / batchSize)}: ${batch.length} 个代理`);

      const batchPromises = batch.map(proxy => testWithSemaphore(proxy));
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // 批次间短暂延迟，避免网络拥塞
      if (i + batchSize < proxies.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(1);

    // 自动清理：只保留可用且速度快的代理
    const cleanedResults = results.filter(proxy => {
      // 删除连接失败的代理
      if (proxy.isWorking === false) {
        console.log(`🗑️ 删除失败代理: ${proxy.host}:${proxy.port}`);
        return false;
      }

      // 删除没有速度数据的代理
      if (!proxy.speed) {
        console.log(`🗑️ 删除无速度数据代理: ${proxy.host}:${proxy.port}`);
        return false;
      }

      // 删除速度太慢的代理
      if (proxy.speed > maxSpeed) {
        console.log(`🗑️ 删除慢速代理: ${proxy.host}:${proxy.port} (${proxy.speed}ms)`);
        return false;
      }

      return true;
    });

    // 按速度排序，最快的在前面
    cleanedResults.sort((a, b) => (a.speed || 999999) - (b.speed || 999999));

    // 更新配置中的代理列表
    const proxyConfig = await this.getProxyConfig();
    const updatedProxies = proxyConfig.proxies.map(existingProxy => {
      const testResult = cleanedResults.find(r =>
        r.host === existingProxy.host && r.port === existingProxy.port
      );
      return testResult;
    }).filter(Boolean) as ProxyInfo[];

    proxyConfig.proxies = updatedProxies;
    await this.saveProxyConfig(proxyConfig);

    const originalCount = results.length;
    const workingCount = cleanedResults.length;
    const deletedCount = originalCount - workingCount;
    const avgSpeed = cleanedResults.length > 0 ?
      cleanedResults.reduce((sum, p) => sum + (p.speed || 0), 0) / cleanedResults.length : 0;

    console.log(`🎉 批量测试完成! 用时${duration}秒`);
    console.log(`📊 原始: ${originalCount}, 可用: ${workingCount}, 已清理: ${deletedCount}`);
    console.log(`⚡ 平均速度: ${avgSpeed?.toFixed(0)}ms`);

    return cleanedResults;
  }

  /**
   * 删除代理
   */
  async deleteProxies(proxyIds: string[]): Promise<void> {
    try {
      const config = await this.getProxyConfig();
      config.proxies = config.proxies.filter(p => !proxyIds.includes(p.id));
      await this.saveProxyConfig(config);
      console.log(`已删除 ${proxyIds.length} 个代理`);
    } catch (error) {
      console.error('删除代理失败:', error);
      throw new Error(`删除代理失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 构建代理URL
   */
  private buildProxyUrl(proxy: ProxyInfo): string {
    const auth = proxy.username && proxy.password ? `${proxy.username}:${proxy.password}@` : '';
    return `${proxy.type}://${auth}${proxy.host}:${proxy.port}`;
  }

  /**
   * 去重代理 - 优化版本，保留质量更好的代理
   */
  private deduplicateProxies(proxies: ProxyInfo[]): ProxyInfo[] {
    const proxyMap = new Map<string, ProxyInfo>();

    proxies.forEach(proxy => {
      const key = `${proxy.host}:${proxy.port}`;
      const existing = proxyMap.get(key);

      if (!existing) {
        // 如果不存在，直接添加
        proxyMap.set(key, proxy);
      } else {
        // 如果已存在，保留质量更好的代理
        const newScore = this.calculateProxyScore(proxy);
        const existingScore = this.calculateProxyScore(existing);

        if (newScore > existingScore) {
          proxyMap.set(key, proxy);
        }
      }
    });

    const uniqueProxies = Array.from(proxyMap.values());
    console.log(`去重完成: ${proxies.length} -> ${uniqueProxies.length} 个代理`);

    return uniqueProxies;
  }

  /**
   * 过滤高质量代理
   */
  private filterHighQualityProxies(proxies: ProxyInfo[]): ProxyInfo[] {
    return proxies.filter(proxy => {
      // 过滤掉明显无效的IP
      if (!this.isValidIP(proxy.host)) {
        return false;
      }

      // 过滤掉无效端口
      if (proxy.port < 1 || proxy.port > 65535) {
        return false;
      }

      // 过滤掉常见的无效端口
      const invalidPorts = [22, 23, 25, 53, 110, 143, 993, 995];
      if (invalidPorts.includes(proxy.port)) {
        return false;
      }

      // 优先保留已测试可用的代理
      if (proxy.isWorking === true) {
        return true;
      }

      // 过滤掉已知不可用的代理
      if (proxy.isWorking === false) {
        return false;
      }

      // 优先保留高匿名性代理
      if (proxy.anonymity === 'elite' || proxy.anonymity === 'anonymous') {
        return true;
      }

      // 过滤掉透明代理（安全性低）
      if (proxy.anonymity === 'transparent') {
        return false;
      }

      // 优先保留速度快的代理
      if (proxy.speed && proxy.speed < 5000) { // 5秒以内
        return true;
      }

      // 优先保留高在线率的代理
      if (proxy.uptime && proxy.uptime > 80) { // 80%以上在线率
        return true;
      }

      // 来自可靠源的代理
      const reliableSources = ['Proxifly', 'ProxyList.geonode.com'];
      if (reliableSources.some(source => proxy.source.includes(source))) {
        return true;
      }

      // 默认保留（但会在后续测试中验证）
      return true;
    });
  }

  /**
   * 验证IP地址格式
   */
  private isValidIP(ip: string): boolean {
    // IPv4格式验证
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

    if (ipv4Regex.test(ip)) {
      // 过滤掉私有IP和保留IP
      const parts = ip.split('.').map(Number);

      // 私有IP范围
      if (parts[0] === 10) return false; // 10.0.0.0/8
      if (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31) return false; // **********/12
      if (parts[0] === 192 && parts[1] === 168) return false; // ***********/16

      // 保留IP范围
      if (parts[0] === 127) return false; // *********/8 (localhost)
      if (parts[0] === 0) return false; // 0.0.0.0/8
      if (parts[0] >= 224) return false; // *********/4 (multicast and reserved)

      return true;
    }

    return false;
  }

  /**
   * 数组分块
   */
  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * 生成代理ID
   */
  private generateProxyId(): string {
    return 'proxy_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 创建默认配置
   */
  private createDefaultConfig(): ProxyConfig {
    return {
      proxies: [],
      sources: this.defaultSources,
      settings: {
        testTimeout: 10000,
        testUrl: 'https://httpbin.org/ip',
        autoRefresh: false,
        refreshInterval: 3600000, // 1小时
        maxConcurrentTests: 10
      }
    };
  }

  /**
   * 验证并修复配置
   */
  private validateAndFixConfig(config: any): ProxyConfig {
    const validatedConfig: ProxyConfig = {
      proxies: [],
      sources: config.sources || this.defaultSources,
      settings: {
        testTimeout: config.settings?.testTimeout || 10000,
        testUrl: config.settings?.testUrl || 'https://httpbin.org/ip',
        autoRefresh: config.settings?.autoRefresh || false,
        refreshInterval: config.settings?.refreshInterval || 3600000,
        maxConcurrentTests: config.settings?.maxConcurrentTests || 10
      }
    };

    if (Array.isArray(config.proxies)) {
      validatedConfig.proxies = config.proxies.map((proxy: any) => ({
        id: proxy.id || this.generateProxyId(),
        host: proxy.host || '',
        port: proxy.port || 0,
        type: proxy.type || 'http',
        username: proxy.username,
        password: proxy.password,
        country: proxy.country,
        region: proxy.region,
        city: proxy.city,
        isp: proxy.isp,
        anonymity: proxy.anonymity,
        speed: proxy.speed,
        uptime: proxy.uptime,
        lastChecked: proxy.lastChecked ? new Date(proxy.lastChecked) : undefined,
        isWorking: proxy.isWorking,
        source: proxy.source || '未知',
        createdAt: proxy.createdAt ? new Date(proxy.createdAt) : new Date(),
        updatedAt: proxy.updatedAt ? new Date(proxy.updatedAt) : new Date()
      }));
    }

    return validatedConfig;
  }

  /**
   * 获取或创建加密密钥
   */
  private getOrCreateEncryptionKey(): string {
    const keyPath = path.join(this.configDir, '.proxy-key');
    
    if (fs.existsSync(keyPath)) {
      return fs.readFileSync(keyPath, 'utf-8');
    }
    
    // 确保配置目录存在
    if (!fs.existsSync(this.configDir)) {
      fs.mkdirSync(this.configDir, { recursive: true });
    }
    
    const key = crypto.randomBytes(32).toString('hex');
    fs.writeFileSync(keyPath, key, 'utf-8');
    
    return key;
  }

  /**
   * 加密数据
   */
  private encrypt(text: string): string {
    const iv = crypto.randomBytes(16);
    const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
    const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return iv.toString('hex') + ':' + encrypted;
  }

  /**
   * 解密数据
   */
  private decrypt(text: string): string {
    const parts = text.split(':');
    const iv = Buffer.from(parts.shift()!, 'hex');
    const encryptedText = parts.join(':');
    const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
    const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
    let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }

  /**
   * 从代理列表中选择最佳的N个代理
   */
  private selectBestProxies(proxies: ProxyInfo[], maxCount: number): ProxyInfo[] {
    // 先进行基础质量过滤
    const filteredProxies = this.filterHighQualityProxies(proxies);

    // 按质量评分排序
    const scoredProxies = filteredProxies.map(proxy => ({
      proxy,
      score: this.calculateProxyScore(proxy)
    }));

    // 按评分降序排序，取前N个
    scoredProxies.sort((a, b) => b.score - a.score);

    return scoredProxies.slice(0, maxCount).map(item => item.proxy);
  }

  /**
   * 计算代理质量评分
   */
  private calculateProxyScore(proxy: ProxyInfo): number {
    let score = 0;

    // 已知可用的代理得高分
    if (proxy.isWorking === true) {
      score += 100;
    }

    // 匿名性评分
    switch (proxy.anonymity) {
      case 'elite':
        score += 50;
        break;
      case 'anonymous':
        score += 30;
        break;
      case 'transparent':
        score -= 20;
        break;
    }

    // 速度评分 (速度越快分数越高)
    if (proxy.speed) {
      if (proxy.speed < 1000) score += 40;
      else if (proxy.speed < 3000) score += 30;
      else if (proxy.speed < 5000) score += 20;
      else if (proxy.speed < 10000) score += 10;
    }

    // 在线率评分
    if (proxy.uptime) {
      if (proxy.uptime > 95) score += 30;
      else if (proxy.uptime > 90) score += 25;
      else if (proxy.uptime > 80) score += 20;
      else if (proxy.uptime > 70) score += 15;
      else if (proxy.uptime > 60) score += 10;
    }

    // 代理类型评分
    switch (proxy.type) {
      case 'http':
      case 'https':
        score += 20;
        break;
      case 'socks5':
        score += 25;
        break;
      case 'socks4':
        score += 15;
        break;
    }

    // 来源可靠性评分
    const reliableSources = [
      'Proxifly', 'ProxyList.geonode.com', 'GitHub TheSpeedX',
      'ProxyScrape', 'GitHub Monosans'
    ];
    if (reliableSources.some(source => proxy.source.includes(source))) {
      score += 15;
    }

    // 地理位置评分 (优先一些稳定的国家/地区)
    const preferredCountries = ['US', 'DE', 'NL', 'SG', 'JP', 'UK', 'CA', 'FR'];
    if (proxy.country && preferredCountries.includes(proxy.country.toUpperCase())) {
      score += 10;
    }

    return score;
  }

  /**
   * 快速测试代理可用性 (小批量并发测试)
   */
  private async quickTestProxies(proxies: ProxyInfo[]): Promise<ProxyInfo[]> {
    if (proxies.length === 0) return [];

    console.log(`快速测试 ${proxies.length} 个代理的可用性...`);

    const results: ProxyInfo[] = [];
    const batchSize = 5; // 小批量测试，避免过载

    for (let i = 0; i < proxies.length; i += batchSize) {
      const batch = proxies.slice(i, i + batchSize);

      const batchPromises = batch.map(async (proxy) => {
        try {
          // 使用更短的超时时间进行快速测试 (3秒)
          const isWorking = await this.testProxy(proxy, 'http://httpbin.org/ip', 3000);
          return { ...proxy, isWorking, lastChecked: new Date() };
        } catch (error) {
          return { ...proxy, isWorking: false, lastChecked: new Date() };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // 批次间稍微延迟
      if (i + batchSize < proxies.length) {
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    }

    const workingCount = results.filter(p => p.isWorking).length;
    console.log(`快速测试完成: ${workingCount}/${results.length} 个代理可用`);

    return results;
  }

  /**
   * 按质量对代理进行排序
   */
  private sortProxiesByQuality(proxies: ProxyInfo[]): ProxyInfo[] {
    return proxies.sort((a, b) => {
      // 已验证可用的代理排在最前面
      if (a.isWorking === true && b.isWorking !== true) return -1;
      if (b.isWorking === true && a.isWorking !== true) return 1;

      // 已验证不可用的代理排在最后面
      if (a.isWorking === false && b.isWorking !== false) return 1;
      if (b.isWorking === false && a.isWorking !== false) return -1;

      // 其他情况按评分排序
      const scoreA = this.calculateProxyScore(a);
      const scoreB = this.calculateProxyScore(b);
      return scoreB - scoreA;
    });
  }
}

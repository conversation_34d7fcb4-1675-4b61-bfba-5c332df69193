import React, { useState, useEffect } from 'react';
import { 
  IoClose, 
  IoAdd,
  IoTrash,
  IoCheckmarkCircle,
  IoCloseCircle,
  IoRefresh,
  IoGlobe,
  IoSave,
  IoEye,
  IoEyeOff
} from 'react-icons/io5';
import { useNotification } from '../../contexts/NotificationContext';
import Modal from '../Modal';
import './ProxySourcesDialog.css';

interface ProxySource {
  name: string;
  url: string;
  format: 'json' | 'txt' | 'csv';
  parser: string;
  enabled: boolean;
}

interface ProxySourcesDialogProps {
  onClose: () => void;
  onConfirm: (sources: ProxySource[]) => void;
}

const ProxySourcesDialog: React.FC<ProxySourcesDialogProps> = ({ onClose, onConfirm }) => {
  const { showSuccess, showError, showWarning } = useNotification();
  const [sources, setSources] = useState<ProxySource[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // 2025年6月GitHub最新最快的免费HTTP代理源 (基于GitHub搜索结果)
  const latestSources: ProxySource[] = [
    {
      name: 'TheSpeedX (4.4k⭐ 每日更新)',
      url: 'https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt',
      format: 'txt',
      parser: 'simple',
      enabled: true
    },
    {
      name: 'ErcinDedeoglu (每日更新+测试)',
      url: 'https://raw.githubusercontent.com/ErcinDedeoglu/proxies/main/proxies/http.txt',
      format: 'txt',
      parser: 'simple',
      enabled: true
    },
    {
      name: 'Monosans (每小时更新)',
      url: 'https://raw.githubusercontent.com/monosans/proxy-list/main/proxies/http.txt',
      format: 'txt',
      parser: 'simple',
      enabled: true
    },
    {
      name: 'Clarketm (每日更新)',
      url: 'https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt',
      format: 'txt',
      parser: 'simple',
      enabled: true
    },
    {
      name: 'Anonym0usWork1221 (每2小时更新)',
      url: 'https://raw.githubusercontent.com/Anonym0usWork1221/Free-Proxies/main/proxy_files/http_proxies.txt',
      format: 'txt',
      parser: 'simple',
      enabled: true
    },
    {
      name: 'ProxyScrape API (高速)',
      url: 'https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=2000&country=all&ssl=all&anonymity=elite,anonymous&format=textplain',
      format: 'txt',
      parser: 'simple',
      enabled: true
    },
    {
      name: 'Geonode API (按速度排序)',
      url: 'https://proxylist.geonode.com/api/proxy-list?limit=50&page=1&sort_by=speed&sort_type=asc&protocols=http,https&anonymityLevel=elite,anonymous',
      format: 'json',
      parser: 'geonode',
      enabled: true
    },
    {
      name: 'Free Proxy List API',
      url: 'https://www.proxy-list.download/api/v1/get?type=http',
      format: 'txt',
      parser: 'simple',
      enabled: true
    },
    {
      name: 'Zaeem20 (快速)',
      url: 'https://raw.githubusercontent.com/zaeem20/FREE_PROXIES_LIST/master/http.txt',
      format: 'txt',
      parser: 'simple',
      enabled: true
    },
    {
      name: 'Proxifly (验证过的)',
      url: 'https://raw.githubusercontent.com/proxifly/free-proxy-list/main/proxies/http.txt',
      format: 'txt',
      parser: 'simple',
      enabled: true
    }
  ];

  useEffect(() => {
    loadSources();
  }, []);

  const loadSources = async () => {
    try {
      setIsLoading(true);
      const config = await window.electronAPI.proxyManager.getConfig();
      if (config.sources && config.sources.length > 0) {
        setSources(config.sources);
      } else {
        setSources(latestSources);
      }
    } catch (error) {
      console.error('加载代理源失败:', error);
      setSources(latestSources);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddSource = () => {
    const newSource: ProxySource = {
      name: '新代理源',
      url: 'https://',
      format: 'txt',
      parser: 'simple',
      enabled: true
    };
    setSources([...sources, newSource]);
  };

  const handleDeleteSource = (index: number) => {
    setSources(sources.filter((_, i) => i !== index));
  };

  const handleUpdateSource = (index: number, field: keyof ProxySource, value: any) => {
    const updatedSources = [...sources];
    updatedSources[index] = { ...updatedSources[index], [field]: value };
    setSources(updatedSources);
  };

  const handleToggleSource = (index: number) => {
    handleUpdateSource(index, 'enabled', !sources[index].enabled);
  };

  const handleUseLatestSources = () => {
    setSources(latestSources);
    showSuccess('已更新为2025年6月最新代理源');
  };

  const handleConfirm = () => {
    const enabledSources = sources.filter(s => s.enabled);
    if (enabledSources.length === 0) {
      showWarning('请至少启用一个代理源');
      return;
    }

    // 验证URL格式
    const invalidSources = enabledSources.filter(s => !s.url.startsWith('http'));
    if (invalidSources.length > 0) {
      showError('存在无效的URL格式', `请检查: ${invalidSources.map(s => s.name).join(', ')}`);
      return;
    }

    onConfirm(sources);
  };

  const getParserOptions = () => [
    { value: 'simple', label: 'Simple (host:port)' },
    { value: 'geonode', label: 'Geonode JSON' },
    { value: 'proxifly', label: 'Proxifly JSON' },
    { value: 'socks4', label: 'SOCKS4' },
    { value: 'socks5', label: 'SOCKS5' },
    { value: 'freeproxyworld', label: 'FreeProxyWorld JSON' },
    { value: 'proxy11', label: 'Proxy11 JSON' }
  ];

  return (
    <Modal onClose={onClose} className="proxy-sources-dialog">
      <div className="dialog-header">
        <div className="header-left">
          <IoGlobe className="dialog-icon" />
          <div>
            <h2 className="dialog-title">代理源配置</h2>
            <p className="dialog-subtitle">配置和管理代理数据源</p>
          </div>
        </div>
        <button className="dialog-close" onClick={onClose}>
          <IoClose />
        </button>
      </div>

      <div className="dialog-body">
        <div className="sources-toolbar">
          <div className="toolbar-left">
            <h3>代理源列表 ({sources.filter(s => s.enabled).length}/{sources.length})</h3>
          </div>
          <div className="toolbar-right">
            <button
              className="btn btn-success btn-sm"
              onClick={handleUseLatestSources}
            >
              <IoRefresh />
              使用2025年6月最新源
            </button>
            <button
              className="btn btn-primary btn-sm"
              onClick={handleAddSource}
            >
              <IoAdd />
              添加源
            </button>
          </div>
        </div>

        {isLoading ? (
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>正在加载代理源...</p>
          </div>
        ) : (
          <div className="sources-list">
            {sources.map((source, index) => (
              <div key={index} className={`source-item ${source.enabled ? 'enabled' : 'disabled'}`}>
                <div className="source-header">
                  <button
                    className="toggle-btn"
                    onClick={() => handleToggleSource(index)}
                  >
                    {source.enabled ? (
                      <IoEye className="toggle-icon enabled" />
                    ) : (
                      <IoEyeOff className="toggle-icon disabled" />
                    )}
                  </button>
                  
                  <input
                    type="text"
                    className="source-name"
                    value={source.name}
                    onChange={(e) => handleUpdateSource(index, 'name', e.target.value)}
                    placeholder="代理源名称"
                  />
                  
                  <button
                    className="btn btn-danger btn-xs"
                    onClick={() => handleDeleteSource(index)}
                  >
                    <IoTrash />
                  </button>
                </div>
                
                <div className="source-config">
                  <div className="config-row">
                    <label>URL:</label>
                    <input
                      type="url"
                      className="source-url"
                      value={source.url}
                      onChange={(e) => handleUpdateSource(index, 'url', e.target.value)}
                      placeholder="https://api.example.com/proxies"
                    />
                  </div>
                  
                  <div className="config-row">
                    <label>格式:</label>
                    <select
                      className="source-format"
                      value={source.format}
                      onChange={(e) => handleUpdateSource(index, 'format', e.target.value)}
                    >
                      <option value="txt">TXT</option>
                      <option value="json">JSON</option>
                      <option value="csv">CSV</option>
                    </select>
                    
                    <label>解析器:</label>
                    <select
                      className="source-parser"
                      value={source.parser}
                      onChange={(e) => handleUpdateSource(index, 'parser', e.target.value)}
                    >
                      {getParserOptions().map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="dialog-footer">
        <button className="btn btn-outline" onClick={onClose}>
          取消
        </button>
        <button className="btn btn-primary" onClick={handleConfirm}>
          <IoSave />
          确定并拉取
        </button>
      </div>
    </Modal>
  );
};

export default ProxySourcesDialog;

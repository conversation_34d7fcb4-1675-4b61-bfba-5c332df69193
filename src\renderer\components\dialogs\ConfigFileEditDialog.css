.config-file-edit-dialog {
  width: 90vw;
  max-width: 1000px;
  height: 80vh;
  max-height: 800px;

  /* CSS变量定义 */
  --surface-color: #ffffff;
  --background-secondary: #f8f9fa;
  --background-primary: #ffffff;
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --border-color: #e1e5e9;
  --primary-color: #007acc;
  --primary-color-hover: #005a9e;
  --success-color: #28a745;
  --success-color-hover: #218838;
  --warning-color: #ffc107;
}

.config-file-edit-dialog .dialog-body {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0;
}

/* 文件选择器 */
.config-file-edit-dialog .file-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--surface-color);
}

.config-file-edit-dialog .file-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.config-file-edit-dialog .file-name {
  font-weight: 500;
  color: var(--text-primary);
}

.config-file-edit-dialog .unsaved-indicator {
  color: var(--warning-color);
  font-size: 1.2rem;
  line-height: 1;
}

.config-file-edit-dialog .file-actions {
  display: flex;
  gap: 0.5rem;
}

/* 编辑器容器 */
.config-file-edit-dialog .editor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* 编辑器工具栏 */
.config-file-edit-dialog .editor-toolbar {
  display: flex;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--surface-color);
}

/* 钱包列表面板 */
.config-file-edit-dialog .wallet-list-panel {
  border-bottom: 1px solid var(--border-color);
  background: var(--background-secondary);
  max-height: 400px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.config-file-edit-dialog .wallet-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--surface-color);
  position: sticky;
  top: 0;
  z-index: 1;
}

.config-file-edit-dialog .wallet-list-header h4 {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-primary);
  font-weight: 600;
}

.config-file-edit-dialog .wallet-list-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.config-file-edit-dialog .wallet-private-key-list {
  flex: 1;
  overflow-y: auto;
}

/* 私钥列表项 */
.config-file-edit-dialog .private-key-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.config-file-edit-dialog .private-key-item:hover {
  background: var(--surface-color);
}

.config-file-edit-dialog .private-key-item:last-child {
  border-bottom: none;
}

.config-file-edit-dialog .private-key-selector {
  flex-shrink: 0;
}

.config-file-edit-dialog .checkbox-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
}

.config-file-edit-dialog .checkbox-btn:hover {
  background: var(--surface-color);
  color: var(--primary-color);
}

.config-file-edit-dialog .private-key-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.config-file-edit-dialog .wallet-name-chain {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.config-file-edit-dialog .wallet-name {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.config-file-edit-dialog .wallet-chain {
  font-size: 0.7rem;
  color: white;
  background: var(--primary-color);
  padding: 0.125rem 0.375rem;
  border-radius: 3px;
  font-weight: 600;
}

.config-file-edit-dialog .private-key-display {
  width: 100%;
}

.config-file-edit-dialog .private-key-text {
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.8rem;
  color: var(--text-primary);
  word-break: break-all;
  line-height: 1.4;
  background: var(--background-primary);
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  display: block;
  width: 100%;
}

.config-file-edit-dialog .private-key-actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

/* 配置编辑器 */
.config-file-edit-dialog .config-editor {
  flex: 1;
  width: 100%;
  padding: 1rem;
  border: none;
  outline: none;
  resize: none;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  background: var(--background-primary);
  color: var(--text-primary);
  min-height: 300px;
}

.config-file-edit-dialog .config-editor:focus {
  background: var(--surface-color);
}

.config-file-edit-dialog .config-editor::placeholder {
  color: var(--text-secondary);
  font-style: italic;
}

/* 空状态 */
.config-file-edit-dialog .empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.config-file-edit-dialog .empty-icon {
  font-size: 3rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.config-file-edit-dialog .empty-state h3 {
  margin: 0 0 0.5rem;
  color: var(--text-primary);
}

.config-file-edit-dialog .empty-state p {
  margin: 0 0 1.5rem;
  color: var(--text-secondary);
}

/* 暗色主题适配 */
[data-theme="dark"] .config-file-edit-dialog {
  --surface-color: #2a2a2a;
  --background-secondary: #1e1e1e;
}

[data-theme="dark"] .config-file-edit-dialog .file-selector {
  background: #2a2a2a;
  border-bottom-color: #404040;
}

[data-theme="dark"] .config-file-edit-dialog .editor-toolbar {
  background: #2a2a2a;
  border-bottom-color: #404040;
}

[data-theme="dark"] .config-file-edit-dialog .wallet-list-panel {
  background: #1e1e1e;
  border-bottom-color: #404040;
}

[data-theme="dark"] .config-file-edit-dialog .wallet-list-panel h4 {
  border-bottom-color: #404040;
}

[data-theme="dark"] .config-file-edit-dialog .private-key-item {
  border-bottom-color: #404040;
}

[data-theme="dark"] .config-file-edit-dialog .private-key-item:hover {
  background: #2a2a2a;
}

[data-theme="dark"] .config-file-edit-dialog .config-editor {
  background: #1a1a1a;
  color: #e0e0e0;
}

[data-theme="dark"] .config-file-edit-dialog .config-editor:focus {
  background: #2a2a2a;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .config-file-edit-dialog {
    width: 95vw;
    height: 90vh;
  }

  .config-file-edit-dialog .file-selector {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .config-file-edit-dialog .file-actions {
    justify-content: center;
  }

  .config-file-edit-dialog .wallet-list-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }

  .config-file-edit-dialog .wallet-list-actions {
    justify-content: center;
  }

  .config-file-edit-dialog .private-key-item {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .config-file-edit-dialog .private-key-actions {
    justify-content: center;
  }

  .config-file-edit-dialog .private-key-text {
    font-size: 0.7rem;
  }

  .config-file-edit-dialog .config-editor {
    font-size: 0.8rem;
  }
}

/* 滚动条样式 */
.config-file-edit-dialog .wallet-list-panel::-webkit-scrollbar,
.config-file-edit-dialog .wallet-private-key-list::-webkit-scrollbar {
  width: 6px;
}

.config-file-edit-dialog .wallet-list-panel::-webkit-scrollbar-track,
.config-file-edit-dialog .wallet-private-key-list::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

.config-file-edit-dialog .wallet-list-panel::-webkit-scrollbar-thumb,
.config-file-edit-dialog .wallet-private-key-list::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.config-file-edit-dialog .wallet-list-panel::-webkit-scrollbar-thumb:hover,
.config-file-edit-dialog .wallet-private-key-list::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

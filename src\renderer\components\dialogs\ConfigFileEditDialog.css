.config-file-edit-dialog {
  width: 90vw;
  max-width: 1000px;
  height: 80vh;
  max-height: 800px;
}

.config-file-edit-dialog .dialog-body {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0;
}

/* 文件选择器 */
.file-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--surface-color);
}

.file-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.file-name {
  font-weight: 500;
  color: var(--text-primary);
}

.unsaved-indicator {
  color: var(--warning-color);
  font-size: 1.2rem;
  line-height: 1;
}

.file-actions {
  display: flex;
  gap: 0.5rem;
}

/* 编辑器容器 */
.editor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* 编辑器工具栏 */
.editor-toolbar {
  display: flex;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--surface-color);
}

/* 钱包列表面板 */
.wallet-list-panel {
  border-bottom: 1px solid var(--border-color);
  background: var(--background-secondary);
  max-height: 200px;
  overflow-y: auto;
}

.wallet-list-panel h4 {
  margin: 0;
  padding: 0.75rem 1rem 0.5rem;
  font-size: 0.9rem;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--border-color);
}

.wallet-list {
  padding: 0.5rem;
}

.wallet-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  transition: all 0.2s ease;
}

.wallet-item:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.wallet-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.wallet-name {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.wallet-chain {
  font-size: 0.75rem;
  color: var(--text-secondary);
  background: var(--primary-color);
  color: white;
  padding: 0.125rem 0.375rem;
  border-radius: 3px;
  width: fit-content;
}

.wallet-address {
  font-size: 0.8rem;
  color: var(--text-secondary);
  font-family: 'Courier New', monospace;
  word-break: break-all;
}

.wallet-actions {
  display: flex;
  gap: 0.5rem;
}

/* 配置编辑器 */
.config-editor {
  flex: 1;
  width: 100%;
  padding: 1rem;
  border: none;
  outline: none;
  resize: none;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  background: var(--background-primary);
  color: var(--text-primary);
  min-height: 300px;
}

.config-editor:focus {
  background: var(--surface-color);
}

.config-editor::placeholder {
  color: var(--text-secondary);
  font-style: italic;
}

/* 空状态 */
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.empty-icon {
  font-size: 3rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin: 0 0 0.5rem;
  color: var(--text-primary);
}

.empty-state p {
  margin: 0 0 1.5rem;
  color: var(--text-secondary);
}

/* 暗色主题适配 */
[data-theme="dark"] .config-file-edit-dialog {
  --surface-color: #2a2a2a;
  --background-secondary: #1e1e1e;
}

[data-theme="dark"] .file-selector {
  background: #2a2a2a;
  border-bottom-color: #404040;
}

[data-theme="dark"] .editor-toolbar {
  background: #2a2a2a;
  border-bottom-color: #404040;
}

[data-theme="dark"] .wallet-list-panel {
  background: #1e1e1e;
  border-bottom-color: #404040;
}

[data-theme="dark"] .wallet-list-panel h4 {
  border-bottom-color: #404040;
}

[data-theme="dark"] .wallet-item {
  background: #2a2a2a;
  border-color: #404040;
}

[data-theme="dark"] .wallet-item:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .config-editor {
  background: #1a1a1a;
  color: #e0e0e0;
}

[data-theme="dark"] .config-editor:focus {
  background: #2a2a2a;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .config-file-edit-dialog {
    width: 95vw;
    height: 90vh;
  }
  
  .file-selector {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }
  
  .file-actions {
    justify-content: center;
  }
  
  .wallet-item {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .wallet-actions {
    justify-content: center;
  }
  
  .config-editor {
    font-size: 0.8rem;
  }
}

/* 滚动条样式 */
.wallet-list-panel::-webkit-scrollbar {
  width: 6px;
}

.wallet-list-panel::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

.wallet-list-panel::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.wallet-list-panel::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

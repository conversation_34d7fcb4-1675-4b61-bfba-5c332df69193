.global-settings-dialog {
  width: 90vw;
  max-width: 800px;
  height: 80vh;
  max-height: 700px;
  
  /* CSS变量定义 */
  --surface-color: #ffffff;
  --background-secondary: #f8f9fa;
  --background-primary: #ffffff;
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --border-color: #e1e5e9;
  --primary-color: #007acc;
  --primary-color-hover: #005a9e;
  --success-color: #28a745;
  --success-color-hover: #218838;
  --warning-color: #ffc107;
}

.global-settings-dialog .dialog-body {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0;
}

/* 设置内容 */
.global-settings-dialog .settings-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

/* 设置分组 */
.global-settings-dialog .settings-section {
  margin-bottom: 2rem;
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
}

.global-settings-dialog .section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: var(--background-secondary);
  border-bottom: 1px solid var(--border-color);
}

.global-settings-dialog .section-icon {
  font-size: 1.25rem;
  color: var(--primary-color);
}

.global-settings-dialog .section-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* 设置项 */
.global-settings-dialog .setting-item {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.global-settings-dialog .setting-item:last-child {
  border-bottom: none;
}

.global-settings-dialog .setting-label {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-weight: 500;
  color: var(--text-primary);
  cursor: pointer;
}

.global-settings-dialog .setting-description {
  font-size: 0.875rem;
  font-weight: 400;
  color: var(--text-secondary);
}

/* 输入框 */
.global-settings-dialog .setting-input {
  width: 80px;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 0.875rem;
  text-align: center;
  background: var(--background-primary);
  color: var(--text-primary);
}

.global-settings-dialog .setting-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
}

/* 复选框样式 */
.global-settings-dialog .setting-checkbox {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  flex: 1;
}

.global-settings-dialog .checkbox-btn {
  background: none;
  border: none;
  color: var(--primary-color);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.global-settings-dialog .checkbox-btn:hover {
  background: var(--surface-color);
  transform: scale(1.1);
}

/* 快捷键输入 */
.global-settings-dialog .shortcut-input {
  width: 150px;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 0.875rem;
  font-family: 'Courier New', Monaco, monospace;
  background: var(--background-primary);
  color: var(--text-primary);
  text-align: center;
}

.global-settings-dialog .shortcut-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
}

/* 加载状态 */
.global-settings-dialog .loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.global-settings-dialog .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 对话框底部 */
.global-settings-dialog .dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  background: var(--surface-color);
}

.global-settings-dialog .footer-left,
.global-settings-dialog .footer-right {
  display: flex;
  gap: 0.75rem;
}

/* 按钮样式 */
.global-settings-dialog .btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
}

.global-settings-dialog .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.global-settings-dialog .btn-primary {
  background: var(--primary-color);
  color: white;
}

.global-settings-dialog .btn-primary:hover:not(:disabled) {
  background: var(--primary-color-hover);
}

.global-settings-dialog .btn-outline {
  background: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.global-settings-dialog .btn-outline:hover:not(:disabled) {
  background: var(--surface-color);
  border-color: var(--primary-color);
}

/* 暗色主题适配 */
[data-theme="dark"] .global-settings-dialog {
  --surface-color: #2a2a2a;
  --background-secondary: #1e1e1e;
  --background-primary: #1a1a1a;
  --text-primary: #e0e0e0;
  --text-secondary: #b0b0b0;
  --border-color: #404040;
  --primary-color: #007acc;
  --primary-color-hover: #005a9e;
  --success-color: #28a745;
  --success-color-hover: #218838;
  --warning-color: #ffc107;
}

[data-theme="dark"] .global-settings-dialog .settings-section {
  background: #2a2a2a;
  border-color: #404040;
}

[data-theme="dark"] .global-settings-dialog .section-header {
  background: #1e1e1e;
  border-bottom-color: #404040;
}

[data-theme="dark"] .global-settings-dialog .setting-item {
  border-bottom-color: #404040;
}

[data-theme="dark"] .global-settings-dialog .setting-input,
[data-theme="dark"] .global-settings-dialog .shortcut-input {
  background: #1a1a1a;
  color: #e0e0e0;
  border-color: #404040;
}

[data-theme="dark"] .global-settings-dialog .setting-input:focus,
[data-theme="dark"] .global-settings-dialog .shortcut-input:focus {
  border-color: #007acc;
}

[data-theme="dark"] .global-settings-dialog .dialog-footer {
  background: #2a2a2a;
  border-top-color: #404040;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .global-settings-dialog {
    width: 95vw;
    height: 90vh;
  }
  
  .global-settings-dialog .setting-item {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .global-settings-dialog .setting-input,
  .global-settings-dialog .shortcut-input {
    width: 100%;
  }
  
  .global-settings-dialog .dialog-footer {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .global-settings-dialog .footer-left,
  .global-settings-dialog .footer-right {
    width: 100%;
    justify-content: center;
  }
}

/* 滚动条样式 */
.global-settings-dialog .settings-content::-webkit-scrollbar {
  width: 6px;
}

.global-settings-dialog .settings-content::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

.global-settings-dialog .settings-content::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.global-settings-dialog .settings-content::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

"use strict";
const electron = require("electron");
const electronAPI = {
  // 项目管理
  project: {
    list: () => electron.ipcRenderer.invoke("project:list"),
    add: (project) => electron.ipcRenderer.invoke("project:add", project),
    update: (id, updates) => electron.ipcRenderer.invoke("project:update", id, updates),
    delete: (id, deleteFiles) => electron.ipcRenderer.invoke("project:delete", id, deleteFiles),
    start: (id, profileId) => electron.ipcRenderer.invoke("project:start", id, profileId),
    stop: (id) => electron.ipcRenderer.invoke("project:stop", id),
    restart: (id) => electron.ipcRenderer.invoke("project:restart", id),
    scan: (directories) => electron.ipcRenderer.invoke("project:scan", directories),
    scanWithProgress: (directories) => electron.ipcRenderer.invoke("project:scan-with-progress", directories),
    detectType: (path) => electron.ipcRenderer.invoke("project:detect-type", path),
    detectProject: (path) => electron.ipcRenderer.invoke("project:detect-project", path),
    refreshStatus: (projectId) => electron.ipcRenderer.invoke("project:refresh-status", projectId),
    refreshAllStatus: () => electron.ipcRenderer.invoke("project:refresh-all-status")
  },
  // 文件系统操作
  fs: {
    selectDirectory: () => electron.ipcRenderer.invoke("fs:select-directory"),
    openDirectory: (path) => electron.ipcRenderer.invoke("fs:open-directory", path),
    openTerminal: (path) => electron.ipcRenderer.invoke("fs:open-terminal", path),
    createTempFile: (content) => electron.ipcRenderer.invoke("fs:create-temp-file", content),
    readFile: (filePath) => electron.ipcRenderer.invoke("fs:read-file", filePath),
    writeFile: (filePath, content) => electron.ipcRenderer.invoke("fs:write-file", filePath, content),
    fileExists: (filePath) => electron.ipcRenderer.invoke("fs:file-exists", filePath)
  },
  // Git操作
  git: {
    status: (path) => electron.ipcRenderer.invoke("git:status", path),
    pull: (path) => electron.ipcRenderer.invoke("git:pull", path),
    clone: (url, targetPath) => electron.ipcRenderer.invoke("git:clone", url, targetPath)
  },
  // 依赖管理
  deps: {
    check: (projectPath, projectType) => electron.ipcRenderer.invoke("deps:check", projectPath, projectType),
    install: (projectPath, projectType, dependencyName, version) => electron.ipcRenderer.invoke("deps:install", projectPath, projectType, dependencyName, version),
    installAll: (projectPath, projectType) => electron.ipcRenderer.invoke("deps:install-all", projectPath, projectType),
    uninstall: (projectPath, projectType, dependencyName) => electron.ipcRenderer.invoke("deps:uninstall", projectPath, projectType, dependencyName),
    cleanAll: (projectPath, projectType) => electron.ipcRenderer.invoke("deps:clean-all", projectPath, projectType)
  },
  // 虚拟环境
  venv: {
    checkPython: () => electron.ipcRenderer.invoke("venv:check-python"),
    list: (projectPath) => electron.ipcRenderer.invoke("venv:list", projectPath),
    create: (projectPath, envName, pythonVersion) => electron.ipcRenderer.invoke("venv:create", projectPath, envName, pythonVersion),
    activate: (projectPath, envName) => electron.ipcRenderer.invoke("venv:activate", projectPath, envName),
    delete: (projectPath, envName) => electron.ipcRenderer.invoke("venv:delete", projectPath, envName),
    installPackage: (projectPath, envName, packageName, version) => electron.ipcRenderer.invoke("venv:install-package", projectPath, envName, packageName, version),
    getPackages: (projectPath, envName) => electron.ipcRenderer.invoke("venv:get-packages", projectPath, envName)
  },
  // 配置管理
  config: {
    get: (projectPath) => electron.ipcRenderer.invoke("config:get", projectPath),
    save: (projectPath, configuration) => electron.ipcRenderer.invoke("config:save", projectPath, configuration),
    createProfile: (projectPath, profile) => electron.ipcRenderer.invoke("config:create-profile", projectPath, profile),
    updateProfile: (projectPath, profileId, updates) => electron.ipcRenderer.invoke("config:update-profile", projectPath, profileId, updates),
    deleteProfile: (projectPath, profileId) => electron.ipcRenderer.invoke("config:delete-profile", projectPath, profileId),
    getDefaultProfile: (projectPath) => electron.ipcRenderer.invoke("config:get-default-profile", projectPath),
    duplicateProfile: (projectPath, profileId, newName) => electron.ipcRenderer.invoke("config:duplicate-profile", projectPath, profileId, newName),
    export: (projectPath) => electron.ipcRenderer.invoke("config:export", projectPath),
    import: (projectPath, configJson) => electron.ipcRenderer.invoke("config:import", projectPath, configJson)
  },
  // 仓库服务
  repo: {
    getUserRepos: (platform, username, token) => electron.ipcRenderer.invoke("repo:get-user-repos", platform, username, token),
    pullRepos: (repositories, targetDirectory, onProgress) => electron.ipcRenderer.invoke("repo:pull-repos", repositories, targetDirectory, onProgress),
    clone: (repository, targetDirectory) => electron.ipcRenderer.invoke("repo:clone", repository, targetDirectory),
    filter: (repositories, filters) => electron.ipcRenderer.invoke("repo:filter", repositories, filters),
    validateGit: () => electron.ipcRenderer.invoke("repo:validate-git"),
    validateNetwork: (platform) => electron.ipcRenderer.invoke("repo:validate-network", platform),
    getDetails: (platform, fullName, token) => electron.ipcRenderer.invoke("repo:get-details", platform, fullName, token)
  },
  // 执行历史
  history: {
    get: (filter) => electron.ipcRenderer.invoke("history:get", filter),
    getRecord: (executionId) => electron.ipcRenderer.invoke("history:get-record", executionId),
    getStats: (projectId) => electron.ipcRenderer.invoke("history:get-stats", projectId),
    delete: (executionId) => electron.ipcRenderer.invoke("history:delete", executionId),
    deleteProject: (projectId) => electron.ipcRenderer.invoke("history:delete-project", projectId),
    cleanup: (daysToKeep) => electron.ipcRenderer.invoke("history:cleanup", daysToKeep),
    getRunning: () => electron.ipcRenderer.invoke("history:get-running")
  },
  // 项目日志
  logs: {
    get: (projectId, limit) => electron.ipcRenderer.invoke("logs:get", projectId, limit),
    clear: (projectId) => electron.ipcRenderer.invoke("logs:clear", projectId),
    cleanup: (projectId, keepCount) => electron.ipcRenderer.invoke("logs:cleanup", projectId, keepCount)
  },
  // 进程管理
  process: {
    sendInput: (pid, input) => electron.ipcRenderer.invoke("process:send-input", pid, input),
    kill: (pid) => electron.ipcRenderer.invoke("process:kill", pid),
    getRunning: () => electron.ipcRenderer.invoke("process:get-running")
  },
  // 系统信息
  system: {
    info: () => electron.ipcRenderer.invoke("system:info"),
    resources: (pid) => electron.ipcRenderer.invoke("system:resources", pid)
  },
  // 窗口控制
  window: {
    minimize: () => electron.ipcRenderer.invoke("window:minimize"),
    maximize: () => electron.ipcRenderer.invoke("window:maximize"),
    unmaximize: () => electron.ipcRenderer.invoke("window:unmaximize"),
    close: () => electron.ipcRenderer.invoke("window:close"),
    isMaximized: () => electron.ipcRenderer.invoke("window:is-maximized")
  },
  // 对话框
  dialog: {
    showOpenDialog: (options) => electron.ipcRenderer.invoke("dialog:showOpenDialog", options),
    showSaveDialog: (options) => electron.ipcRenderer.invoke("dialog:showSaveDialog", options),
    showMessageBox: (options) => electron.ipcRenderer.invoke("dialog:showMessageBox", options)
  },
  // 事件监听
  on: (channel, callback) => {
    electron.ipcRenderer.on(channel, (_, ...args) => callback(...args));
  },
  off: (channel, callback) => {
    electron.ipcRenderer.removeListener(channel, callback);
  },
  // 移除所有监听器
  removeAllListeners: (channel) => {
    electron.ipcRenderer.removeAllListeners(channel);
  },
  // 进程事件监听
  onProcessOutput: (listener) => {
    electron.ipcRenderer.on("process:output", (_, pid, output, type) => listener(pid, output, type));
  },
  onProcessResources: (listener) => {
    electron.ipcRenderer.on("process:resources", (_, pid, resources) => listener(pid, resources));
  },
  onProcessExit: (listener) => {
    electron.ipcRenderer.on("process:exit", (_, pid, code, signal) => listener(pid, code, signal));
  },
  onProcessError: (listener) => {
    electron.ipcRenderer.on("process:error", (_, pid, error) => listener(pid, error));
  },
  // 项目分析
  analysis: {
    analyzeFiles: (projectPath) => electron.ipcRenderer.invoke("analysis:files", projectPath),
    getEnvironment: (projectPath) => electron.ipcRenderer.invoke("analysis:environment", projectPath),
    getGitInfo: (projectPath) => electron.ipcRenderer.invoke("analysis:git", projectPath),
    detectType: (projectPath) => electron.ipcRenderer.invoke("analysis:detect-type", projectPath)
  },
  // 钱包配置
  wallet: {
    getConfig: (projectPath) => electron.ipcRenderer.invoke("wallet:get-config", projectPath),
    saveConfig: (projectPath, config) => electron.ipcRenderer.invoke("wallet:save-config", projectPath, config),
    add: (projectPath, wallet) => electron.ipcRenderer.invoke("wallet:add", projectPath, wallet),
    update: (projectPath, walletId, updates) => electron.ipcRenderer.invoke("wallet:update", projectPath, walletId, updates),
    delete: (projectPath, walletId) => electron.ipcRenderer.invoke("wallet:delete", projectPath, walletId),
    getDefault: (projectPath) => electron.ipcRenderer.invoke("wallet:get-default", projectPath),
    validate: (wallet) => electron.ipcRenderer.invoke("wallet:validate", wallet)
  },
  // 全局钱包管理
  globalWallet: {
    getConfig: () => electron.ipcRenderer.invoke("global-wallet:get-config"),
    saveConfig: (config) => electron.ipcRenderer.invoke("global-wallet:save-config", config),
    add: (wallet) => electron.ipcRenderer.invoke("global-wallet:add", wallet),
    addBatch: (wallets) => electron.ipcRenderer.invoke("global-wallet:add-batch", wallets),
    update: (walletId, updates) => electron.ipcRenderer.invoke("global-wallet:update", walletId, updates),
    delete: (walletId) => electron.ipcRenderer.invoke("global-wallet:delete", walletId),
    deleteBatch: (walletIds) => electron.ipcRenderer.invoke("global-wallet:delete-batch", walletIds),
    importFromFile: (filePath, chain) => electron.ipcRenderer.invoke("global-wallet:import-from-file", filePath, chain),
    createBatch: (count, chain) => electron.ipcRenderer.invoke("global-wallet:create-batch", count, chain),
    getAddressFromMnemonic: (mnemonic, chain, derivationPath) => electron.ipcRenderer.invoke("global-wallet:get-address-from-mnemonic", mnemonic, chain, derivationPath)
  },
  // 代理管理
  proxyManager: {
    getConfig: () => electron.ipcRenderer.invoke("proxy-manager:get-config"),
    saveConfig: (config) => electron.ipcRenderer.invoke("proxy-manager:save-config", config),
    fetchAll: () => electron.ipcRenderer.invoke("proxy-manager:fetch-all"),
    testProxy: (proxy, testUrl, timeout) => electron.ipcRenderer.invoke("proxy-manager:test-proxy", proxy, testUrl, timeout),
    testBatch: (proxies, maxConcurrent) => electron.ipcRenderer.invoke("proxy-manager:test-batch", proxies, maxConcurrent),
    delete: (proxyIds) => electron.ipcRenderer.invoke("proxy-manager:delete", proxyIds)
  },
  // 代理配置
  proxy: {
    getConfig: (projectPath) => electron.ipcRenderer.invoke("proxy:get-config", projectPath),
    saveConfig: (projectPath, config) => electron.ipcRenderer.invoke("proxy:save-config", projectPath, config),
    add: (projectPath, proxy) => electron.ipcRenderer.invoke("proxy:add", projectPath, proxy),
    update: (projectPath, proxyId, updates) => electron.ipcRenderer.invoke("proxy:update", projectPath, proxyId, updates),
    delete: (projectPath, proxyId) => electron.ipcRenderer.invoke("proxy:delete", projectPath, proxyId),
    getDefault: (projectPath) => electron.ipcRenderer.invoke("proxy:get-default", projectPath),
    test: (proxy) => electron.ipcRenderer.invoke("proxy:test", proxy),
    getSystem: () => electron.ipcRenderer.invoke("proxy:get-system")
  },
  // 性能监控
  performance: {
    start: () => electron.ipcRenderer.invoke("performance:start"),
    stop: () => electron.ipcRenderer.invoke("performance:stop"),
    getSystem: () => electron.ipcRenderer.invoke("performance:get-system"),
    getProcess: (pid) => electron.ipcRenderer.invoke("performance:get-process", pid),
    getHistory: (limit) => electron.ipcRenderer.invoke("performance:get-history", limit),
    getProcessHistory: (pid, limit) => electron.ipcRenderer.invoke("performance:get-process-history", pid, limit),
    getAlerts: (limit) => electron.ipcRenderer.invoke("performance:get-alerts", limit),
    clearAlerts: () => electron.ipcRenderer.invoke("performance:clear-alerts"),
    setThresholds: (thresholds) => electron.ipcRenderer.invoke("performance:set-thresholds", thresholds),
    getThresholds: () => electron.ipcRenderer.invoke("performance:get-thresholds"),
    startProcess: (pid, projectId) => electron.ipcRenderer.invoke("performance:start-process", pid, projectId),
    stopProcess: (pid) => electron.ipcRenderer.invoke("performance:stop-process", pid)
  },
  // 伪终端
  terminal: {
    create: (id, cwd, title) => electron.ipcRenderer.invoke("terminal:create", id, cwd, title),
    write: (id, data) => electron.ipcRenderer.invoke("terminal:write", id, data),
    resize: (id, cols, rows) => electron.ipcRenderer.invoke("terminal:resize", id, cols, rows),
    close: (id) => electron.ipcRenderer.invoke("terminal:close", id),
    minimize: (id) => electron.ipcRenderer.invoke("terminal:minimize", id),
    restore: (id) => electron.ipcRenderer.invoke("terminal:restore", id),
    getSession: (id) => electron.ipcRenderer.invoke("terminal:get-session", id),
    getAllSessions: () => electron.ipcRenderer.invoke("terminal:get-all-sessions"),
    getLogs: (id, limit) => electron.ipcRenderer.invoke("terminal:get-logs", id, limit),
    clearLogs: (id) => electron.ipcRenderer.invoke("terminal:clear-logs", id)
  },
  // 性能监控事件监听
  onSystemMetrics: (listener) => {
    electron.ipcRenderer.on("performance:system-metrics", (_, metrics) => listener(metrics));
  },
  onProcessMetrics: (listener) => {
    electron.ipcRenderer.on("performance:process-metrics", (_, metrics) => listener(metrics));
  },
  onPerformanceAlert: (listener) => {
    electron.ipcRenderer.on("performance:alert", (_, alert) => listener(alert));
  },
  // 终端事件监听
  onTerminalOutput: (listener) => {
    const handler = (_, data) => listener(data);
    electron.ipcRenderer.on("terminal:output", handler);
    return () => electron.ipcRenderer.removeListener("terminal:output", handler);
  },
  onTerminalExit: (listener) => {
    const handler = (_, data) => listener(data);
    electron.ipcRenderer.on("terminal:exit", handler);
    return () => electron.ipcRenderer.removeListener("terminal:exit", handler);
  },
  onTerminalError: (listener) => {
    const handler = (_, data) => listener(data);
    electron.ipcRenderer.on("terminal:error", handler);
    return () => electron.ipcRenderer.removeListener("terminal:error", handler);
  },
  // 批量操作
  batch: {
    execute: (type, projectIds, options) => electron.ipcRenderer.invoke("batch:execute", type, projectIds, options),
    cancel: (operationId) => electron.ipcRenderer.invoke("batch:cancel", operationId),
    get: (operationId) => electron.ipcRenderer.invoke("batch:get", operationId),
    getAll: () => electron.ipcRenderer.invoke("batch:get-all"),
    cleanup: () => electron.ipcRenderer.invoke("batch:cleanup"),
    getStats: () => electron.ipcRenderer.invoke("batch:get-stats"),
    estimateTime: (type, projectCount) => electron.ipcRenderer.invoke("batch:estimate-time", type, projectCount),
    validate: (type, projectIds) => electron.ipcRenderer.invoke("batch:validate", type, projectIds)
  },
  // 批量操作事件监听
  onBatchOperationCreated: (listener) => {
    electron.ipcRenderer.on("batch:operation-created", (_, operation) => listener(operation));
  },
  onBatchOperationStarted: (listener) => {
    electron.ipcRenderer.on("batch:operation-started", (_, operation) => listener(operation));
  },
  onBatchOperationProgress: (listener) => {
    electron.ipcRenderer.on("batch:operation-progress", (_, operation) => listener(operation));
  },
  onBatchOperationCompleted: (listener) => {
    electron.ipcRenderer.on("batch:operation-completed", (_, operation) => listener(operation));
  },
  onBatchOperationFailed: (listener) => {
    electron.ipcRenderer.on("batch:operation-failed", (_, operation, error) => listener(operation, error));
  },
  onBatchOperationCancelled: (listener) => {
    electron.ipcRenderer.on("batch:operation-cancelled", (_, operation) => listener(operation));
  },
  // Git配置
  gitConfig: {
    add: (config) => electron.ipcRenderer.invoke("git-config:add", config),
    update: (id, updates) => electron.ipcRenderer.invoke("git-config:update", id, updates),
    getAll: (filter) => electron.ipcRenderer.invoke("git-config:get-all", filter),
    get: (id) => electron.ipcRenderer.invoke("git-config:get", id),
    getDefault: () => electron.ipcRenderer.invoke("git-config:get-default"),
    delete: (id) => electron.ipcRenderer.invoke("git-config:delete", id),
    setDefault: (id) => electron.ipcRenderer.invoke("git-config:set-default", id),
    saveUsernameHistory: (username, provider) => electron.ipcRenderer.invoke("git-config:save-username-history", username, provider),
    getUsernameHistory: (provider, limit) => electron.ipcRenderer.invoke("git-config:get-username-history", provider, limit),
    cleanupUsernameHistory: (keepCount) => electron.ipcRenderer.invoke("git-config:cleanup-username-history", keepCount),
    saveTokenHistory: (provider, token, tokenName) => electron.ipcRenderer.invoke("git-config:save-token-history", provider, token, tokenName),
    getTokenHistory: (provider, limit) => electron.ipcRenderer.invoke("git-config:get-token-history", provider, limit),
    deleteTokenHistory: (provider, token) => electron.ipcRenderer.invoke("git-config:delete-token-history", provider, token),
    cleanupTokenHistory: (keepCount) => electron.ipcRenderer.invoke("git-config:cleanup-token-history", keepCount),
    saveDirectoryHistory: (directoryPath) => electron.ipcRenderer.invoke("git-config:save-directory-history", directoryPath),
    getDirectoryHistory: (limit) => electron.ipcRenderer.invoke("git-config:get-directory-history", limit),
    getLastUsedDirectory: () => electron.ipcRenderer.invoke("git-config:get-last-used-directory"),
    cleanupDirectoryHistory: (keepCount) => electron.ipcRenderer.invoke("git-config:cleanup-directory-history", keepCount)
  },
  // GitHub API
  github: {
    getUserRepositories: (username, token) => electron.ipcRenderer.invoke("github:get-user-repositories", username, token),
    getGiteeRepositories: (username, token) => electron.ipcRenderer.invoke("github:get-gitee-repositories", username, token),
    getRepositoriesByPlatform: (platform, username, token) => electron.ipcRenderer.invoke("github:get-repositories-by-platform", platform, username, token),
    getRepositoriesByToken: (platform, token) => electron.ipcRenderer.invoke("github:get-repositories-by-token", platform, token),
    cloneRepository: (repository, targetPath, useSSH) => electron.ipcRenderer.invoke("github:clone-repository", repository, targetPath, useSSH)
  },
  // Git仓库
  gitRepo: {
    getRepositories: (configId, filter) => electron.ipcRenderer.invoke("git-repo:get-repositories", configId, filter),
    pullProjects: (request) => electron.ipcRenderer.invoke("git-repo:pull-projects", request)
  },
  // Git拉取进度事件监听
  onGitPullProgress: (listener) => {
    electron.ipcRenderer.on("git-pull:progress", (_, progress) => listener(progress));
  }
};
electron.contextBridge.exposeInMainWorld("electronAPI", electronAPI);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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

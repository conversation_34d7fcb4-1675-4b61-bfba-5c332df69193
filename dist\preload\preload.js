"use strict";
const electron = require("electron");
const electronAPI = {
  // 项目管理
  project: {
    list: () => electron.ipcRenderer.invoke("project:list"),
    add: (project) => electron.ipcRenderer.invoke("project:add", project),
    update: (id, updates) => electron.ipcRenderer.invoke("project:update", id, updates),
    delete: (id, deleteFiles) => electron.ipcRenderer.invoke("project:delete", id, deleteFiles),
    start: (id, profileId) => electron.ipcRenderer.invoke("project:start", id, profileId),
    stop: (id) => electron.ipcRenderer.invoke("project:stop", id),
    restart: (id) => electron.ipcRenderer.invoke("project:restart", id),
    scan: (directories) => electron.ipcRenderer.invoke("project:scan", directories),
    scanWithProgress: (directories) => electron.ipcRenderer.invoke("project:scan-with-progress", directories),
    detectType: (path) => electron.ipcRenderer.invoke("project:detect-type", path),
    detectProject: (path) => electron.ipcRenderer.invoke("project:detect-project", path),
    refreshStatus: (projectId) => electron.ipcRenderer.invoke("project:refresh-status", projectId),
    refreshAllStatus: () => electron.ipcRenderer.invoke("project:refresh-all-status")
  },
  // 文件系统操作
  fs: {
    selectDirectory: () => electron.ipcRenderer.invoke("fs:select-directory"),
    openDirectory: (path) => electron.ipcRenderer.invoke("fs:open-directory", path),
    openTerminal: (path) => electron.ipcRenderer.invoke("fs:open-terminal", path),
    createTempFile: (content) => electron.ipcRenderer.invoke("fs:create-temp-file", content)
  },
  // Git操作
  git: {
    status: (path) => electron.ipcRenderer.invoke("git:status", path),
    pull: (path) => electron.ipcRenderer.invoke("git:pull", path),
    clone: (url, targetPath) => electron.ipcRenderer.invoke("git:clone", url, targetPath)
  },
  // 依赖管理
  deps: {
    check: (projectPath, projectType) => electron.ipcRenderer.invoke("deps:check", projectPath, projectType),
    install: (projectPath, projectType, dependencyName, version) => electron.ipcRenderer.invoke("deps:install", projectPath, projectType, dependencyName, version),
    installAll: (projectPath, projectType) => electron.ipcRenderer.invoke("deps:install-all", projectPath, projectType),
    uninstall: (projectPath, projectType, dependencyName) => electron.ipcRenderer.invoke("deps:uninstall", projectPath, projectType, dependencyName),
    cleanAll: (projectPath, projectType) => electron.ipcRenderer.invoke("deps:clean-all", projectPath, projectType)
  },
  // 虚拟环境
  venv: {
    checkPython: () => electron.ipcRenderer.invoke("venv:check-python"),
    list: (projectPath) => electron.ipcRenderer.invoke("venv:list", projectPath),
    create: (projectPath, envName, pythonVersion) => electron.ipcRenderer.invoke("venv:create", projectPath, envName, pythonVersion),
    activate: (projectPath, envName) => electron.ipcRenderer.invoke("venv:activate", projectPath, envName),
    delete: (projectPath, envName) => electron.ipcRenderer.invoke("venv:delete", projectPath, envName),
    installPackage: (projectPath, envName, packageName, version) => electron.ipcRenderer.invoke("venv:install-package", projectPath, envName, packageName, version),
    getPackages: (projectPath, envName) => electron.ipcRenderer.invoke("venv:get-packages", projectPath, envName)
  },
  // 配置管理
  config: {
    get: (projectPath) => electron.ipcRenderer.invoke("config:get", projectPath),
    save: (projectPath, configuration) => electron.ipcRenderer.invoke("config:save", projectPath, configuration),
    createProfile: (projectPath, profile) => electron.ipcRenderer.invoke("config:create-profile", projectPath, profile),
    updateProfile: (projectPath, profileId, updates) => electron.ipcRenderer.invoke("config:update-profile", projectPath, profileId, updates),
    deleteProfile: (projectPath, profileId) => electron.ipcRenderer.invoke("config:delete-profile", projectPath, profileId),
    getDefaultProfile: (projectPath) => electron.ipcRenderer.invoke("config:get-default-profile", projectPath),
    duplicateProfile: (projectPath, profileId, newName) => electron.ipcRenderer.invoke("config:duplicate-profile", projectPath, profileId, newName),
    export: (projectPath) => electron.ipcRenderer.invoke("config:export", projectPath),
    import: (projectPath, configJson) => electron.ipcRenderer.invoke("config:import", projectPath, configJson)
  },
  // 仓库服务
  repo: {
    getUserRepos: (platform, username, token) => electron.ipcRenderer.invoke("repo:get-user-repos", platform, username, token),
    pullRepos: (repositories, targetDirectory, onProgress) => electron.ipcRenderer.invoke("repo:pull-repos", repositories, targetDirectory, onProgress),
    clone: (repository, targetDirectory) => electron.ipcRenderer.invoke("repo:clone", repository, targetDirectory),
    filter: (repositories, filters) => electron.ipcRenderer.invoke("repo:filter", repositories, filters),
    validateGit: () => electron.ipcRenderer.invoke("repo:validate-git"),
    validateNetwork: (platform) => electron.ipcRenderer.invoke("repo:validate-network", platform),
    getDetails: (platform, fullName, token) => electron.ipcRenderer.invoke("repo:get-details", platform, fullName, token)
  },
  // 执行历史
  history: {
    get: (filter) => electron.ipcRenderer.invoke("history:get", filter),
    getRecord: (executionId) => electron.ipcRenderer.invoke("history:get-record", executionId),
    getStats: (projectId) => electron.ipcRenderer.invoke("history:get-stats", projectId),
    delete: (executionId) => electron.ipcRenderer.invoke("history:delete", executionId),
    deleteProject: (projectId) => electron.ipcRenderer.invoke("history:delete-project", projectId),
    cleanup: (daysToKeep) => electron.ipcRenderer.invoke("history:cleanup", daysToKeep),
    getRunning: () => electron.ipcRenderer.invoke("history:get-running")
  },
  // 项目日志
  logs: {
    get: (projectId, limit) => electron.ipcRenderer.invoke("logs:get", projectId, limit),
    clear: (projectId) => electron.ipcRenderer.invoke("logs:clear", projectId),
    cleanup: (projectId, keepCount) => electron.ipcRenderer.invoke("logs:cleanup", projectId, keepCount)
  },
  // 进程管理
  process: {
    sendInput: (pid, input) => electron.ipcRenderer.invoke("process:send-input", pid, input),
    kill: (pid) => electron.ipcRenderer.invoke("process:kill", pid),
    getRunning: () => electron.ipcRenderer.invoke("process:get-running")
  },
  // 系统信息
  system: {
    info: () => electron.ipcRenderer.invoke("system:info"),
    resources: (pid) => electron.ipcRenderer.invoke("system:resources", pid)
  },
  // 窗口控制
  window: {
    minimize: () => electron.ipcRenderer.invoke("window:minimize"),
    maximize: () => electron.ipcRenderer.invoke("window:maximize"),
    unmaximize: () => electron.ipcRenderer.invoke("window:unmaximize"),
    close: () => electron.ipcRenderer.invoke("window:close"),
    isMaximized: () => electron.ipcRenderer.invoke("window:is-maximized")
  },
  // 对话框
  dialog: {
    showOpenDialog: (options) => electron.ipcRenderer.invoke("dialog:showOpenDialog", options),
    showSaveDialog: (options) => electron.ipcRenderer.invoke("dialog:showSaveDialog", options),
    showMessageBox: (options) => electron.ipcRenderer.invoke("dialog:showMessageBox", options)
  },
  // 事件监听
  on: (channel, callback) => {
    electron.ipcRenderer.on(channel, (_, ...args) => callback(...args));
  },
  off: (channel, callback) => {
    electron.ipcRenderer.removeListener(channel, callback);
  },
  // 移除所有监听器
  removeAllListeners: (channel) => {
    electron.ipcRenderer.removeAllListeners(channel);
  },
  // 进程事件监听
  onProcessOutput: (listener) => {
    electron.ipcRenderer.on("process:output", (_, pid, output, type) => listener(pid, output, type));
  },
  onProcessResources: (listener) => {
    electron.ipcRenderer.on("process:resources", (_, pid, resources) => listener(pid, resources));
  },
  onProcessExit: (listener) => {
    electron.ipcRenderer.on("process:exit", (_, pid, code, signal) => listener(pid, code, signal));
  },
  onProcessError: (listener) => {
    electron.ipcRenderer.on("process:error", (_, pid, error) => listener(pid, error));
  },
  // 项目分析
  analysis: {
    analyzeFiles: (projectPath) => electron.ipcRenderer.invoke("analysis:files", projectPath),
    getEnvironment: (projectPath) => electron.ipcRenderer.invoke("analysis:environment", projectPath),
    getGitInfo: (projectPath) => electron.ipcRenderer.invoke("analysis:git", projectPath),
    detectType: (projectPath) => electron.ipcRenderer.invoke("analysis:detect-type", projectPath)
  },
  // 钱包配置
  wallet: {
    getConfig: (projectPath) => electron.ipcRenderer.invoke("wallet:get-config", projectPath),
    saveConfig: (projectPath, config) => electron.ipcRenderer.invoke("wallet:save-config", projectPath, config),
    add: (projectPath, wallet) => electron.ipcRenderer.invoke("wallet:add", projectPath, wallet),
    update: (projectPath, walletId, updates) => electron.ipcRenderer.invoke("wallet:update", projectPath, walletId, updates),
    delete: (projectPath, walletId) => electron.ipcRenderer.invoke("wallet:delete", projectPath, walletId),
    getDefault: (projectPath) => electron.ipcRenderer.invoke("wallet:get-default", projectPath),
    validate: (wallet) => electron.ipcRenderer.invoke("wallet:validate", wallet)
  },
  // 全局钱包管理
  globalWallet: {
    getConfig: () => electron.ipcRenderer.invoke("global-wallet:get-config"),
    saveConfig: (config) => electron.ipcRenderer.invoke("global-wallet:save-config", config),
    add: (wallet) => electron.ipcRenderer.invoke("global-wallet:add", wallet),
    addBatch: (wallets) => electron.ipcRenderer.invoke("global-wallet:add-batch", wallets),
    update: (walletId, updates) => electron.ipcRenderer.invoke("global-wallet:update", walletId, updates),
    delete: (walletId) => electron.ipcRenderer.invoke("global-wallet:delete", walletId),
    deleteBatch: (walletIds) => electron.ipcRenderer.invoke("global-wallet:delete-batch", walletIds),
    importFromFile: (filePath, chain) => electron.ipcRenderer.invoke("global-wallet:import-from-file", filePath, chain),
    createBatch: (count, chain) => electron.ipcRenderer.invoke("global-wallet:create-batch", count, chain),
    getAddressFromMnemonic: (mnemonic, chain, derivationPath) => electron.ipcRenderer.invoke("global-wallet:get-address-from-mnemonic", mnemonic, chain, derivationPath)
  },
  // 代理管理
  proxyManager: {
    getConfig: () => electron.ipcRenderer.invoke("proxy-manager:get-config"),
    saveConfig: (config) => electron.ipcRenderer.invoke("proxy-manager:save-config", config),
    fetchAll: () => electron.ipcRenderer.invoke("proxy-manager:fetch-all"),
    testProxy: (proxy, testUrl, timeout) => electron.ipcRenderer.invoke("proxy-manager:test-proxy", proxy, testUrl, timeout),
    testBatch: (proxies, maxConcurrent) => electron.ipcRenderer.invoke("proxy-manager:test-batch", proxies, maxConcurrent),
    delete: (proxyIds) => electron.ipcRenderer.invoke("proxy-manager:delete", proxyIds)
  },
  // 代理配置
  proxy: {
    getConfig: (projectPath) => electron.ipcRenderer.invoke("proxy:get-config", projectPath),
    saveConfig: (projectPath, config) => electron.ipcRenderer.invoke("proxy:save-config", projectPath, config),
    add: (projectPath, proxy) => electron.ipcRenderer.invoke("proxy:add", projectPath, proxy),
    update: (projectPath, proxyId, updates) => electron.ipcRenderer.invoke("proxy:update", projectPath, proxyId, updates),
    delete: (projectPath, proxyId) => electron.ipcRenderer.invoke("proxy:delete", projectPath, proxyId),
    getDefault: (projectPath) => electron.ipcRenderer.invoke("proxy:get-default", projectPath),
    test: (proxy) => electron.ipcRenderer.invoke("proxy:test", proxy),
    getSystem: () => electron.ipcRenderer.invoke("proxy:get-system")
  },
  // 性能监控
  performance: {
    start: () => electron.ipcRenderer.invoke("performance:start"),
    stop: () => electron.ipcRenderer.invoke("performance:stop"),
    getSystem: () => electron.ipcRenderer.invoke("performance:get-system"),
    getProcess: (pid) => electron.ipcRenderer.invoke("performance:get-process", pid),
    getHistory: (limit) => electron.ipcRenderer.invoke("performance:get-history", limit),
    getProcessHistory: (pid, limit) => electron.ipcRenderer.invoke("performance:get-process-history", pid, limit),
    getAlerts: (limit) => electron.ipcRenderer.invoke("performance:get-alerts", limit),
    clearAlerts: () => electron.ipcRenderer.invoke("performance:clear-alerts"),
    setThresholds: (thresholds) => electron.ipcRenderer.invoke("performance:set-thresholds", thresholds),
    getThresholds: () => electron.ipcRenderer.invoke("performance:get-thresholds"),
    startProcess: (pid, projectId) => electron.ipcRenderer.invoke("performance:start-process", pid, projectId),
    stopProcess: (pid) => electron.ipcRenderer.invoke("performance:stop-process", pid)
  },
  // 伪终端
  terminal: {
    create: (id, cwd, title) => electron.ipcRenderer.invoke("terminal:create", id, cwd, title),
    write: (id, data) => electron.ipcRenderer.invoke("terminal:write", id, data),
    resize: (id, cols, rows) => electron.ipcRenderer.invoke("terminal:resize", id, cols, rows),
    close: (id) => electron.ipcRenderer.invoke("terminal:close", id),
    minimize: (id) => electron.ipcRenderer.invoke("terminal:minimize", id),
    restore: (id) => electron.ipcRenderer.invoke("terminal:restore", id),
    getSession: (id) => electron.ipcRenderer.invoke("terminal:get-session", id),
    getAllSessions: () => electron.ipcRenderer.invoke("terminal:get-all-sessions"),
    getLogs: (id, limit) => electron.ipcRenderer.invoke("terminal:get-logs", id, limit),
    clearLogs: (id) => electron.ipcRenderer.invoke("terminal:clear-logs", id)
  },
  // 性能监控事件监听
  onSystemMetrics: (listener) => {
    electron.ipcRenderer.on("performance:system-metrics", (_, metrics) => listener(metrics));
  },
  onProcessMetrics: (listener) => {
    electron.ipcRenderer.on("performance:process-metrics", (_, metrics) => listener(metrics));
  },
  onPerformanceAlert: (listener) => {
    electron.ipcRenderer.on("performance:alert", (_, alert) => listener(alert));
  },
  // 终端事件监听
  onTerminalOutput: (listener) => {
    const handler = (_, data) => listener(data);
    electron.ipcRenderer.on("terminal:output", handler);
    return () => electron.ipcRenderer.removeListener("terminal:output", handler);
  },
  onTerminalExit: (listener) => {
    const handler = (_, data) => listener(data);
    electron.ipcRenderer.on("terminal:exit", handler);
    return () => electron.ipcRenderer.removeListener("terminal:exit", handler);
  },
  onTerminalError: (listener) => {
    const handler = (_, data) => listener(data);
    electron.ipcRenderer.on("terminal:error", handler);
    return () => electron.ipcRenderer.removeListener("terminal:error", handler);
  },
  // 批量操作
  batch: {
    execute: (type, projectIds, options) => electron.ipcRenderer.invoke("batch:execute", type, projectIds, options),
    cancel: (operationId) => electron.ipcRenderer.invoke("batch:cancel", operationId),
    get: (operationId) => electron.ipcRenderer.invoke("batch:get", operationId),
    getAll: () => electron.ipcRenderer.invoke("batch:get-all"),
    cleanup: () => electron.ipcRenderer.invoke("batch:cleanup"),
    getStats: () => electron.ipcRenderer.invoke("batch:get-stats"),
    estimateTime: (type, projectCount) => electron.ipcRenderer.invoke("batch:estimate-time", type, projectCount),
    validate: (type, projectIds) => electron.ipcRenderer.invoke("batch:validate", type, projectIds)
  },
  // 批量操作事件监听
  onBatchOperationCreated: (listener) => {
    electron.ipcRenderer.on("batch:operation-created", (_, operation) => listener(operation));
  },
  onBatchOperationStarted: (listener) => {
    electron.ipcRenderer.on("batch:operation-started", (_, operation) => listener(operation));
  },
  onBatchOperationProgress: (listener) => {
    electron.ipcRenderer.on("batch:operation-progress", (_, operation) => listener(operation));
  },
  onBatchOperationCompleted: (listener) => {
    electron.ipcRenderer.on("batch:operation-completed", (_, operation) => listener(operation));
  },
  onBatchOperationFailed: (listener) => {
    electron.ipcRenderer.on("batch:operation-failed", (_, operation, error) => listener(operation, error));
  },
  onBatchOperationCancelled: (listener) => {
    electron.ipcRenderer.on("batch:operation-cancelled", (_, operation) => listener(operation));
  },
  // Git配置
  gitConfig: {
    add: (config) => electron.ipcRenderer.invoke("git-config:add", config),
    update: (id, updates) => electron.ipcRenderer.invoke("git-config:update", id, updates),
    getAll: (filter) => electron.ipcRenderer.invoke("git-config:get-all", filter),
    get: (id) => electron.ipcRenderer.invoke("git-config:get", id),
    getDefault: () => electron.ipcRenderer.invoke("git-config:get-default"),
    delete: (id) => electron.ipcRenderer.invoke("git-config:delete", id),
    setDefault: (id) => electron.ipcRenderer.invoke("git-config:set-default", id),
    saveUsernameHistory: (username, provider) => electron.ipcRenderer.invoke("git-config:save-username-history", username, provider),
    getUsernameHistory: (provider, limit) => electron.ipcRenderer.invoke("git-config:get-username-history", provider, limit),
    cleanupUsernameHistory: (keepCount) => electron.ipcRenderer.invoke("git-config:cleanup-username-history", keepCount),
    saveTokenHistory: (provider, token, tokenName) => electron.ipcRenderer.invoke("git-config:save-token-history", provider, token, tokenName),
    getTokenHistory: (provider, limit) => electron.ipcRenderer.invoke("git-config:get-token-history", provider, limit),
    deleteTokenHistory: (provider, token) => electron.ipcRenderer.invoke("git-config:delete-token-history", provider, token),
    cleanupTokenHistory: (keepCount) => electron.ipcRenderer.invoke("git-config:cleanup-token-history", keepCount),
    saveDirectoryHistory: (directoryPath) => electron.ipcRenderer.invoke("git-config:save-directory-history", directoryPath),
    getDirectoryHistory: (limit) => electron.ipcRenderer.invoke("git-config:get-directory-history", limit),
    getLastUsedDirectory: () => electron.ipcRenderer.invoke("git-config:get-last-used-directory"),
    cleanupDirectoryHistory: (keepCount) => electron.ipcRenderer.invoke("git-config:cleanup-directory-history", keepCount)
  },
  // GitHub API
  github: {
    getUserRepositories: (username, token) => electron.ipcRenderer.invoke("github:get-user-repositories", username, token),
    getGiteeRepositories: (username, token) => electron.ipcRenderer.invoke("github:get-gitee-repositories", username, token),
    getRepositoriesByPlatform: (platform, username, token) => electron.ipcRenderer.invoke("github:get-repositories-by-platform", platform, username, token),
    getRepositoriesByToken: (platform, token) => electron.ipcRenderer.invoke("github:get-repositories-by-token", platform, token),
    cloneRepository: (repository, targetPath, useSSH) => electron.ipcRenderer.invoke("github:clone-repository", repository, targetPath, useSSH)
  },
  // Git仓库
  gitRepo: {
    getRepositories: (configId, filter) => electron.ipcRenderer.invoke("git-repo:get-repositories", configId, filter),
    pullProjects: (request) => electron.ipcRenderer.invoke("git-repo:pull-projects", request)
  },
  // Git拉取进度事件监听
  onGitPullProgress: (listener) => {
    electron.ipcRenderer.on("git-pull:progress", (_, progress) => listener(progress));
  }
};
electron.contextBridge.exposeInMainWorld("electronAPI", electronAPI);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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

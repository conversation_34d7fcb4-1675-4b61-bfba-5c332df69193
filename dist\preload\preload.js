"use strict";
const electron = require("electron");
const electronAPI = {
  // 项目管理
  project: {
    list: () => electron.ipcRenderer.invoke("project:list"),
    add: (project) => electron.ipcRenderer.invoke("project:add", project),
    update: (id, updates) => electron.ipcRenderer.invoke("project:update", id, updates),
    delete: (id, deleteFiles) => electron.ipcRenderer.invoke("project:delete", id, deleteFiles),
    start: (id, profileId) => electron.ipcRenderer.invoke("project:start", id, profileId),
    stop: (id) => electron.ipcRenderer.invoke("project:stop", id),
    restart: (id) => electron.ipcRenderer.invoke("project:restart", id),
    scan: (directories) => electron.ipcRenderer.invoke("project:scan", directories),
    scanWithProgress: (directories) => electron.ipcRenderer.invoke("project:scan-with-progress", directories),
    detectType: (path) => electron.ipcRenderer.invoke("project:detect-type", path),
    detectProject: (path) => electron.ipcRenderer.invoke("project:detect-project", path),
    refreshStatus: (projectId) => electron.ipcRenderer.invoke("project:refresh-status", projectId),
    refreshAllStatus: () => electron.ipcRenderer.invoke("project:refresh-all-status")
  },
  // 文件系统操作
  fs: {
    selectDirectory: () => electron.ipcRenderer.invoke("fs:select-directory"),
    openDirectory: (path) => electron.ipcRenderer.invoke("fs:open-directory", path),
    openTerminal: (path) => electron.ipcRenderer.invoke("fs:open-terminal", path),
    createTempFile: (content) => electron.ipcRenderer.invoke("fs:create-temp-file", content),
    readFile: (filePath) => electron.ipcRenderer.invoke("fs:read-file", filePath),
    writeFile: (filePath, content) => electron.ipcRenderer.invoke("fs:write-file", filePath, content),
    fileExists: (filePath) => electron.ipcRenderer.invoke("fs:file-exists", filePath)
  },
  // Git操作
  git: {
    status: (path) => electron.ipcRenderer.invoke("git:status", path),
    pull: (path) => electron.ipcRenderer.invoke("git:pull", path),
    clone: (url, targetPath) => electron.ipcRenderer.invoke("git:clone", url, targetPath)
  },
  // 依赖管理
  deps: {
    check: (projectPath, projectType) => electron.ipcRenderer.invoke("deps:check", projectPath, projectType),
    install: (projectPath, projectType, dependencyName, version) => electron.ipcRenderer.invoke("deps:install", projectPath, projectType, dependencyName, version),
    installAll: (projectPath, projectType) => electron.ipcRenderer.invoke("deps:install-all", projectPath, projectType),
    uninstall: (projectPath, projectType, dependencyName) => electron.ipcRenderer.invoke("deps:uninstall", projectPath, projectType, dependencyName),
    cleanAll: (projectPath, projectType) => electron.ipcRenderer.invoke("deps:clean-all", projectPath, projectType)
  },
  // 虚拟环境
  venv: {
    checkPython: () => electron.ipcRenderer.invoke("venv:check-python"),
    list: (projectPath) => electron.ipcRenderer.invoke("venv:list", projectPath),
    create: (projectPath, envName, pythonVersion) => electron.ipcRenderer.invoke("venv:create", projectPath, envName, pythonVersion),
    activate: (projectPath, envName) => electron.ipcRenderer.invoke("venv:activate", projectPath, envName),
    delete: (projectPath, envName) => electron.ipcRenderer.invoke("venv:delete", projectPath, envName),
    installPackage: (projectPath, envName, packageName, version) => electron.ipcRenderer.invoke("venv:install-package", projectPath, envName, packageName, version),
    getPackages: (projectPath, envName) => electron.ipcRenderer.invoke("venv:get-packages", projectPath, envName)
  },
  // 配置管理
  config: {
    get: (projectPath) => electron.ipcRenderer.invoke("config:get", projectPath),
    save: (projectPath, configuration) => electron.ipcRenderer.invoke("config:save", projectPath, configuration),
    createProfile: (projectPath, profile) => electron.ipcRenderer.invoke("config:create-profile", projectPath, profile),
    updateProfile: (projectPath, profileId, updates) => electron.ipcRenderer.invoke("config:update-profile", projectPath, profileId, updates),
    deleteProfile: (projectPath, profileId) => electron.ipcRenderer.invoke("config:delete-profile", projectPath, profileId),
    getDefaultProfile: (projectPath) => electron.ipcRenderer.invoke("config:get-default-profile", projectPath),
    duplicateProfile: (projectPath, profileId, newName) => electron.ipcRenderer.invoke("config:duplicate-profile", projectPath, profileId, newName),
    export: (projectPath) => electron.ipcRenderer.invoke("config:export", projectPath),
    import: (projectPath, configJson) => electron.ipcRenderer.invoke("config:import", projectPath, configJson)
  },
  // 仓库服务
  repo: {
    getUserRepos: (platform, username, token) => electron.ipcRenderer.invoke("repo:get-user-repos", platform, username, token),
    pullRepos: (repositories, targetDirectory, onProgress) => electron.ipcRenderer.invoke("repo:pull-repos", repositories, targetDirectory, onProgress),
    clone: (repository, targetDirectory) => electron.ipcRenderer.invoke("repo:clone", repository, targetDirectory),
    filter: (repositories, filters) => electron.ipcRenderer.invoke("repo:filter", repositories, filters),
    validateGit: () => electron.ipcRenderer.invoke("repo:validate-git"),
    validateNetwork: (platform) => electron.ipcRenderer.invoke("repo:validate-network", platform),
    getDetails: (platform, fullName, token) => electron.ipcRenderer.invoke("repo:get-details", platform, fullName, token)
  },
  // 执行历史
  history: {
    get: (filter) => electron.ipcRenderer.invoke("history:get", filter),
    getRecord: (executionId) => electron.ipcRenderer.invoke("history:get-record", executionId),
    getStats: (projectId) => electron.ipcRenderer.invoke("history:get-stats", projectId),
    delete: (executionId) => electron.ipcRenderer.invoke("history:delete", executionId),
    deleteProject: (projectId) => electron.ipcRenderer.invoke("history:delete-project", projectId),
    cleanup: (daysToKeep) => electron.ipcRenderer.invoke("history:cleanup", daysToKeep),
    getRunning: () => electron.ipcRenderer.invoke("history:get-running")
  },
  // 项目日志
  logs: {
    get: (projectId, limit) => electron.ipcRenderer.invoke("logs:get", projectId, limit),
    clear: (projectId) => electron.ipcRenderer.invoke("logs:clear", projectId),
    cleanup: (projectId, keepCount) => electron.ipcRenderer.invoke("logs:cleanup", projectId, keepCount)
  },
  // 进程管理
  process: {
    sendInput: (pid, input) => electron.ipcRenderer.invoke("process:send-input", pid, input),
    kill: (pid) => electron.ipcRenderer.invoke("process:kill", pid),
    getRunning: () => electron.ipcRenderer.invoke("process:get-running")
  },
  // 系统信息
  system: {
    info: () => electron.ipcRenderer.invoke("system:info"),
    resources: (pid) => electron.ipcRenderer.invoke("system:resources", pid)
  },
  // 窗口控制
  window: {
    minimize: () => electron.ipcRenderer.invoke("window:minimize"),
    maximize: () => electron.ipcRenderer.invoke("window:maximize"),
    unmaximize: () => electron.ipcRenderer.invoke("window:unmaximize"),
    close: () => electron.ipcRenderer.invoke("window:close"),
    isMaximized: () => electron.ipcRenderer.invoke("window:is-maximized")
  },
  // 对话框
  dialog: {
    showOpenDialog: (options) => electron.ipcRenderer.invoke("dialog:showOpenDialog", options),
    showSaveDialog: (options) => electron.ipcRenderer.invoke("dialog:showSaveDialog", options),
    showMessageBox: (options) => electron.ipcRenderer.invoke("dialog:showMessageBox", options)
  },
  // 事件监听
  on: (channel, callback) => {
    electron.ipcRenderer.on(channel, (_, ...args) => callback(...args));
  },
  off: (channel, callback) => {
    electron.ipcRenderer.removeListener(channel, callback);
  },
  // 移除所有监听器
  removeAllListeners: (channel) => {
    electron.ipcRenderer.removeAllListeners(channel);
  },
  // 进程事件监听
  onProcessOutput: (listener) => {
    electron.ipcRenderer.on("process:output", (_, pid, output, type) => listener(pid, output, type));
  },
  onProcessResources: (listener) => {
    electron.ipcRenderer.on("process:resources", (_, pid, resources) => listener(pid, resources));
  },
  onProcessExit: (listener) => {
    electron.ipcRenderer.on("process:exit", (_, pid, code, signal) => listener(pid, code, signal));
  },
  onProcessError: (listener) => {
    electron.ipcRenderer.on("process:error", (_, pid, error) => listener(pid, error));
  },
  // 项目分析
  analysis: {
    analyzeFiles: (projectPath) => electron.ipcRenderer.invoke("analysis:files", projectPath),
    getEnvironment: (projectPath) => electron.ipcRenderer.invoke("analysis:environment", projectPath),
    getGitInfo: (projectPath) => electron.ipcRenderer.invoke("analysis:git", projectPath),
    detectType: (projectPath) => electron.ipcRenderer.invoke("analysis:detect-type", projectPath)
  },
  // 钱包配置
  wallet: {
    getConfig: (projectPath) => electron.ipcRenderer.invoke("wallet:get-config", projectPath),
    saveConfig: (projectPath, config) => electron.ipcRenderer.invoke("wallet:save-config", projectPath, config),
    add: (projectPath, wallet) => electron.ipcRenderer.invoke("wallet:add", projectPath, wallet),
    update: (projectPath, walletId, updates) => electron.ipcRenderer.invoke("wallet:update", projectPath, walletId, updates),
    delete: (projectPath, walletId) => electron.ipcRenderer.invoke("wallet:delete", projectPath, walletId),
    getDefault: (projectPath) => electron.ipcRenderer.invoke("wallet:get-default", projectPath),
    validate: (wallet) => electron.ipcRenderer.invoke("wallet:validate", wallet)
  },
  // 全局钱包管理
  globalWallet: {
    getConfig: () => electron.ipcRenderer.invoke("global-wallet:get-config"),
    saveConfig: (config) => electron.ipcRenderer.invoke("global-wallet:save-config", config),
    add: (wallet) => electron.ipcRenderer.invoke("global-wallet:add", wallet),
    addBatch: (wallets) => electron.ipcRenderer.invoke("global-wallet:add-batch", wallets),
    update: (walletId, updates) => electron.ipcRenderer.invoke("global-wallet:update", walletId, updates),
    delete: (walletId) => electron.ipcRenderer.invoke("global-wallet:delete", walletId),
    deleteBatch: (walletIds) => electron.ipcRenderer.invoke("global-wallet:delete-batch", walletIds),
    importFromFile: (filePath, chain) => electron.ipcRenderer.invoke("global-wallet:import-from-file", filePath, chain),
    createBatch: (count, chain) => electron.ipcRenderer.invoke("global-wallet:create-batch", count, chain),
    getAddressFromMnemonic: (mnemonic, chain, derivationPath) => electron.ipcRenderer.invoke("global-wallet:get-address-from-mnemonic", mnemonic, chain, derivationPath)
  },
  // 代理管理
  proxyManager: {
    getConfig: () => electron.ipcRenderer.invoke("proxy-manager:get-config"),
    saveConfig: (config) => electron.ipcRenderer.invoke("proxy-manager:save-config", config),
    fetchAll: () => electron.ipcRenderer.invoke("proxy-manager:fetch-all"),
    testProxy: (proxy, testUrl, timeout) => electron.ipcRenderer.invoke("proxy-manager:test-proxy", proxy, testUrl, timeout),
    testBatch: (proxies, maxConcurrent) => electron.ipcRenderer.invoke("proxy-manager:test-batch", proxies, maxConcurrent),
    delete: (proxyIds) => electron.ipcRenderer.invoke("proxy-manager:delete", proxyIds)
  },
  // 代理配置
  proxy: {
    getConfig: (projectPath) => electron.ipcRenderer.invoke("proxy:get-config", projectPath),
    saveConfig: (projectPath, config) => electron.ipcRenderer.invoke("proxy:save-config", projectPath, config),
    add: (projectPath, proxy) => electron.ipcRenderer.invoke("proxy:add", projectPath, proxy),
    update: (projectPath, proxyId, updates) => electron.ipcRenderer.invoke("proxy:update", projectPath, proxyId, updates),
    delete: (projectPath, proxyId) => electron.ipcRenderer.invoke("proxy:delete", projectPath, proxyId),
    getDefault: (projectPath) => electron.ipcRenderer.invoke("proxy:get-default", projectPath),
    test: (proxy) => electron.ipcRenderer.invoke("proxy:test", proxy),
    getSystem: () => electron.ipcRenderer.invoke("proxy:get-system")
  },
  // 性能监控
  performance: {
    start: () => electron.ipcRenderer.invoke("performance:start"),
    stop: () => electron.ipcRenderer.invoke("performance:stop"),
    getSystem: () => electron.ipcRenderer.invoke("performance:get-system"),
    getProcess: (pid) => electron.ipcRenderer.invoke("performance:get-process", pid),
    getHistory: (limit) => electron.ipcRenderer.invoke("performance:get-history", limit),
    getProcessHistory: (pid, limit) => electron.ipcRenderer.invoke("performance:get-process-history", pid, limit),
    getAlerts: (limit) => electron.ipcRenderer.invoke("performance:get-alerts", limit),
    clearAlerts: () => electron.ipcRenderer.invoke("performance:clear-alerts"),
    setThresholds: (thresholds) => electron.ipcRenderer.invoke("performance:set-thresholds", thresholds),
    getThresholds: () => electron.ipcRenderer.invoke("performance:get-thresholds"),
    startProcess: (pid, projectId) => electron.ipcRenderer.invoke("performance:start-process", pid, projectId),
    stopProcess: (pid) => electron.ipcRenderer.invoke("performance:stop-process", pid)
  },
  // 伪终端
  terminal: {
    create: (id, cwd, title) => electron.ipcRenderer.invoke("terminal:create", id, cwd, title),
    write: (id, data) => electron.ipcRenderer.invoke("terminal:write", id, data),
    resize: (id, cols, rows) => electron.ipcRenderer.invoke("terminal:resize", id, cols, rows),
    close: (id) => electron.ipcRenderer.invoke("terminal:close", id),
    minimize: (id) => electron.ipcRenderer.invoke("terminal:minimize", id),
    restore: (id) => electron.ipcRenderer.invoke("terminal:restore", id),
    getSession: (id) => electron.ipcRenderer.invoke("terminal:get-session", id),
    getAllSessions: () => electron.ipcRenderer.invoke("terminal:get-all-sessions"),
    getLogs: (id, limit) => electron.ipcRenderer.invoke("terminal:get-logs", id, limit),
    clearLogs: (id) => electron.ipcRenderer.invoke("terminal:clear-logs", id)
  },
  // 性能监控事件监听
  onSystemMetrics: (listener) => {
    electron.ipcRenderer.on("performance:system-metrics", (_, metrics) => listener(metrics));
  },
  onProcessMetrics: (listener) => {
    electron.ipcRenderer.on("performance:process-metrics", (_, metrics) => listener(metrics));
  },
  onPerformanceAlert: (listener) => {
    electron.ipcRenderer.on("performance:alert", (_, alert) => listener(alert));
  },
  // 终端事件监听
  onTerminalOutput: (listener) => {
    const handler = (_, data) => listener(data);
    electron.ipcRenderer.on("terminal:output", handler);
    return () => electron.ipcRenderer.removeListener("terminal:output", handler);
  },
  onTerminalExit: (listener) => {
    const handler = (_, data) => listener(data);
    electron.ipcRenderer.on("terminal:exit", handler);
    return () => electron.ipcRenderer.removeListener("terminal:exit", handler);
  },
  onTerminalError: (listener) => {
    const handler = (_, data) => listener(data);
    electron.ipcRenderer.on("terminal:error", handler);
    return () => electron.ipcRenderer.removeListener("terminal:error", handler);
  },
  // 批量操作
  batch: {
    execute: (type, projectIds, options) => electron.ipcRenderer.invoke("batch:execute", type, projectIds, options),
    cancel: (operationId) => electron.ipcRenderer.invoke("batch:cancel", operationId),
    get: (operationId) => electron.ipcRenderer.invoke("batch:get", operationId),
    getAll: () => electron.ipcRenderer.invoke("batch:get-all"),
    cleanup: () => electron.ipcRenderer.invoke("batch:cleanup"),
    getStats: () => electron.ipcRenderer.invoke("batch:get-stats"),
    estimateTime: (type, projectCount) => electron.ipcRenderer.invoke("batch:estimate-time", type, projectCount),
    validate: (type, projectIds) => electron.ipcRenderer.invoke("batch:validate", type, projectIds)
  },
  // 批量操作事件监听
  onBatchOperationCreated: (listener) => {
    electron.ipcRenderer.on("batch:operation-created", (_, operation) => listener(operation));
  },
  onBatchOperationStarted: (listener) => {
    electron.ipcRenderer.on("batch:operation-started", (_, operation) => listener(operation));
  },
  onBatchOperationProgress: (listener) => {
    electron.ipcRenderer.on("batch:operation-progress", (_, operation) => listener(operation));
  },
  onBatchOperationCompleted: (listener) => {
    electron.ipcRenderer.on("batch:operation-completed", (_, operation) => listener(operation));
  },
  onBatchOperationFailed: (listener) => {
    electron.ipcRenderer.on("batch:operation-failed", (_, operation, error) => listener(operation, error));
  },
  onBatchOperationCancelled: (listener) => {
    electron.ipcRenderer.on("batch:operation-cancelled", (_, operation) => listener(operation));
  },
  // Git配置
  gitConfig: {
    add: (config) => electron.ipcRenderer.invoke("git-config:add", config),
    update: (id, updates) => electron.ipcRenderer.invoke("git-config:update", id, updates),
    getAll: (filter) => electron.ipcRenderer.invoke("git-config:get-all", filter),
    get: (id) => electron.ipcRenderer.invoke("git-config:get", id),
    getDefault: () => electron.ipcRenderer.invoke("git-config:get-default"),
    delete: (id) => electron.ipcRenderer.invoke("git-config:delete", id),
    setDefault: (id) => electron.ipcRenderer.invoke("git-config:set-default", id),
    saveUsernameHistory: (username, provider) => electron.ipcRenderer.invoke("git-config:save-username-history", username, provider),
    getUsernameHistory: (provider, limit) => electron.ipcRenderer.invoke("git-config:get-username-history", provider, limit),
    cleanupUsernameHistory: (keepCount) => electron.ipcRenderer.invoke("git-config:cleanup-username-history", keepCount),
    saveTokenHistory: (provider, token, tokenName) => electron.ipcRenderer.invoke("git-config:save-token-history", provider, token, tokenName),
    getTokenHistory: (provider, limit) => electron.ipcRenderer.invoke("git-config:get-token-history", provider, limit),
    deleteTokenHistory: (provider, token) => electron.ipcRenderer.invoke("git-config:delete-token-history", provider, token),
    cleanupTokenHistory: (keepCount) => electron.ipcRenderer.invoke("git-config:cleanup-token-history", keepCount),
    saveDirectoryHistory: (directoryPath) => electron.ipcRenderer.invoke("git-config:save-directory-history", directoryPath),
    getDirectoryHistory: (limit) => electron.ipcRenderer.invoke("git-config:get-directory-history", limit),
    getLastUsedDirectory: () => electron.ipcRenderer.invoke("git-config:get-last-used-directory"),
    cleanupDirectoryHistory: (keepCount) => electron.ipcRenderer.invoke("git-config:cleanup-directory-history", keepCount)
  },
  // GitHub API
  github: {
    getUserRepositories: (username, token) => electron.ipcRenderer.invoke("github:get-user-repositories", username, token),
    getGiteeRepositories: (username, token) => electron.ipcRenderer.invoke("github:get-gitee-repositories", username, token),
    getRepositoriesByPlatform: (platform, username, token) => electron.ipcRenderer.invoke("github:get-repositories-by-platform", platform, username, token),
    getRepositoriesByToken: (platform, token) => electron.ipcRenderer.invoke("github:get-repositories-by-token", platform, token),
    cloneRepository: (repository, targetPath, useSSH) => electron.ipcRenderer.invoke("github:clone-repository", repository, targetPath, useSSH)
  },
  // Git仓库
  gitRepo: {
    getRepositories: (configId, filter) => electron.ipcRenderer.invoke("git-repo:get-repositories", configId, filter),
    pullProjects: (request) => electron.ipcRenderer.invoke("git-repo:pull-projects", request)
  },
  // 全局设置
  settings: {
    getGlobalSettings: () => electron.ipcRenderer.invoke("settings:getGlobalSettings"),
    saveGlobalSettings: (settings) => electron.ipcRenderer.invoke("settings:saveGlobalSettings", settings),
    resetGlobalSettings: () => electron.ipcRenderer.invoke("settings:resetGlobalSettings"),
    sendNotification: (type, title, body) => electron.ipcRenderer.invoke("settings:sendNotification", type, title, body),
    getConcurrencySettings: () => electron.ipcRenderer.invoke("settings:getConcurrencySettings"),
    getWindowSettings: () => electron.ipcRenderer.invoke("settings:getWindowSettings")
  },
  // Git拉取进度事件监听
  onGitPullProgress: (listener) => {
    electron.ipcRenderer.on("git-pull:progress", (_, progress) => listener(progress));
  }
};
electron.contextBridge.exposeInMainWorld("electronAPI", electronAPI);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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

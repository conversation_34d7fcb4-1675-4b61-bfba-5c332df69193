import * as fs from 'fs';
import * as path from 'path';
import { app, globalShortcut, Notification } from 'electron';

export interface GlobalSettings {
  concurrency: {
    global: number;
    proxy: number;
  };
  notifications: {
    projectStart: boolean;
    projectStop: boolean;
    projectError: boolean;
    projectSuccess: boolean;
    systemEvents: boolean;
  };
  window: {
    minimizeToTray: boolean;
    startMinimized: boolean;
    closeToTray: boolean;
  };
  shortcuts: {
    toggleWindow: string;
    openSettings: string;
    showProjects: string;
    emergencyStop: string;
  };
}

export class GlobalSettingsService {
  private readonly configDir: string;
  private readonly configPath: string;
  private settings: GlobalSettings;

  constructor() {
    this.configDir = path.join(app.getPath('userData'), 'settings');
    this.configPath = path.join(this.configDir, 'global-settings.json');
    
    // 确保配置目录存在
    if (!fs.existsSync(this.configDir)) {
      fs.mkdirSync(this.configDir, { recursive: true });
    }

    this.settings = this.loadSettings();
    this.registerShortcuts();
  }

  /**
   * 获取全局设置
   */
  async getSettings(): Promise<GlobalSettings> {
    return { ...this.settings };
  }

  /**
   * 保存全局设置
   */
  async saveSettings(newSettings: GlobalSettings): Promise<void> {
    try {
      // 验证设置
      this.validateSettings(newSettings);
      
      // 更新快捷键
      if (JSON.stringify(this.settings.shortcuts) !== JSON.stringify(newSettings.shortcuts)) {
        this.unregisterShortcuts();
        this.settings = newSettings;
        this.registerShortcuts();
      } else {
        this.settings = newSettings;
      }

      // 保存到文件
      const configJson = JSON.stringify(this.settings, null, 2);
      fs.writeFileSync(this.configPath, configJson, 'utf-8');
      
      console.log('全局设置已保存');
    } catch (error) {
      console.error('保存全局设置失败:', error);
      throw new Error(`保存全局设置失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 重置设置到默认值
   */
  async resetSettings(): Promise<void> {
    try {
      this.unregisterShortcuts();
      this.settings = this.createDefaultSettings();
      this.registerShortcuts();
      
      const configJson = JSON.stringify(this.settings, null, 2);
      fs.writeFileSync(this.configPath, configJson, 'utf-8');
      
      console.log('全局设置已重置');
    } catch (error) {
      console.error('重置全局设置失败:', error);
      throw new Error(`重置全局设置失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 发送通知
   */
  async sendNotification(type: keyof GlobalSettings['notifications'], title: string, body: string): Promise<void> {
    try {
      if (!this.settings.notifications[type]) {
        return; // 该类型通知已禁用
      }

      if (Notification.isSupported()) {
        const notification = new Notification({
          title,
          body,
          icon: this.getNotificationIcon(type)
        });
        
        notification.show();
      }
    } catch (error) {
      console.error('发送通知失败:', error);
    }
  }

  /**
   * 获取并发设置
   */
  getConcurrencySettings(): { global: number; proxy: number } {
    return { ...this.settings.concurrency };
  }

  /**
   * 获取窗口设置
   */
  getWindowSettings(): GlobalSettings['window'] {
    return { ...this.settings.window };
  }

  /**
   * 加载设置
   */
  private loadSettings(): GlobalSettings {
    try {
      if (!fs.existsSync(this.configPath)) {
        return this.createDefaultSettings();
      }

      const content = fs.readFileSync(this.configPath, 'utf-8');
      const settings = JSON.parse(content);
      
      return this.validateAndFixSettings(settings);
    } catch (error) {
      console.error('加载全局设置失败:', error);
      return this.createDefaultSettings();
    }
  }

  /**
   * 创建默认设置
   */
  private createDefaultSettings(): GlobalSettings {
    return {
      concurrency: {
        global: 5,
        proxy: 3
      },
      notifications: {
        projectStart: true,
        projectStop: true,
        projectError: true,
        projectSuccess: true,
        systemEvents: false
      },
      window: {
        minimizeToTray: true,
        startMinimized: false,
        closeToTray: true
      },
      shortcuts: {
        toggleWindow: 'CommandOrControl+Shift+W',
        openSettings: 'CommandOrControl+,',
        showProjects: 'CommandOrControl+P',
        emergencyStop: 'CommandOrControl+Shift+S'
      }
    };
  }

  /**
   * 验证并修复设置
   */
  private validateAndFixSettings(settings: any): GlobalSettings {
    const defaultSettings = this.createDefaultSettings();
    
    return {
      concurrency: {
        global: Math.max(1, Math.min(20, settings.concurrency?.global || defaultSettings.concurrency.global)),
        proxy: Math.max(1, Math.min(10, settings.concurrency?.proxy || defaultSettings.concurrency.proxy))
      },
      notifications: {
        projectStart: settings.notifications?.projectStart !== false,
        projectStop: settings.notifications?.projectStop !== false,
        projectError: settings.notifications?.projectError !== false,
        projectSuccess: settings.notifications?.projectSuccess !== false,
        systemEvents: settings.notifications?.systemEvents === true
      },
      window: {
        minimizeToTray: settings.window?.minimizeToTray !== false,
        startMinimized: settings.window?.startMinimized === true,
        closeToTray: settings.window?.closeToTray !== false
      },
      shortcuts: {
        toggleWindow: settings.shortcuts?.toggleWindow || defaultSettings.shortcuts.toggleWindow,
        openSettings: settings.shortcuts?.openSettings || defaultSettings.shortcuts.openSettings,
        showProjects: settings.shortcuts?.showProjects || defaultSettings.shortcuts.showProjects,
        emergencyStop: settings.shortcuts?.emergencyStop || defaultSettings.shortcuts.emergencyStop
      }
    };
  }

  /**
   * 验证设置
   */
  private validateSettings(settings: GlobalSettings): void {
    if (settings.concurrency.global < 1 || settings.concurrency.global > 20) {
      throw new Error('全局并发数必须在1-20之间');
    }
    
    if (settings.concurrency.proxy < 1 || settings.concurrency.proxy > 10) {
      throw new Error('代理并发数必须在1-10之间');
    }
  }

  /**
   * 注册全局快捷键
   */
  private registerShortcuts(): void {
    try {
      // 注册快捷键的逻辑将在后续实现
      console.log('全局快捷键已注册');
    } catch (error) {
      console.error('注册快捷键失败:', error);
    }
  }

  /**
   * 取消注册快捷键
   */
  private unregisterShortcuts(): void {
    try {
      globalShortcut.unregisterAll();
      console.log('全局快捷键已取消注册');
    } catch (error) {
      console.error('取消注册快捷键失败:', error);
    }
  }

  /**
   * 获取通知图标
   */
  private getNotificationIcon(type: keyof GlobalSettings['notifications']): string | undefined {
    // 根据通知类型返回相应的图标路径
    // 这里可以根据实际需要设置图标
    return undefined;
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.unregisterShortcuts();
  }
}

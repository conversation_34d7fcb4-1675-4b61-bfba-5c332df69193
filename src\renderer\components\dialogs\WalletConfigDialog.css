.wallet-config-dialog {
  width: 95vw;
  max-width: 1200px;
  max-height: 85vh;
}

.wallet-config-dialog .dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color);
}

.wallet-config-dialog .header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.wallet-config-dialog .dialog-icon {
  width: 24px;
  height: 24px;
  color: var(--primary-color);
}

.wallet-config-dialog .dialog-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.wallet-config-dialog .dialog-subtitle {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: var(--text-secondary);
}

.wallet-config-dialog .dialog-body {
  padding: 0;
  max-height: 60vh;
  overflow: hidden;
}

/* 钱包配置内容 */
.wallet-config-content {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.config-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.wallet-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.wallet-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
}

.wallet-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.wallet-card.default {
  border-color: var(--primary-color);
  background: var(--primary-color-alpha);
}

.wallet-card.disabled {
  opacity: 0.6;
  background: var(--bg-tertiary);
}

.wallet-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.wallet-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.wallet-icon {
  width: 20px;
  height: 20px;
  color: var(--primary-color);
}

.wallet-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.wallet-type {
  font-size: 12px;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.wallet-badges {
  display: flex;
  gap: 8px;
}

.badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-primary {
  background: var(--primary-color);
  color: white;
}

.badge-secondary {
  background: var(--text-tertiary);
  color: white;
}

.wallet-address {
  margin-bottom: 12px;
  padding: 8px 12px;
  background: var(--bg-tertiary);
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 12px;
}

.address-label {
  color: var(--text-secondary);
  margin-right: 8px;
}

.address-value {
  color: var(--text-primary);
  word-break: break-all;
}

.wallet-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 钱包表单 */
.wallet-form {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color);
}

.form-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.btn-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.btn-close:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.form-body {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-primary);
  font-size: 14px;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-color-alpha);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.wallet-type-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.wallet-type-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  background: var(--bg-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.wallet-type-card:hover {
  border-color: var(--primary-color);
  background: var(--bg-tertiary);
}

.wallet-type-card.selected {
  border-color: var(--primary-color);
  background: var(--primary-color-alpha);
}

.wallet-type-icon {
  width: 20px;
  height: 20px;
  color: var(--primary-color);
  flex-shrink: 0;
}

.wallet-type-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.wallet-type-label {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 14px;
}

.wallet-type-description {
  font-size: 12px;
  color: var(--text-secondary);
}

.form-help {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 6px;
  font-size: 12px;
  color: var(--warning-color);
}

.help-icon {
  width: 14px;
  height: 14px;
}

.form-checkboxes {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: var(--text-primary);
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color);
  border-radius: 3px;
  background: var(--bg-primary);
  position: relative;
  transition: all 0.2s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 10px;
  font-weight: bold;
}

.form-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid var(--border-color);
}

/* 按钮样式 */
.wallet-config-dialog .btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.wallet-config-dialog .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.wallet-config-dialog .btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.wallet-config-dialog .btn-primary {
  background: var(--primary-color);
  color: white;
}

.wallet-config-dialog .btn-primary:hover:not(:disabled) {
  background: var(--primary-color-hover);
}

.wallet-config-dialog .btn-outline {
  background: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.wallet-config-dialog .btn-outline:hover:not(:disabled) {
  background: var(--bg-tertiary);
  border-color: var(--primary-color);
}

.wallet-config-dialog .btn-danger {
  background: var(--danger-color);
  color: white;
}

.wallet-config-dialog .btn-danger:hover:not(:disabled) {
  background: var(--danger-color-hover);
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-icon {
  width: 48px;
  height: 48px;
  color: var(--text-tertiary);
  margin-bottom: 16px;
}

.empty-container h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: var(--text-primary);
}

.empty-container p {
  margin: 0 0 20px 0;
  color: var(--text-secondary);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 主题变量 */
:root {
  --primary-color-alpha: rgba(0, 122, 204, 0.1);
  --danger-color: #dc3545;
  --danger-color-hover: #c82333;
  --warning-color: #ffc107;
}

/* 暗色主题适配 */
[data-theme="dark"] .wallet-config-dialog {
  --bg-primary: #1e1e1e;
  --bg-secondary: #2a2a2a;
  --bg-tertiary: #333333;
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-tertiary: #999999;
  --border-color: #444444;
  --primary-color: #007acc;
  --primary-color-hover: #005a9e;
}

/* 亮色主题适配 */
[data-theme="light"] .wallet-config-dialog {
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --border-color: #e1e5e9;
  --primary-color: #007acc;
  --primary-color-hover: #005a9e;
}

/* 钱包表格 */
.wallet-table-container {
  flex: 1;
  overflow: auto;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--bg-secondary);
  max-height: 500px;
}

.wallet-table-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.wallet-table-container::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 4px;
}

.wallet-table-container::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

.wallet-table-container::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

.wallet-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.wallet-table thead {
  background: var(--bg-tertiary);
  position: sticky;
  top: 0;
  z-index: 1;
}

.wallet-table th {
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 2px solid var(--border-color);
  white-space: nowrap;
}

.wallet-table td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  vertical-align: middle;
}

.wallet-row:hover {
  background: var(--bg-tertiary);
}

/* 表格单元格样式 */
.wallet-name-cell {
  min-width: 150px;
}

.wallet-name-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.wallet-name {
  font-weight: 500;
  color: var(--text-primary);
}

.wallet-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.wallet-tag {
  padding: 2px 6px;
  background: var(--primary-color-alpha);
  color: var(--primary-color);
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
}

.wallet-chain-cell {
  min-width: 80px;
}

.chain-badge {
  padding: 4px 8px;
  border-radius: 4px;
  color: white;
  font-size: 11px;
  font-weight: 600;
  text-align: center;
  display: inline-block;
  min-width: 50px;
}

.wallet-address-cell {
  min-width: 200px;
  max-width: 250px;
}

.address-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.address-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: var(--text-primary);
  word-break: break-all;
  flex: 1;
}

.wallet-private-key-cell {
  min-width: 200px;
  max-width: 300px;
}

.private-key-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.private-key-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: var(--text-primary);
  word-break: break-all;
  flex: 1;
}

.private-key-actions {
  display: flex;
  gap: 4px;
}

.wallet-mnemonic-cell {
  min-width: 150px;
  max-width: 200px;
}

.mnemonic-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mnemonic-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: var(--text-primary);
  flex: 1;
}

.wallet-actions-cell {
  min-width: 120px;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

.btn-icon {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon:hover {
  background: var(--bg-tertiary);
  color: var(--primary-color);
}

.no-data {
  color: var(--text-tertiary);
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .wallet-table th,
  .wallet-table td {
    padding: 8px 12px;
    font-size: 13px;
  }

  .wallet-address-cell,
  .wallet-private-key-cell {
    max-width: 180px;
  }
}

@media (max-width: 768px) {
  .wallet-config-dialog {
    width: 98vw;
    height: 95vh;
  }

  .wallet-table-container {
    overflow-x: auto;
  }

  .wallet-table {
    min-width: 800px;
  }

  .wallet-table th,
  .wallet-table td {
    padding: 6px 8px;
    font-size: 12px;
  }
}

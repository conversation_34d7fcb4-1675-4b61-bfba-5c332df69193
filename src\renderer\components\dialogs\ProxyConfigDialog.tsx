import React, { useState, useEffect } from 'react';
import {
  IoClose,
  IoGlobe,
  IoCheckmarkCircle,
  IoSettings,
  IoShield,
  IoFlash,
  IoDocument,
  IoCopy,
  IoSpeedometer,
  IoTime
} from 'react-icons/io5';
import { useNotification } from '../../contexts/NotificationContext';
import { Project } from '@shared/types/project';
import Modal from '../Modal';
import ProxyFileEditDialog from './ProxyFileEditDialog';
import './ProxyConfigDialog.css';

interface ProxyInfo {
  id: string;
  host: string;
  port: number;
  type: 'http' | 'https' | 'socks4' | 'socks5';
  username?: string;
  password?: string;
  country?: string;
  region?: string;
  city?: string;
  isp?: string;
  anonymity?: 'transparent' | 'anonymous' | 'elite';
  speed?: number;
  uptime?: number;
  lastChecked?: Date;
  isWorking?: boolean;
  source: string;
  createdAt: Date;
  updatedAt: Date;
}

interface ProxyConfigDialogProps {
  project: Project;
  onClose: () => void;
}

const ProxyConfigDialog: React.FC<ProxyConfigDialogProps> = ({ project, onClose }) => {
  const { showSuccess, showError, showWarning } = useNotification();
  const [globalProxies, setGlobalProxies] = useState<ProxyInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showProxyFileEditor, setShowProxyFileEditor] = useState(false);
  const [systemProxy, setSystemProxy] = useState<any>(null);

  useEffect(() => {
    loadGlobalProxies();
    loadSystemProxy();
  }, []);

  const loadGlobalProxies = async () => {
    try {
      setIsLoading(true);
      const config = await window.electronAPI.proxyManager.getConfig();
      const proxies = config.proxies || [];
      setGlobalProxies(proxies);
    } catch (error) {
      console.error('加载全局代理失败:', error);
      showError('加载全局代理失败', error instanceof Error ? error.message : '未知错误');
    } finally {
      setIsLoading(false);
    }
  };

  const loadSystemProxy = async () => {
    try {
      const sysProxy = await window.electronAPI.proxy.getSystem();
      setSystemProxy(sysProxy);
    } catch (error) {
      console.error('获取系统代理失败:', error);
    }
  };

  const handleOpenProxyFileEditor = () => {
    setShowProxyFileEditor(true);
  };

  const handleCopyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      showSuccess(`${type}已复制到剪贴板`);
    } catch (error) {
      showError('复制失败', error instanceof Error ? error.message : '未知错误');
    }
  };

  const handleTestProxy = async (proxy: ProxyInfo) => {
    try {
      const result = await window.electronAPI.proxyManager.testProxy(proxy);
      if (result && result.isWorking) {
        showSuccess(`代理连接测试成功 - 速度: ${result.speed}ms`);
      } else {
        showWarning('代理连接测试失败');
      }
    } catch (error) {
      showError('代理测试失败', error instanceof Error ? error.message : '未知错误');
    }
  };

  const getProxyStatusColor = (proxy: ProxyInfo) => {
    if (proxy.isWorking === true) return '#28a745';
    if (proxy.isWorking === false) return '#dc3545';
    return '#6c757d';
  };

  const getCountryFlag = (country?: string) => {
    if (!country) return '🌍';
    const flags: { [key: string]: string } = {
      'US': '🇺🇸', 'CN': '🇨🇳', 'JP': '🇯🇵', 'KR': '🇰🇷', 'DE': '🇩🇪',
      'FR': '🇫🇷', 'GB': '🇬🇧', 'CA': '🇨🇦', 'AU': '🇦🇺', 'RU': '🇷🇺'
    };
    return flags[country.toUpperCase()] || '🌍';
  };

  const formatLastChecked = (date?: Date) => {
    if (!date) return '未知';
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}小时前`;
    const days = Math.floor(hours / 24);
    return `${days}天前`;
  };



  return (
    <>
      <Modal onClose={onClose} className="proxy-config-dialog">
        <div className="dialog-header">
          <div className="header-left">
            <IoGlobe className="dialog-icon" />
            <div>
              <h2 className="dialog-title">代理配置</h2>
              <p className="dialog-subtitle">{project.name}</p>
            </div>
          </div>
          <button className="dialog-close" onClick={onClose}>
            <IoClose />
          </button>
        </div>

        <div className="dialog-body">
          <div className="proxy-config-content">
            <div className="config-header">
              <h3>代理列表</h3>
              <button
                className="btn btn-primary btn-sm"
                onClick={handleOpenProxyFileEditor}
                title="导入代理"
              >
                <IoDocument />
                导入代理
              </button>
            </div>

            {/* 系统代理信息 */}
            {systemProxy && systemProxy.enabled && (
              <div className="system-proxy-info">
                <div className="system-proxy-header">
                  <IoSettings className="system-icon" />
                  <span>系统代理</span>
                </div>
                <div className="system-proxy-details">
                  <span>{systemProxy.host}:{systemProxy.port}</span>
                  <span className="proxy-type-badge">{systemProxy.type.toUpperCase()}</span>
                  {systemProxy.autoDetected && (
                    <span className="auto-detected-badge">自动检测</span>
                  )}
                </div>
              </div>
            )}

            {isLoading ? (
              <div className="loading-container">
                <div className="loading-spinner"></div>
                <p>正在加载代理数据...</p>
              </div>
            ) : globalProxies.length === 0 ? (
              <div className="empty-container">
                <IoGlobe className="empty-icon" />
                <h3>暂无代理数据</h3>
                <p>请先在代理管理中添加代理</p>
              </div>
            ) : (
              <div className="proxy-table-container">
                <table className="proxy-table">
                  <thead>
                    <tr>
                      <th>地址</th>
                      <th>类型</th>
                      <th>国家</th>
                      <th>状态</th>
                      <th>速度</th>
                      <th>最后检测</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {globalProxies.map((proxy) => (
                      <tr key={proxy.id} className="proxy-row">
                        <td className="proxy-address-cell">
                          <div className="address-container">
                            <span className="address-text">{proxy.host}:{proxy.port}</span>
                            <button
                              className="btn-icon"
                              onClick={() => handleCopyToClipboard(`${proxy.host}:${proxy.port}`, '代理地址')}
                              title="复制地址"
                            >
                              <IoCopy />
                            </button>
                          </div>
                        </td>
                        <td className="proxy-type-cell">
                          <span className="type-badge">{proxy.type.toUpperCase()}</span>
                        </td>
                        <td className="proxy-country-cell">
                          <span className="country-flag">{getCountryFlag(proxy.country)}</span>
                          <span className="country-name">{proxy.country || '未知'}</span>
                        </td>
                        <td className="proxy-status-cell">
                          <span
                            className="status-indicator"
                            style={{ color: getProxyStatusColor(proxy) }}
                          >
                            {proxy.isWorking === true ? '✓ 正常' :
                             proxy.isWorking === false ? '✗ 失效' : '? 未知'}
                          </span>
                        </td>
                        <td className="proxy-speed-cell">
                          {proxy.speed ? (
                            <div className="speed-container">
                              <IoSpeedometer className="speed-icon" />
                              <span>{proxy.speed}ms</span>
                            </div>
                          ) : (
                            <span className="no-data">-</span>
                          )}
                        </td>
                        <td className="proxy-time-cell">
                          <div className="time-container">
                            <IoTime className="time-icon" />
                            <span>{formatLastChecked(proxy.lastChecked)}</span>
                          </div>
                        </td>
                        <td className="proxy-actions-cell">
                          <div className="action-buttons">
                            <button
                              className="btn btn-sm btn-outline"
                              onClick={() => handleTestProxy(proxy)}
                              title="测试代理"
                            >
                              <IoCheckmarkCircle />
                            </button>
                            <button
                              className="btn btn-sm btn-outline"
                              onClick={() => handleCopyToClipboard(`${proxy.host}:${proxy.port}`, '代理地址')}
                              title="复制地址"
                            >
                              <IoCopy />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </Modal>

      {showProxyFileEditor && (
        <ProxyFileEditDialog
          project={project}
          onClose={() => setShowProxyFileEditor(false)}
        />
      )}
    </>
  );
};

export default ProxyConfigDialog;

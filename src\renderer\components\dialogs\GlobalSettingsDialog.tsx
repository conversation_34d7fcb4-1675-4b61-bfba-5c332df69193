import React, { useState, useEffect } from 'react';
import {
  IoClose,
  IoSettings,
  IoNotifications,
  IoDesktop,
  IoSpeedometer,
  IoGlobe,
  IoCheckbox,
  IoSquareOutline,
  IoSave,
  IoRefresh
} from 'react-icons/io5';
import { useNotification } from '../../contexts/NotificationContext';
import Modal from '../Modal';
import './GlobalSettingsDialog.css';

interface GlobalSettings {
  concurrency: {
    global: number;
    proxy: number;
  };
  notifications: {
    projectStart: boolean;
    projectStop: boolean;
    projectError: boolean;
    projectSuccess: boolean;
    systemEvents: boolean;
  };
  window: {
    minimizeToTray: boolean;
    startMinimized: boolean;
    closeToTray: boolean;
  };
  shortcuts: {
    toggleWindow: string;
    openSettings: string;
    showProjects: string;
    emergencyStop: string;
  };
}

interface GlobalSettingsDialogProps {
  onClose: () => void;
}

const GlobalSettingsDialog: React.FC<GlobalSettingsDialogProps> = ({ onClose }) => {
  const { showSuccess, showError } = useNotification();
  const [settings, setSettings] = useState<GlobalSettings>({
    concurrency: {
      global: 5,
      proxy: 3
    },
    notifications: {
      projectStart: true,
      projectStop: true,
      projectError: true,
      projectSuccess: true,
      systemEvents: false
    },
    window: {
      minimizeToTray: true,
      startMinimized: false,
      closeToTray: true
    },
    shortcuts: {
      toggleWindow: 'Ctrl+Shift+W',
      openSettings: 'Ctrl+,',
      showProjects: 'Ctrl+P',
      emergencyStop: 'Ctrl+Shift+S'
    }
  });
  const [isLoading, setIsLoading] = useState(true);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setIsLoading(true);
      const globalSettings = await window.electronAPI.settings.getGlobalSettings();
      if (globalSettings) {
        setSettings(globalSettings);
      }
    } catch (error) {
      console.error('加载全局设置失败:', error);
      showError('加载设置失败', error instanceof Error ? error.message : '未知错误');
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      setIsLoading(true);
      await window.electronAPI.settings.saveGlobalSettings(settings);
      setHasChanges(false);
      showSuccess('设置保存成功');
    } catch (error) {
      console.error('保存全局设置失败:', error);
      showError('保存设置失败', error instanceof Error ? error.message : '未知错误');
    } finally {
      setIsLoading(false);
    }
  };

  const resetSettings = async () => {
    if (window.confirm('确定要重置所有设置到默认值吗？')) {
      try {
        await window.electronAPI.settings.resetGlobalSettings();
        await loadSettings();
        setHasChanges(false);
        showSuccess('设置已重置');
      } catch (error) {
        showError('重置设置失败', error instanceof Error ? error.message : '未知错误');
      }
    }
  };

  const updateSettings = (path: string, value: any) => {
    setSettings(prev => {
      const newSettings = { ...prev };
      const keys = path.split('.');
      let current: any = newSettings;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      setHasChanges(true);
      return newSettings;
    });
  };

  const handleShortcutChange = (key: string, value: string) => {
    updateSettings(`shortcuts.${key}`, value);
  };

  return (
    <Modal onClose={onClose} className="global-settings-dialog">
      <div className="dialog-header">
        <div className="header-left">
          <IoSettings className="dialog-icon" />
          <div>
            <h2 className="dialog-title">全局设置</h2>
            <p className="dialog-subtitle">配置应用程序全局参数</p>
          </div>
        </div>
        <button className="dialog-close" onClick={onClose}>
          <IoClose />
        </button>
      </div>

      <div className="dialog-body">
        {isLoading ? (
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>正在加载设置...</p>
          </div>
        ) : (
          <div className="settings-layout">
            {/* 左侧导航 */}
            <div className="settings-sidebar">
              <div className="sidebar-nav">
                <div className="nav-item active">
                  <IoSpeedometer className="nav-icon" />
                  <span>性能设置</span>
                </div>
                <div className="nav-item">
                  <IoNotifications className="nav-icon" />
                  <span>通知设置</span>
                </div>
                <div className="nav-item">
                  <IoDesktop className="nav-icon" />
                  <span>窗口设置</span>
                </div>
                <div className="nav-item">
                  <IoGlobe className="nav-icon" />
                  <span>快捷键</span>
                </div>
              </div>
            </div>

            {/* 右侧内容 */}
            <div className="settings-content">
              {/* 性能设置 */}
              <div className="settings-panel active">
                <div className="panel-header">
                  <IoSpeedometer className="panel-icon" />
                  <div>
                    <h3>性能设置</h3>
                    <p>配置应用程序的并发和性能参数</p>
                  </div>
                </div>

                <div className="settings-grid">
                  <div className="setting-card">
                    <div className="card-header">
                      <h4>全局并发数</h4>
                      <span className="setting-value">{settings.concurrency.global}</span>
                    </div>
                    <p className="card-description">同时运行的最大项目数量</p>
                    <div className="slider-container">
                      <input
                        type="range"
                        min="1"
                        max="20"
                        value={settings.concurrency.global}
                        onChange={(e) => updateSettings('concurrency.global', parseInt(e.target.value))}
                        className="setting-slider"
                      />
                      <div className="slider-labels">
                        <span>1</span>
                        <span>20</span>
                      </div>
                    </div>
                  </div>

                  <div className="setting-card">
                    <div className="card-header">
                      <h4>代理并发数</h4>
                      <span className="setting-value">{settings.concurrency.proxy}</span>
                    </div>
                    <p className="card-description">每个项目使用的最大代理数量</p>
                    <div className="slider-container">
                      <input
                        type="range"
                        min="1"
                        max="10"
                        value={settings.concurrency.proxy}
                        onChange={(e) => updateSettings('concurrency.proxy', parseInt(e.target.value))}
                        className="setting-slider"
                      />
                      <div className="slider-labels">
                        <span>1</span>
                        <span>10</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            {/* 通知设置 */}
            <div className="settings-section">
              <div className="section-header">
                <IoNotifications className="section-icon" />
                <h3>桌面通知</h3>
              </div>
              <div className="setting-item">
                <label className="setting-checkbox">
                  <button
                    className="checkbox-btn"
                    onClick={() => updateSettings('notifications.projectStart', !settings.notifications.projectStart)}
                  >
                    {settings.notifications.projectStart ? <IoCheckbox /> : <IoSquareOutline />}
                  </button>
                  <span>项目启动通知</span>
                </label>
              </div>
              <div className="setting-item">
                <label className="setting-checkbox">
                  <button
                    className="checkbox-btn"
                    onClick={() => updateSettings('notifications.projectStop', !settings.notifications.projectStop)}
                  >
                    {settings.notifications.projectStop ? <IoCheckbox /> : <IoSquareOutline />}
                  </button>
                  <span>项目停止通知</span>
                </label>
              </div>
              <div className="setting-item">
                <label className="setting-checkbox">
                  <button
                    className="checkbox-btn"
                    onClick={() => updateSettings('notifications.projectError', !settings.notifications.projectError)}
                  >
                    {settings.notifications.projectError ? <IoCheckbox /> : <IoSquareOutline />}
                  </button>
                  <span>项目错误通知</span>
                </label>
              </div>
              <div className="setting-item">
                <label className="setting-checkbox">
                  <button
                    className="checkbox-btn"
                    onClick={() => updateSettings('notifications.projectSuccess', !settings.notifications.projectSuccess)}
                  >
                    {settings.notifications.projectSuccess ? <IoCheckbox /> : <IoSquareOutline />}
                  </button>
                  <span>项目成功通知</span>
                </label>
              </div>
              <div className="setting-item">
                <label className="setting-checkbox">
                  <button
                    className="checkbox-btn"
                    onClick={() => updateSettings('notifications.systemEvents', !settings.notifications.systemEvents)}
                  >
                    {settings.notifications.systemEvents ? <IoCheckbox /> : <IoSquareOutline />}
                  </button>
                  <span>系统事件通知</span>
                </label>
              </div>
            </div>

            {/* 窗口设置 */}
            <div className="settings-section">
              <div className="section-header">
                <IoDesktop className="section-icon" />
                <h3>窗口设置</h3>
              </div>
              <div className="setting-item">
                <label className="setting-checkbox">
                  <button
                    className="checkbox-btn"
                    onClick={() => updateSettings('window.minimizeToTray', !settings.window.minimizeToTray)}
                  >
                    {settings.window.minimizeToTray ? <IoCheckbox /> : <IoSquareOutline />}
                  </button>
                  <span>最小化到托盘</span>
                </label>
              </div>
              <div className="setting-item">
                <label className="setting-checkbox">
                  <button
                    className="checkbox-btn"
                    onClick={() => updateSettings('window.closeToTray', !settings.window.closeToTray)}
                  >
                    {settings.window.closeToTray ? <IoCheckbox /> : <IoSquareOutline />}
                  </button>
                  <span>关闭到托盘</span>
                </label>
              </div>
              <div className="setting-item">
                <label className="setting-checkbox">
                  <button
                    className="checkbox-btn"
                    onClick={() => updateSettings('window.startMinimized', !settings.window.startMinimized)}
                  >
                    {settings.window.startMinimized ? <IoCheckbox /> : <IoSquareOutline />}
                  </button>
                  <span>启动时最小化</span>
                </label>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="dialog-footer">
        <div className="footer-left">
          <button
            className="btn btn-outline"
            onClick={resetSettings}
            disabled={isLoading}
          >
            <IoRefresh />
            重置
          </button>
        </div>
        <div className="footer-right">
          <button
            className="btn btn-outline"
            onClick={onClose}
            disabled={isLoading}
          >
            取消
          </button>
          <button
            className="btn btn-primary"
            onClick={saveSettings}
            disabled={isLoading || !hasChanges}
          >
            <IoSave />
            保存
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default GlobalSettingsDialog;

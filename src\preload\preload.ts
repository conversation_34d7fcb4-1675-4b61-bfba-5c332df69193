import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';
import { IpcE<PERSON>s, IpcEventHandlers } from '@shared/types/ipc';

// 创建API对象
const electronAPI = {
  // 项目管理
  project: {
    list: () => ipcRenderer.invoke('project:list'),
    add: (project: any) => ipcRenderer.invoke('project:add', project),
    update: (id: string, updates: any) => ipcRenderer.invoke('project:update', id, updates),
    delete: (id: string, deleteFiles?: boolean) => ipcRenderer.invoke('project:delete', id, deleteFiles),
    start: (id: string, profileId?: string) => ipcRenderer.invoke('project:start', id, profileId),
    stop: (id: string) => ipcRenderer.invoke('project:stop', id),
    restart: (id: string) => ipcRenderer.invoke('project:restart', id),
    scan: (directories: string[]) => ipcRenderer.invoke('project:scan', directories),
    scanWithProgress: (directories: string[]) => ipcRenderer.invoke('project:scan-with-progress', directories),
    detectType: (path: string) => ipcRenderer.invoke('project:detect-type', path),
    detectProject: (path: string) => ipcRenderer.invoke('project:detect-project', path),
    refreshStatus: (projectId: string) => ipcRenderer.invoke('project:refresh-status', projectId),
    refreshAllStatus: () => ipcRenderer.invoke('project:refresh-all-status'),
  },

  // 文件系统操作
  fs: {
    selectDirectory: () => ipcRenderer.invoke('fs:select-directory'),
    openDirectory: (path: string) => ipcRenderer.invoke('fs:open-directory', path),
    openTerminal: (path: string) => ipcRenderer.invoke('fs:open-terminal', path),
    createTempFile: (content: string) => ipcRenderer.invoke('fs:create-temp-file', content),
    readFile: (filePath: string) => ipcRenderer.invoke('fs:read-file', filePath),
    writeFile: (filePath: string, content: string) => ipcRenderer.invoke('fs:write-file', filePath, content),
    fileExists: (filePath: string) => ipcRenderer.invoke('fs:file-exists', filePath),
  },

  // Git操作
  git: {
    status: (path: string) => ipcRenderer.invoke('git:status', path),
    pull: (path: string) => ipcRenderer.invoke('git:pull', path),
    clone: (url: string, targetPath: string) => ipcRenderer.invoke('git:clone', url, targetPath),
  },

  // 依赖管理
  deps: {
    check: (projectPath: string, projectType: string) => ipcRenderer.invoke('deps:check', projectPath, projectType),
    install: (projectPath: string, projectType: string, dependencyName: string, version?: string) =>
      ipcRenderer.invoke('deps:install', projectPath, projectType, dependencyName, version),
    installAll: (projectPath: string, projectType: string) =>
      ipcRenderer.invoke('deps:install-all', projectPath, projectType),
    uninstall: (projectPath: string, projectType: string, dependencyName: string) =>
      ipcRenderer.invoke('deps:uninstall', projectPath, projectType, dependencyName),
    cleanAll: (projectPath: string, projectType: string) =>
      ipcRenderer.invoke('deps:clean-all', projectPath, projectType),
  },

  // 虚拟环境
  venv: {
    checkPython: () => ipcRenderer.invoke('venv:check-python'),
    list: (projectPath: string) => ipcRenderer.invoke('venv:list', projectPath),
    create: (projectPath: string, envName?: string, pythonVersion?: string) =>
      ipcRenderer.invoke('venv:create', projectPath, envName, pythonVersion),
    activate: (projectPath: string, envName: string) => ipcRenderer.invoke('venv:activate', projectPath, envName),
    delete: (projectPath: string, envName: string) => ipcRenderer.invoke('venv:delete', projectPath, envName),
    installPackage: (projectPath: string, envName: string, packageName: string, version?: string) =>
      ipcRenderer.invoke('venv:install-package', projectPath, envName, packageName, version),
    getPackages: (projectPath: string, envName: string) =>
      ipcRenderer.invoke('venv:get-packages', projectPath, envName),
  },

  // 配置管理
  config: {
    get: (projectPath: string) => ipcRenderer.invoke('config:get', projectPath),
    save: (projectPath: string, configuration: any) => ipcRenderer.invoke('config:save', projectPath, configuration),
    createProfile: (projectPath: string, profile: any) => ipcRenderer.invoke('config:create-profile', projectPath, profile),
    updateProfile: (projectPath: string, profileId: string, updates: any) =>
      ipcRenderer.invoke('config:update-profile', projectPath, profileId, updates),
    deleteProfile: (projectPath: string, profileId: string) =>
      ipcRenderer.invoke('config:delete-profile', projectPath, profileId),
    getDefaultProfile: (projectPath: string) => ipcRenderer.invoke('config:get-default-profile', projectPath),
    duplicateProfile: (projectPath: string, profileId: string, newName: string) =>
      ipcRenderer.invoke('config:duplicate-profile', projectPath, profileId, newName),
    export: (projectPath: string) => ipcRenderer.invoke('config:export', projectPath),
    import: (projectPath: string, configJson: string) => ipcRenderer.invoke('config:import', projectPath, configJson),
  },

  // 仓库服务
  repo: {
    getUserRepos: (platform: string, username: string, token?: string) =>
      ipcRenderer.invoke('repo:get-user-repos', platform, username, token),
    pullRepos: (repositories: any[], targetDirectory: string, onProgress?: any) =>
      ipcRenderer.invoke('repo:pull-repos', repositories, targetDirectory, onProgress),
    clone: (repository: any, targetDirectory: string) =>
      ipcRenderer.invoke('repo:clone', repository, targetDirectory),
    filter: (repositories: any[], filters: any) =>
      ipcRenderer.invoke('repo:filter', repositories, filters),
    validateGit: () => ipcRenderer.invoke('repo:validate-git'),
    validateNetwork: (platform: string) => ipcRenderer.invoke('repo:validate-network', platform),
    getDetails: (platform: string, fullName: string, token?: string) =>
      ipcRenderer.invoke('repo:get-details', platform, fullName, token),
  },

  // 执行历史
  history: {
    get: (filter?: any) => ipcRenderer.invoke('history:get', filter),
    getRecord: (executionId: string) => ipcRenderer.invoke('history:get-record', executionId),
    getStats: (projectId: string) => ipcRenderer.invoke('history:get-stats', projectId),
    delete: (executionId: string) => ipcRenderer.invoke('history:delete', executionId),
    deleteProject: (projectId: string) => ipcRenderer.invoke('history:delete-project', projectId),
    cleanup: (daysToKeep: number) => ipcRenderer.invoke('history:cleanup', daysToKeep),
    getRunning: () => ipcRenderer.invoke('history:get-running'),
  },

  // 项目日志
  logs: {
    get: (projectId: string, limit?: number) => ipcRenderer.invoke('logs:get', projectId, limit),
    clear: (projectId: string) => ipcRenderer.invoke('logs:clear', projectId),
    cleanup: (projectId: string, keepCount?: number) => ipcRenderer.invoke('logs:cleanup', projectId, keepCount),
  },

  // 进程管理
  process: {
    sendInput: (pid: number, input: string) => ipcRenderer.invoke('process:send-input', pid, input),
    kill: (pid: number) => ipcRenderer.invoke('process:kill', pid),
    getRunning: () => ipcRenderer.invoke('process:get-running'),
  },

  // 系统信息
  system: {
    info: () => ipcRenderer.invoke('system:info'),
    resources: (pid: number) => ipcRenderer.invoke('system:resources', pid),
  },

  // 窗口控制
  window: {
    minimize: () => ipcRenderer.invoke('window:minimize'),
    maximize: () => ipcRenderer.invoke('window:maximize'),
    unmaximize: () => ipcRenderer.invoke('window:unmaximize'),
    close: () => ipcRenderer.invoke('window:close'),
    isMaximized: () => ipcRenderer.invoke('window:is-maximized'),
  },

  // 对话框
  dialog: {
    showOpenDialog: (options: any) => ipcRenderer.invoke('dialog:showOpenDialog', options),
    showSaveDialog: (options: any) => ipcRenderer.invoke('dialog:showSaveDialog', options),
    showMessageBox: (options: any) => ipcRenderer.invoke('dialog:showMessageBox', options),
  },

  // 事件监听
  on: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.on(channel, (_, ...args) => callback(...args));
  },

  off: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.removeListener(channel, callback);
  },

  // 移除所有监听器
  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel);
  },

  // 进程事件监听
  onProcessOutput: (listener: (pid: number, output: string, type: 'stdout' | 'stderr') => void) => {
    ipcRenderer.on('process:output', (_, pid, output, type) => listener(pid, output, type));
  },

  onProcessResources: (listener: (pid: number, resources: any) => void) => {
    ipcRenderer.on('process:resources', (_, pid, resources) => listener(pid, resources));
  },

  onProcessExit: (listener: (pid: number, code: number, signal: string) => void) => {
    ipcRenderer.on('process:exit', (_, pid, code, signal) => listener(pid, code, signal));
  },

  onProcessError: (listener: (pid: number, error: string) => void) => {
    ipcRenderer.on('process:error', (_, pid, error) => listener(pid, error));
  },

  // 项目分析
  analysis: {
    analyzeFiles: (projectPath: string) => ipcRenderer.invoke('analysis:files', projectPath),
    getEnvironment: (projectPath: string) => ipcRenderer.invoke('analysis:environment', projectPath),
    getGitInfo: (projectPath: string) => ipcRenderer.invoke('analysis:git', projectPath),
    detectType: (projectPath: string) => ipcRenderer.invoke('analysis:detect-type', projectPath),
  },

  // 钱包配置
  wallet: {
    getConfig: (projectPath: string) => ipcRenderer.invoke('wallet:get-config', projectPath),
    saveConfig: (projectPath: string, config: any) => ipcRenderer.invoke('wallet:save-config', projectPath, config),
    add: (projectPath: string, wallet: any) => ipcRenderer.invoke('wallet:add', projectPath, wallet),
    update: (projectPath: string, walletId: string, updates: any) =>
      ipcRenderer.invoke('wallet:update', projectPath, walletId, updates),
    delete: (projectPath: string, walletId: string) => ipcRenderer.invoke('wallet:delete', projectPath, walletId),
    getDefault: (projectPath: string) => ipcRenderer.invoke('wallet:get-default', projectPath),
    validate: (wallet: any) => ipcRenderer.invoke('wallet:validate', wallet),
  },

  // 全局钱包管理
  globalWallet: {
    getConfig: () => ipcRenderer.invoke('global-wallet:get-config'),
    saveConfig: (config: any) => ipcRenderer.invoke('global-wallet:save-config', config),
    add: (wallet: any) => ipcRenderer.invoke('global-wallet:add', wallet),
    addBatch: (wallets: any[]) => ipcRenderer.invoke('global-wallet:add-batch', wallets),
    update: (walletId: string, updates: any) => ipcRenderer.invoke('global-wallet:update', walletId, updates),
    delete: (walletId: string) => ipcRenderer.invoke('global-wallet:delete', walletId),
    deleteBatch: (walletIds: string[]) => ipcRenderer.invoke('global-wallet:delete-batch', walletIds),
    importFromFile: (filePath: string, chain: string) => ipcRenderer.invoke('global-wallet:import-from-file', filePath, chain),
    createBatch: (count: number, chain: string) => ipcRenderer.invoke('global-wallet:create-batch', count, chain),
    getAddressFromMnemonic: (mnemonic: string, chain: string, derivationPath?: string) =>
      ipcRenderer.invoke('global-wallet:get-address-from-mnemonic', mnemonic, chain, derivationPath),
  },

  // 代理管理
  proxyManager: {
    getConfig: () => ipcRenderer.invoke('proxy-manager:get-config'),
    saveConfig: (config: any) => ipcRenderer.invoke('proxy-manager:save-config', config),
    fetchAll: () => ipcRenderer.invoke('proxy-manager:fetch-all'),
    testProxy: (proxy: any, testUrl?: string, timeout?: number) =>
      ipcRenderer.invoke('proxy-manager:test-proxy', proxy, testUrl, timeout),
    testBatch: (proxies: any[], maxConcurrent?: number) =>
      ipcRenderer.invoke('proxy-manager:test-batch', proxies, maxConcurrent),
    delete: (proxyIds: string[]) => ipcRenderer.invoke('proxy-manager:delete', proxyIds),
  },

  // 代理配置
  proxy: {
    getConfig: (projectPath: string) => ipcRenderer.invoke('proxy:get-config', projectPath),
    saveConfig: (projectPath: string, config: any) => ipcRenderer.invoke('proxy:save-config', projectPath, config),
    add: (projectPath: string, proxy: any) => ipcRenderer.invoke('proxy:add', projectPath, proxy),
    update: (projectPath: string, proxyId: string, updates: any) =>
      ipcRenderer.invoke('proxy:update', projectPath, proxyId, updates),
    delete: (projectPath: string, proxyId: string) => ipcRenderer.invoke('proxy:delete', projectPath, proxyId),
    getDefault: (projectPath: string) => ipcRenderer.invoke('proxy:get-default', projectPath),
    test: (proxy: any) => ipcRenderer.invoke('proxy:test', proxy),
    getSystem: () => ipcRenderer.invoke('proxy:get-system'),
  },

  // 性能监控
  performance: {
    start: () => ipcRenderer.invoke('performance:start'),
    stop: () => ipcRenderer.invoke('performance:stop'),
    getSystem: () => ipcRenderer.invoke('performance:get-system'),
    getProcess: (pid: number) => ipcRenderer.invoke('performance:get-process', pid),
    getHistory: (limit?: number) => ipcRenderer.invoke('performance:get-history', limit),
    getProcessHistory: (pid: number, limit?: number) =>
      ipcRenderer.invoke('performance:get-process-history', pid, limit),
    getAlerts: (limit?: number) => ipcRenderer.invoke('performance:get-alerts', limit),
    clearAlerts: () => ipcRenderer.invoke('performance:clear-alerts'),
    setThresholds: (thresholds: any) => ipcRenderer.invoke('performance:set-thresholds', thresholds),
    getThresholds: () => ipcRenderer.invoke('performance:get-thresholds'),
    startProcess: (pid: number, projectId: string) =>
      ipcRenderer.invoke('performance:start-process', pid, projectId),
    stopProcess: (pid: number) => ipcRenderer.invoke('performance:stop-process', pid),
  },

  // 伪终端
  terminal: {
    create: (id: string, cwd?: string, title?: string) => ipcRenderer.invoke('terminal:create', id, cwd, title),
    write: (id: string, data: string) => ipcRenderer.invoke('terminal:write', id, data),
    resize: (id: string, cols: number, rows: number) => ipcRenderer.invoke('terminal:resize', id, cols, rows),
    close: (id: string) => ipcRenderer.invoke('terminal:close', id),
    minimize: (id: string) => ipcRenderer.invoke('terminal:minimize', id),
    restore: (id: string) => ipcRenderer.invoke('terminal:restore', id),
    getSession: (id: string) => ipcRenderer.invoke('terminal:get-session', id),
    getAllSessions: () => ipcRenderer.invoke('terminal:get-all-sessions'),
    getLogs: (id: string, limit?: number) => ipcRenderer.invoke('terminal:get-logs', id, limit),
    clearLogs: (id: string) => ipcRenderer.invoke('terminal:clear-logs', id),
  },

  // 性能监控事件监听
  onSystemMetrics: (listener: (metrics: any) => void) => {
    ipcRenderer.on('performance:system-metrics', (_, metrics) => listener(metrics));
  },

  onProcessMetrics: (listener: (metrics: any) => void) => {
    ipcRenderer.on('performance:process-metrics', (_, metrics) => listener(metrics));
  },

  onPerformanceAlert: (listener: (alert: any) => void) => {
    ipcRenderer.on('performance:alert', (_, alert) => listener(alert));
  },

  // 终端事件监听
  onTerminalOutput: (listener: (data: { id: string; data: string }) => void) => {
    const handler = (_: any, data: { id: string; data: string }) => listener(data);
    ipcRenderer.on('terminal:output', handler);
    return () => ipcRenderer.removeListener('terminal:output', handler);
  },

  onTerminalExit: (listener: (data: { id: string; exitCode: number }) => void) => {
    const handler = (_: any, data: { id: string; exitCode: number }) => listener(data);
    ipcRenderer.on('terminal:exit', handler);
    return () => ipcRenderer.removeListener('terminal:exit', handler);
  },

  onTerminalError: (listener: (data: { id: string; error: string }) => void) => {
    const handler = (_: any, data: { id: string; error: string }) => listener(data);
    ipcRenderer.on('terminal:error', handler);
    return () => ipcRenderer.removeListener('terminal:error', handler);
  },

  // 批量操作
  batch: {
    execute: (type: string, projectIds: string[], options?: any) =>
      ipcRenderer.invoke('batch:execute', type, projectIds, options),
    cancel: (operationId: string) => ipcRenderer.invoke('batch:cancel', operationId),
    get: (operationId: string) => ipcRenderer.invoke('batch:get', operationId),
    getAll: () => ipcRenderer.invoke('batch:get-all'),
    cleanup: () => ipcRenderer.invoke('batch:cleanup'),
    getStats: () => ipcRenderer.invoke('batch:get-stats'),
    estimateTime: (type: string, projectCount: number) =>
      ipcRenderer.invoke('batch:estimate-time', type, projectCount),
    validate: (type: string, projectIds: string[]) =>
      ipcRenderer.invoke('batch:validate', type, projectIds),
  },

  // 批量操作事件监听
  onBatchOperationCreated: (listener: (operation: any) => void) => {
    ipcRenderer.on('batch:operation-created', (_, operation) => listener(operation));
  },

  onBatchOperationStarted: (listener: (operation: any) => void) => {
    ipcRenderer.on('batch:operation-started', (_, operation) => listener(operation));
  },

  onBatchOperationProgress: (listener: (operation: any) => void) => {
    ipcRenderer.on('batch:operation-progress', (_, operation) => listener(operation));
  },

  onBatchOperationCompleted: (listener: (operation: any) => void) => {
    ipcRenderer.on('batch:operation-completed', (_, operation) => listener(operation));
  },

  onBatchOperationFailed: (listener: (operation: any, error: any) => void) => {
    ipcRenderer.on('batch:operation-failed', (_, operation, error) => listener(operation, error));
  },

  onBatchOperationCancelled: (listener: (operation: any) => void) => {
    ipcRenderer.on('batch:operation-cancelled', (_, operation) => listener(operation));
  },

  // Git配置
  gitConfig: {
    add: (config: any) => ipcRenderer.invoke('git-config:add', config),
    update: (id: string, updates: any) => ipcRenderer.invoke('git-config:update', id, updates),
    getAll: (filter?: any) => ipcRenderer.invoke('git-config:get-all', filter),
    get: (id: string) => ipcRenderer.invoke('git-config:get', id),
    getDefault: () => ipcRenderer.invoke('git-config:get-default'),
    delete: (id: string) => ipcRenderer.invoke('git-config:delete', id),
    setDefault: (id: string) => ipcRenderer.invoke('git-config:set-default', id),
    saveUsernameHistory: (username: string, provider: string) =>
      ipcRenderer.invoke('git-config:save-username-history', username, provider),
    getUsernameHistory: (provider?: string, limit?: number) =>
      ipcRenderer.invoke('git-config:get-username-history', provider, limit),
    cleanupUsernameHistory: (keepCount?: number) =>
      ipcRenderer.invoke('git-config:cleanup-username-history', keepCount),
    saveTokenHistory: (provider: string, token: string, tokenName?: string) =>
      ipcRenderer.invoke('git-config:save-token-history', provider, token, tokenName),
    getTokenHistory: (provider: string, limit?: number) =>
      ipcRenderer.invoke('git-config:get-token-history', provider, limit),
    deleteTokenHistory: (provider: string, token: string) =>
      ipcRenderer.invoke('git-config:delete-token-history', provider, token),
    cleanupTokenHistory: (keepCount?: number) =>
      ipcRenderer.invoke('git-config:cleanup-token-history', keepCount),
    saveDirectoryHistory: (directoryPath: string) =>
      ipcRenderer.invoke('git-config:save-directory-history', directoryPath),
    getDirectoryHistory: (limit?: number) =>
      ipcRenderer.invoke('git-config:get-directory-history', limit),
    getLastUsedDirectory: () =>
      ipcRenderer.invoke('git-config:get-last-used-directory'),
    cleanupDirectoryHistory: (keepCount?: number) =>
      ipcRenderer.invoke('git-config:cleanup-directory-history', keepCount),
  },

  // GitHub API
  github: {
    getUserRepositories: (username: string, token?: string) =>
      ipcRenderer.invoke('github:get-user-repositories', username, token),
    getGiteeRepositories: (username: string, token?: string) =>
      ipcRenderer.invoke('github:get-gitee-repositories', username, token),
    getRepositoriesByPlatform: (platform: 'github' | 'gitee', username: string, token?: string) =>
      ipcRenderer.invoke('github:get-repositories-by-platform', platform, username, token),
    getRepositoriesByToken: (platform: 'github' | 'gitee', token: string) =>
      ipcRenderer.invoke('github:get-repositories-by-token', platform, token),
    cloneRepository: (repository: any, targetPath: string, useSSH?: boolean) =>
      ipcRenderer.invoke('github:clone-repository', repository, targetPath, useSSH),
  },

  // Git仓库
  gitRepo: {
    getRepositories: (configId: string, filter?: any) => ipcRenderer.invoke('git-repo:get-repositories', configId, filter),
    pullProjects: (request: any) => ipcRenderer.invoke('git-repo:pull-projects', request)
  },

  // 全局设置
  settings: {
    getGlobalSettings: () => ipcRenderer.invoke('settings:getGlobalSettings'),
    saveGlobalSettings: (settings: any) => ipcRenderer.invoke('settings:saveGlobalSettings', settings),
    resetGlobalSettings: () => ipcRenderer.invoke('settings:resetGlobalSettings'),
    sendNotification: (type: string, title: string, body: string) =>
      ipcRenderer.invoke('settings:sendNotification', type, title, body),
    getConcurrencySettings: () => ipcRenderer.invoke('settings:getConcurrencySettings'),
    getWindowSettings: () => ipcRenderer.invoke('settings:getWindowSettings'),
  },

  // Git拉取进度事件监听
  onGitPullProgress: (listener: (progress: any) => void) => {
    ipcRenderer.on('git-pull:progress', (_, progress) => listener(progress));
  },
};

// 暴露API到渲染进程
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// 类型声明
declare global {
  interface Window {
    electronAPI: typeof electronAPI;
  }
}

import React, { useEffect, useState } from 'react';
import { useProject } from '../contexts/ProjectContext';
import { useNotification } from '../contexts/NotificationContext';
import ProjectToolbar from '../components/ProjectToolbar';
import ProjectTable from '../components/ProjectTable';
import NotificationContainer from '../components/NotificationContainer';
import CreateProjectDialog from '../components/CreateProjectDialog';
import ScanProgressDialog from '../components/dialogs/ScanProgressDialog';
import { ProjectPullDialog } from '../components/dialogs/ProjectPullDialog';
import ClearProjectsDialog from '../components/dialogs/ClearProjectsDialog';
import PerformancePanel from '../components/PerformancePanel';
import PerformanceMonitorDialog from '../components/dialogs/PerformanceMonitorDialog';
import BatchOperationDialog from '../components/dialogs/BatchOperationDialog';
import TerminalDialog from '../components/dialogs/TerminalDialog';
import TerminalManagerDialog from '../components/dialogs/TerminalManagerDialog';
import GlobalWalletDialog from '../components/dialogs/GlobalWalletDialog';
import ProxyManagerDialog from '../components/dialogs/ProxyManagerDialog';
import './ProjectManagementPage.css';

const ProjectManagementPage: React.FC = () => {
  const { state, actions } = useProject();
  const { showError, showSuccess } = useNotification();
  
  // 对话框状态
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showScanDialog, setShowScanDialog] = useState(false);
  const [showPullDialog, setShowPullDialog] = useState(false);
  const [showClearDialog, setShowClearDialog] = useState(false);
  const [showPerformancePanel, setShowPerformancePanel] = useState(false);
  const [showPerformanceDialog, setShowPerformanceDialog] = useState(false);
  const [showBatchOperationDialog, setShowBatchOperationDialog] = useState(false);
  const [showTerminalDialog, setShowTerminalDialog] = useState(false);
  const [terminalProject, setTerminalProject] = useState<any>(null);
  const [minimizedTerminals, setMinimizedTerminals] = useState<Map<string, string>>(new Map()); // projectId -> terminalId
  const [showTerminalManagerDialog, setShowTerminalManagerDialog] = useState(false);
  const [showGlobalWalletDialog, setShowGlobalWalletDialog] = useState(false);
  const [showProxyManagerDialog, setShowProxyManagerDialog] = useState(false);
  const [selectedProjectIds, setSelectedProjectIds] = useState<string[]>([]);
  const [scanDirectories, setScanDirectories] = useState<string[]>([]);

  // 加载项目列表
  useEffect(() => {
    actions.loadProjects().catch(error => {
      showError('加载项目失败', error.message);
    });
  }, []);

  // 处理扫描项目
  const handleScanProjects = async () => {
    try {
      const directories = await window.electronAPI.fs.selectDirectory();
      if (directories) {
        setScanDirectories([directories]);
        setShowScanDialog(true);
      }
    } catch (error) {
      showError('选择扫描目录失败', error instanceof Error ? error.message : '未知错误');
    }
  };

  // 处理扫描完成
  const handleScanComplete = async (foundProjects: number) => {
    try {
      // 重新加载项目列表
      await actions.loadProjects();
      showSuccess(`扫描完成，发现 ${foundProjects} 个项目`);
    } catch (error) {
      showError('加载项目列表失败', error instanceof Error ? error.message : '未知错误');
    }
  };

  // 处理清空列表
  const handleClearProjects = () => {
    setShowClearDialog(true);
  };

  // 确认清空列表
  const handleConfirmClear = async () => {
    try {
      // 删除所有项目（不删除文件）
      for (const project of state.projects) {
        await actions.deleteProject(project.id, false);
      }
      showSuccess('项目列表已清空');
    } catch (error) {
      showError('清空列表失败', error instanceof Error ? error.message : '未知错误');
      throw error; // 重新抛出错误，让对话框处理
    }
  };

  // 处理打开终端
  const handleOpenTerminal = async (project: any) => {
    // 检查是否有最小化的终端
    const existingTerminalId = minimizedTerminals.get(project.id);

    if (existingTerminalId) {
      // 检查终端是否还存在
      try {
        const session = await window.electronAPI.terminal.getSession(existingTerminalId);

        if (session && session.isMinimized) {
          // 恢复已存在的终端
          setTerminalProject({ ...project, terminalId: existingTerminalId });
          setShowTerminalDialog(true);
          return;
        } else {
          // 终端不存在或未最小化，清除记录
          setMinimizedTerminals(prev => {
            const newMap = new Map(prev);
            newMap.delete(project.id);
            return newMap;
          });
        }
      } catch (error) {
        // 清除无效的终端记录
        setMinimizedTerminals(prev => {
          const newMap = new Map(prev);
          newMap.delete(project.id);
          return newMap;
        });
      }
    }
    // 创建新终端
    setTerminalProject(project);
    setShowTerminalDialog(true);
  };

  const handleMinimizeTerminal = (terminalId: string) => {
    if (terminalProject) {
      // 记录最小化的终端
      setMinimizedTerminals(prev => {
        const newMap = new Map(prev);
        newMap.set(terminalProject.id, terminalId);
        return newMap;
      });
    }
    setShowTerminalDialog(false);
    setTerminalProject(null);
  };

  // 处理从终端管理器恢复终端
  const handleRestoreFromManager = (terminalId: string) => {
    // 查找对应的项目
    const projectId = Array.from(minimizedTerminals.entries())
      .find(([_, id]) => id === terminalId)?.[0];

    if (projectId) {
      const project = state.projects.find(p => p.id === projectId);
      if (project) {
        setTerminalProject({ ...project, terminalId });
        setShowTerminalDialog(true);
      }
    }
  };

  return (
    <div className="project-management-page">
      {/* 顶部工具栏 */}
      <ProjectToolbar
        onScanProjects={handleScanProjects}
        onAddProject={() => setShowAddDialog(true)}
        onPullProjects={() => setShowPullDialog(true)}
        onClearProjects={handleClearProjects}
        onShowPerformance={() => setShowPerformanceDialog(true)}
        onShowBatchOperation={() => setShowBatchOperationDialog(true)}
        onShowTerminalManager={() => setShowTerminalManagerDialog(true)}
        onShowGlobalWallet={() => setShowGlobalWalletDialog(true)}
        onShowProxyManager={() => setShowProxyManagerDialog(true)}
        selectedProjectsCount={selectedProjectIds.length}
      />

      {/* 项目表格 */}
      <div className="project-content">
        {state.isLoading ? (
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>正在加载项目...</p>
          </div>
        ) : state.error ? (
          <div className="error-container">
            <div className="error-icon">⚠️</div>
            <h3>加载失败</h3>
            <p>{state.error}</p>
            <button 
              className="btn btn-primary"
              onClick={() => actions.loadProjects()}
            >
              重试
            </button>
          </div>
        ) : state.projects.length === 0 ? (
          <div className="empty-container">
            <div className="empty-icon">📁</div>
            <h3>暂无项目</h3>
            <p>点击上方按钮添加或扫描Git项目</p>
            <div className="empty-actions">
              <button
                className="btn btn-primary"
                onClick={() => setShowAddDialog(true)}
              >
                添加项目
              </button>
              <button
                className="btn btn-outline ml-2"
                onClick={handleScanProjects}
                title="扫描包含.git文件夹的项目"
              >
                扫描Git项目
              </button>
            </div>
          </div>
        ) : (
          <ProjectTable
            projects={state.projects}
            onSelectionChange={setSelectedProjectIds}
            onOpenTerminal={handleOpenTerminal}
          />
        )}
      </div>

      {/* 对话框 */}
      <CreateProjectDialog
        isOpen={showAddDialog}
        onClose={() => setShowAddDialog(false)}
        onSubmit={async (projectData) => {
          try {
            // 转换数据格式以匹配ProjectContext期望的格式
            const project = {
              name: projectData.name,
              path: projectData.path,
              type: projectData.type as any,
              description: projectData.description,
              status: 'STOPPED' as const,
              tags: [],
              config: {
                startCommand: projectData.startCommand,
                workingDirectory: projectData.path,
                environment: {},
                profiles: []
              },
              gitStatus: {
                isRepo: false,
                branch: '',
                hasChanges: false,
                remoteUrl: ''
              },
              dependencyStatus: 'UNKNOWN' as const,
              environmentStatus: 'UNKNOWN' as const
            };

            await actions.addProject(project);
            showSuccess('项目添加成功');
          } catch (error) {
            showError('添加项目失败', error instanceof Error ? error.message : '未知错误');
          }
        }}
      />

      {showScanDialog && (
        <ScanProgressDialog
          onClose={() => setShowScanDialog(false)}
          directories={scanDirectories}
          onComplete={handleScanComplete}
        />
      )}

      {showPullDialog && (
        <ProjectPullDialog
          isOpen={showPullDialog}
          onClose={() => setShowPullDialog(false)}
          onPull={async (repositories, targetPath) => {
            try {
              let successCount = 0;
              let failedCount = 0;
              const clonedPaths: string[] = [];

              // 克隆所有仓库
              for (const repo of repositories) {
                try {
                  await window.electronAPI.github.cloneRepository(repo, targetPath, false);
                  const projectPath = `${targetPath}/${repo.name}`;
                  clonedPaths.push(projectPath);
                  successCount++;
                } catch (error) {
                  console.error(`Failed to clone ${repo.name}:`, error);
                  failedCount++;
                }
              }

              // 扫描克隆的目录并添加到项目管理器
              if (clonedPaths.length > 0) {
                console.log('Scanning cloned projects:', clonedPaths);
                await window.electronAPI.project.scan(clonedPaths);
              }

              // 重新加载项目列表
              await actions.loadProjects();

              if (failedCount === 0) {
                showSuccess(`成功拉取并添加 ${successCount} 个项目到管理器`);
              } else {
                showSuccess(`拉取完成：成功 ${successCount} 个，失败 ${failedCount} 个`);
              }
            } catch (error) {
              showError('项目拉取失败', error instanceof Error ? error.message : '未知错误');
            }
          }}
        />
      )}

      {showClearDialog && (
        <ClearProjectsDialog
          onClose={() => setShowClearDialog(false)}
          onConfirm={handleConfirmClear}
          projectCount={state.projects.length}
        />
      )}

      {/* 性能监控面板 */}
      <PerformancePanel
        isVisible={showPerformancePanel}
        onClose={() => setShowPerformancePanel(false)}
      />

      {/* 性能监控对话框 */}
      {showPerformanceDialog && (
        <PerformanceMonitorDialog
          isOpen={showPerformanceDialog}
          onClose={() => setShowPerformanceDialog(false)}
          runningProjects={state.projects.filter(p => p.status === 'RUNNING').map(p => ({
            id: p.id,
            name: p.name,
            pid: p.pid,
            status: p.status
          }))}
        />
      )}

      {/* 批量操作对话框 */}
      {showBatchOperationDialog && (
        <BatchOperationDialog
          projects={state.projects}
          selectedProjectIds={selectedProjectIds}
          onClose={() => setShowBatchOperationDialog(false)}
          onComplete={() => {
            setShowBatchOperationDialog(false);
            setSelectedProjectIds([]);
            actions.loadProjects();
          }}
        />
      )}

      {/* 终端对话框 */}
      {showTerminalDialog && terminalProject && (
        <TerminalDialog
          isOpen={showTerminalDialog}
          onClose={() => {
            setShowTerminalDialog(false);
            setTerminalProject(null);
          }}
          onMinimize={handleMinimizeTerminal}
          projectPath={terminalProject.path}
          projectName={terminalProject.name}
          terminalId={terminalProject.terminalId}
        />
      )}

      {/* 终端管理器对话框 */}
      <TerminalManagerDialog
        isOpen={showTerminalManagerDialog}
        onClose={() => setShowTerminalManagerDialog(false)}
        onRestoreTerminal={handleRestoreFromManager}
      />

      {/* 全局钱包管理对话框 */}
      {showGlobalWalletDialog && (
        <GlobalWalletDialog
          onClose={() => setShowGlobalWalletDialog(false)}
        />
      )}

      {/* 代理管理对话框 */}
      {showProxyManagerDialog && (
        <ProxyManagerDialog
          onClose={() => setShowProxyManagerDialog(false)}
        />
      )}

      {/* 通知容器 */}
      <NotificationContainer />
    </div>
  );
};

export default ProjectManagementPage;

import React, { useState, useEffect } from 'react';
import {
  IoClose,
  IoRefresh,
  IoCheckmarkCircle,
  IoCloseCircle,
  IoTime,
  IoTrash,
  IoCheckbox,
  IoSquareOutline,
  IoPlay,
  IoStop,
  IoDownload,
  IoGlobe,
  IoSpeedometer,
  IoSettings,
  IoCopy
} from 'react-icons/io5';
import { useNotification } from '../../contexts/NotificationContext';
import Modal from '../Modal';
import ProxySourcesDialog from './ProxySourcesDialog';
import './ProxyManagerDialog.css';

interface ProxyInfo {
  id: string;
  host: string;
  port: number;
  type: 'http' | 'https' | 'socks4' | 'socks5';
  username?: string;
  password?: string;
  country?: string;
  region?: string;
  city?: string;
  isp?: string;
  anonymity?: 'transparent' | 'anonymous' | 'elite';
  speed?: number;
  uptime?: number;
  lastChecked?: Date;
  isWorking?: boolean;
  source: string;
  createdAt: Date;
  updatedAt: Date;
}

interface ProxyManagerDialogProps {
  onClose: () => void;
}

const ProxyManagerDialog: React.FC<ProxyManagerDialogProps> = ({ onClose }) => {
  const { showSuccess, showError, showWarning } = useNotification();
  const [proxies, setProxies] = useState<ProxyInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isFetching, setIsFetching] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [selectedProxies, setSelectedProxies] = useState<Set<string>>(new Set());
  const [testProgress, setTestProgress] = useState({ current: 0, total: 0 });
  const [fetchProgress, setFetchProgress] = useState({ current: 0, total: 0 });
  const [currentTestingProxy, setCurrentTestingProxy] = useState<string>('');
  const [sortBy, setSortBy] = useState<keyof ProxyInfo>('lastChecked');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [filterType, setFilterType] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [showSourcesDialog, setShowSourcesDialog] = useState(false);

  useEffect(() => {
    loadProxies();

    // 监听测试进度更新
    const handleTestProgress = (progress: any) => {
      setTestProgress({ current: progress.completed, total: progress.total });
      setCurrentTestingProxy(`${progress.current?.host}:${progress.current?.port}`);

      // 实时更新代理列表
      if (progress.current) {
        setProxies(prev => prev.map(p =>
          p.host === progress.current.host && p.port === progress.current.port
            ? { ...p, ...progress.current }
            : p
        ));
      }
    };

    // 注册进度监听器 (使用正确的API)
    if (window.electronAPI?.on) {
      window.electronAPI.on('proxy-test-progress', handleTestProgress);

      return () => {
        if (window.electronAPI?.off) {
          window.electronAPI.off('proxy-test-progress', handleTestProgress);
        }
      };
    }
  }, []);

  const loadProxies = async () => {
    try {
      setIsLoading(true);
      const config = await window.electronAPI.proxyManager.getConfig();
      setProxies(config.proxies || []);
    } catch (error) {
      console.error('加载代理失败:', error);
      showError('加载代理失败', error instanceof Error ? error.message : '未知错误');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFetchProxies = async () => {
    setShowSourcesDialog(true);
  };

  const handleFetchWithSources = async (sources: any[]) => {
    try {
      setShowSourcesDialog(false);
      setIsFetching(true);
      const enabledSources = sources.filter(s => s.enabled);
      setFetchProgress({ current: 0, total: enabledSources.length });
      showSuccess(`🚀 开始超高速并发拉取 ${enabledSources.length} 个全球最快代理源...`);

      // 先保存代理源配置
      const config = await window.electronAPI.proxyManager.getConfig();
      config.sources = sources;
      await window.electronAPI.proxyManager.saveConfig(config);

      const startTime = Date.now();
      const newProxies = await window.electronAPI.proxyManager.fetchAll();
      const endTime = Date.now();
      const duration = ((endTime - startTime) / 1000).toFixed(1);

      setProxies(newProxies);

      const workingCount = newProxies.filter(p => p.isWorking === true).length;
      const unknownCount = newProxies.filter(p => p.isWorking === undefined).length;
      const failedCount = newProxies.filter(p => p.isWorking === false).length;

      showSuccess(`拉取完成! 用时${duration}秒，获得${newProxies.length}个优质代理 (已验证可用: ${workingCount}, 待测试: ${unknownCount})`);
    } catch (error) {
      showError('拉取代理失败', error instanceof Error ? error.message : '未知错误');
    } finally {
      setIsFetching(false);
      setFetchProgress({ current: 0, total: 0 });
    }
  };

  const handleTestSelected = async () => {
    if (selectedProxies.size === 0) {
      showWarning('请选择要测试的代理');
      return;
    }

    try {
      setIsTesting(true);
      const selectedProxyList = proxies.filter(p => selectedProxies.has(p.id));
      setTestProgress({ current: 0, total: selectedProxyList.length });

      showSuccess(`🚀 开始快速测试 ${selectedProxyList.length} 个代理，自动清理失败代理...`);
      const startTime = Date.now();

      const testedProxies = await window.electronAPI.proxyManager.testBatch(selectedProxyList, 10);

      const endTime = Date.now();
      const duration = ((endTime - startTime) / 1000).toFixed(1);

      // 重新加载代理列表（因为失败的代理已被自动删除）
      await loadProxies();

      const originalCount = selectedProxyList.length;
      const remainingCount = testedProxies.length;
      const deletedCount = originalCount - remainingCount;

      showSuccess(`🎉 快速测试完成! 用时${duration}秒，保留: ${remainingCount}, 已清理: ${deletedCount}`);
    } catch (error) {
      showError('测试代理失败', error instanceof Error ? error.message : '未知错误');
    } finally {
      setIsTesting(false);
      setTestProgress({ current: 0, total: 0 });
      setCurrentTestingProxy('');
    }
  };

  const handleTestSingle = async (proxy: ProxyInfo) => {
    try {
      const isWorking = await window.electronAPI.proxyManager.testProxy(proxy);
      
      // 更新单个代理状态
      setProxies(prev => prev.map(p => 
        p.id === proxy.id 
          ? { ...p, isWorking, lastChecked: new Date() }
          : p
      ));
      
      showSuccess(`代理 ${proxy.host}:${proxy.port} ${isWorking ? '可用' : '不可用'}`);
    } catch (error) {
      showError('测试代理失败', error instanceof Error ? error.message : '未知错误');
    }
  };

  const handleDeleteSelected = async () => {
    if (selectedProxies.size === 0) {
      showWarning('请选择要删除的代理');
      return;
    }

    try {
      await window.electronAPI.proxyManager.delete(Array.from(selectedProxies));
      await loadProxies();
      setSelectedProxies(new Set());
      showSuccess(`已删除 ${selectedProxies.size} 个代理`);
    } catch (error) {
      showError('删除代理失败', error instanceof Error ? error.message : '未知错误');
    }
  };

  const handleDeduplicateProxies = async () => {
    try {
      const originalCount = proxies.length;

      // 创建去重映射
      const proxyMap = new Map<string, any>();
      proxies.forEach(proxy => {
        const key = `${proxy.host}:${proxy.port}`;
        if (!proxyMap.has(key)) {
          proxyMap.set(key, proxy);
        }
      });

      const uniqueProxies = Array.from(proxyMap.values());
      const duplicateCount = originalCount - uniqueProxies.length;

      if (duplicateCount === 0) {
        showSuccess('没有发现重复的代理');
        return;
      }

      // 保存去重后的代理
      const config = await window.electronAPI.proxyManager.getConfig();
      config.proxies = uniqueProxies;
      await window.electronAPI.proxyManager.saveConfig(config);

      setProxies(uniqueProxies);
      setSelectedProxies(new Set());

      showSuccess(`去重完成! 删除了 ${duplicateCount} 个重复代理，保留 ${uniqueProxies.length} 个`);
    } catch (error) {
      showError('去重失败', error instanceof Error ? error.message : '未知错误');
    }
  };

  const handleSelectProxy = (proxyId: string) => {
    const newSelected = new Set(selectedProxies);
    if (newSelected.has(proxyId)) {
      newSelected.delete(proxyId);
    } else {
      newSelected.add(proxyId);
    }
    setSelectedProxies(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedProxies.size === filteredProxies.length) {
      setSelectedProxies(new Set());
    } else {
      setSelectedProxies(new Set(filteredProxies.map(p => p.id)));
    }
  };

  const handleSort = (field: keyof ProxyInfo) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  // 过滤和排序代理
  const filteredProxies = proxies
    .filter(proxy => {
      if (filterType !== 'all' && proxy.type !== filterType) return false;
      if (filterStatus === 'working' && !proxy.isWorking) return false;
      if (filterStatus === 'failed' && proxy.isWorking !== false) return false;
      return true;
    })
    .sort((a, b) => {
      const aVal = a[sortBy];
      const bVal = b[sortBy];
      
      if (aVal === undefined && bVal === undefined) return 0;
      if (aVal === undefined) return 1;
      if (bVal === undefined) return -1;
      
      let comparison = 0;
      if (typeof aVal === 'string' && typeof bVal === 'string') {
        comparison = aVal.localeCompare(bVal);
      } else if (typeof aVal === 'number' && typeof bVal === 'number') {
        comparison = aVal - bVal;
      } else if (aVal instanceof Date && bVal instanceof Date) {
        comparison = aVal.getTime() - bVal.getTime();
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });

  const getStatusIcon = (proxy: ProxyInfo) => {
    if (proxy.isWorking === true) {
      return <IoCheckmarkCircle className="status-icon working" />;
    } else if (proxy.isWorking === false) {
      return <IoCloseCircle className="status-icon failed" />;
    } else {
      return <IoTime className="status-icon unknown" />;
    }
  };

  const getAnonymityBadge = (anonymity?: string) => {
    if (!anonymity) return null;
    
    const colors = {
      transparent: '#dc3545',
      anonymous: '#ffc107',
      elite: '#28a745'
    };
    
    return (
      <span 
        className="anonymity-badge"
        style={{ backgroundColor: colors[anonymity as keyof typeof colors] }}
      >
        {anonymity.toUpperCase()}
      </span>
    );
  };

  const formatSpeed = (speed?: number, isWorking?: boolean) => {
    if (!speed) {
      if (isWorking === false) return '失败';
      if (isWorking === undefined) return '未测试';
      return '-';
    }
    return `${speed}ms`;
  };

  const formatUptime = (uptime?: number) => {
    if (!uptime) return '-';
    return `${uptime.toFixed(1)}%`;
  };

  const formatLastChecked = (date?: Date) => {
    if (!date) return '未测试';
    return new Date(date).toLocaleString();
  };

  const formatSourceName = (source: string) => {
    // 缩短来源名称显示
    const sourceMap: { [key: string]: string } = {
      'TheSpeedX (4.4k⭐ 每日更新)': 'TheSpeedX',
      'ErcinDedeoglu (每日更新+测试)': 'ErcinDedeoglu',
      'Monosans (每小时更新)': 'Monosans',
      'Clarketm (每日更新)': 'Clarketm',
      'Anonym0usWork1221 (每2小时更新)': 'Anonym0us',
      'ProxyScrape API (高速)': 'ProxyScrape',
      'Geonode API (按速度排序)': 'Geonode',
      'Free Proxy List API': 'FreeProxy',
      'Zaeem20 (快速)': 'Zaeem20',
      'Proxifly (验证过的)': 'Proxifly',
      // 兼容旧的名称
      'Geonode API (全球最快)': 'Geonode',
      'GitHub TheSpeedX (每日更新)': 'TheSpeedX',
      'GitHub ErcinDedeoglu (每日更新)': 'ErcinDedeoglu',
      'GitHub Monosans (高质量)': 'Monosans',
      'GitHub Clarketm (稳定)': 'Clarketm'
    };

    // 如果有映射就使用短名称，否则截取前10个字符
    return sourceMap[source] || (source.length > 10 ? source.substring(0, 10) + '...' : source);
  };

  return (
    <Modal onClose={onClose} className="proxy-manager-dialog">
      <div className="dialog-header">
        <div className="header-left">
          <IoGlobe className="dialog-icon" />
          <div>
            <h2 className="dialog-title">代理管理</h2>
            <p className="dialog-subtitle">管理和测试代理服务器</p>
          </div>
        </div>
        <button className="dialog-close" onClick={onClose}>
          <IoClose />
        </button>
      </div>

      <div className="dialog-body">
        <div className="proxy-toolbar">
          <div className="toolbar-left">
            <h3>代理列表 ({filteredProxies.length})</h3>
            {isTesting && (
              <div className="test-progress">
                ⚡ 快速测试: {testProgress.current}/{testProgress.total}
                {currentTestingProxy && <span className="current-proxy">当前: {currentTestingProxy}</span>}
                <span className="auto-clean">自动清理失败代理</span>
              </div>
            )}
            {isFetching && (
              <div className="fetch-progress">
                🚀 并发拉取中: {fetchProgress.total} 个源同时进行 (每源最多30个优质代理)
              </div>
            )}
          </div>
          
          <div className="toolbar-center">
            <div className="filters">
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="filter-select"
              >
                <option value="all">所有类型</option>
                <option value="http">HTTP</option>
                <option value="https">HTTPS</option>
                <option value="socks4">SOCKS4</option>
                <option value="socks5">SOCKS5</option>
              </select>
              
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="filter-select"
              >
                <option value="all">所有状态</option>
                <option value="working">可用</option>
                <option value="failed">不可用</option>
              </select>
            </div>
          </div>
          
          <div className="toolbar-right">
            {selectedProxies.size > 0 && (
              <>
                <button
                  className="btn btn-primary btn-sm"
                  onClick={handleTestSelected}
                  disabled={isTesting}
                >
                  <IoPlay />
                  测试选中 ({selectedProxies.size})
                </button>
                <button
                  className="btn btn-danger btn-sm"
                  onClick={handleDeleteSelected}
                >
                  <IoTrash />
                  删除选中
                </button>
              </>
            )}
            
            <button
              className="btn btn-success btn-sm"
              onClick={handleFetchProxies}
              disabled={isFetching}
            >
              <IoDownload />
              {isFetching ? '拉取中...' : '配置并拉取'}
            </button>

            <button
              className="btn btn-info btn-sm"
              onClick={() => setShowSourcesDialog(true)}
            >
              <IoSettings />
              代理源配置
            </button>

            <button
              className="btn btn-warning btn-sm"
              onClick={handleDeduplicateProxies}
            >
              <IoCopy />
              去重
            </button>

            <button
              className="btn btn-outline btn-sm"
              onClick={loadProxies}
              disabled={isLoading}
            >
              <IoRefresh />
              刷新
            </button>
          </div>
        </div>

        {isLoading ? (
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>正在加载代理...</p>
          </div>
        ) : filteredProxies.length === 0 ? (
          <div className="empty-container">
            <IoGlobe className="empty-icon" />
            <h3>暂无代理</h3>
            <p>点击"拉取代理"按钮获取最新的代理列表</p>
          </div>
        ) : (
          <div className="proxy-table-container">
            <table className="proxy-table">
              <thead>
                <tr>
                  <th className="select-column">
                    <button className="checkbox-btn" onClick={handleSelectAll}>
                      {selectedProxies.size === filteredProxies.length ? (
                        <IoCheckbox className="checkbox-icon checked" />
                      ) : (
                        <IoSquareOutline className="checkbox-icon" />
                      )}
                    </button>
                  </th>
                  <th className="status-column" onClick={() => handleSort('isWorking')}>
                    状态
                  </th>
                  <th className="address-column" onClick={() => handleSort('host')}>
                    地址:端口
                  </th>
                  <th className="type-column" onClick={() => handleSort('type')}>
                    类型
                  </th>
                  <th className="location-column" onClick={() => handleSort('country')}>
                    位置
                  </th>
                  <th className="anonymity-column" onClick={() => handleSort('anonymity')}>
                    匿名性
                  </th>
                  <th className="speed-column" onClick={() => handleSort('speed')}>
                    速度
                  </th>
                  <th className="uptime-column" onClick={() => handleSort('uptime')}>
                    在线率
                  </th>
                  <th className="source-column" onClick={() => handleSort('source')}>
                    来源
                  </th>
                  <th className="checked-column" onClick={() => handleSort('lastChecked')}>
                    最后检测
                  </th>
                  <th className="actions-column">操作</th>
                </tr>
              </thead>
              <tbody>
                {filteredProxies.map(proxy => (
                  <tr
                    key={proxy.id}
                    className={`proxy-row ${selectedProxies.has(proxy.id) ? 'selected' : ''}`}
                  >
                    <td className="select-cell">
                      <button
                        className="checkbox-btn"
                        onClick={() => handleSelectProxy(proxy.id)}
                      >
                        {selectedProxies.has(proxy.id) ? (
                          <IoCheckbox className="checkbox-icon checked" />
                        ) : (
                          <IoSquareOutline className="checkbox-icon" />
                        )}
                      </button>
                    </td>
                    
                    <td className="status-cell">
                      {getStatusIcon(proxy)}
                    </td>
                    
                    <td className="address-cell">
                      <span className="proxy-address">
                        {proxy.host}:{proxy.port}
                      </span>
                    </td>
                    
                    <td className="type-cell">
                      <span className="proxy-type">{proxy.type.toUpperCase()}</span>
                    </td>
                    
                    <td className="location-cell">
                      <div className="location-info">
                        {proxy.country && <span className="country">{proxy.country}</span>}
                        {proxy.region && <span className="region">{proxy.region}</span>}
                        {proxy.city && <span className="city">{proxy.city}</span>}
                      </div>
                    </td>
                    
                    <td className="anonymity-cell">
                      {getAnonymityBadge(proxy.anonymity)}
                    </td>
                    
                    <td className="speed-cell">
                      <span className={`speed-value ${proxy.isWorking === false ? 'failed' : proxy.isWorking === undefined ? 'unknown' : 'working'}`}>
                        {formatSpeed(proxy.speed, proxy.isWorking)}
                      </span>
                    </td>
                    
                    <td className="uptime-cell">
                      <span className="uptime-value">{formatUptime(proxy.uptime)}</span>
                    </td>
                    
                    <td className="source-cell">
                      <span className="source-name" title={proxy.source}>
                        {formatSourceName(proxy.source)}
                      </span>
                    </td>
                    
                    <td className="checked-cell">
                      <span className="checked-time">{formatLastChecked(proxy.lastChecked)}</span>
                    </td>
                    
                    <td className="actions-cell">
                      <button
                        className="btn btn-primary btn-xs"
                        onClick={() => handleTestSingle(proxy)}
                        title="测试代理"
                      >
                        <IoSpeedometer />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* 代理源配置对话框 */}
      {showSourcesDialog && (
        <ProxySourcesDialog
          onClose={() => setShowSourcesDialog(false)}
          onConfirm={handleFetchWithSources}
        />
      )}
    </Modal>
  );
};

export default ProxyManagerDialog;

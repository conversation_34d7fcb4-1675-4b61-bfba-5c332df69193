"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const electron = require("electron");
const path = require("path");
const uuid = require("uuid");
const fs = require("fs");
const Database = require("better-sqlite3");
const child_process = require("child_process");
const simpleGit = require("simple-git");
const events = require("events");
const util = require("util");
const iconv = require("iconv-lite");
const axios = require("axios");
const crypto = require("crypto");
const ethers = require("ethers");
const web3_js = require("@solana/web3.js");
const bip39 = require("bip39");
const hdkey = require("hdkey");
const httpsProxyAgent = require("https-proxy-agent");
const os = require("os");
const pty = require("node-pty");
function _interopNamespaceDefault(e) {
  const n = Object.create(null, { [Symbol.toStringTag]: { value: "Module" } });
  if (e) {
    for (const k in e) {
      if (k !== "default") {
        const d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: () => e[k]
        });
      }
    }
  }
  n.default = e;
  return Object.freeze(n);
}
const path__namespace = /* @__PURE__ */ _interopNamespaceDefault(path);
const fs__namespace = /* @__PURE__ */ _interopNamespaceDefault(fs);
const iconv__namespace = /* @__PURE__ */ _interopNamespaceDefault(iconv);
const crypto__namespace = /* @__PURE__ */ _interopNamespaceDefault(crypto);
const bip39__namespace = /* @__PURE__ */ _interopNamespaceDefault(bip39);
const hdkey__namespace = /* @__PURE__ */ _interopNamespaceDefault(hdkey);
const os__namespace = /* @__PURE__ */ _interopNamespaceDefault(os);
const pty__namespace = /* @__PURE__ */ _interopNamespaceDefault(pty);
class ProjectService {
  constructor(databaseService, fileSystemService, gitService, processService) {
    this.databaseService = databaseService;
    this.fileSystemService = fileSystemService;
    this.gitService = gitService;
    this.processService = processService;
    this.projects = /* @__PURE__ */ new Map();
  }
  setProjectLogService(projectLogService) {
    this.projectLogService = projectLogService;
  }
  setExecutionHistoryService(executionHistoryService) {
    this.executionHistoryService = executionHistoryService;
  }
  async loadProjects() {
    try {
      const projects = await this.databaseService.getAllProjects();
      for (const project of projects) {
        if (project.status !== "STOPPED") {
          project.status = "STOPPED";
          project.pid = void 0;
          await this.databaseService.saveProject(project);
        }
        this.projects.set(project.id, project);
      }
    } catch (error) {
      console.error("Failed to load projects:", error);
    }
  }
  async listProjects() {
    return Array.from(this.projects.values());
  }
  async addProject(projectData) {
    const project = {
      ...projectData,
      id: uuid.v4(),
      createdAt: /* @__PURE__ */ new Date(),
      updatedAt: /* @__PURE__ */ new Date()
    };
    if (!project.type || project.type === "other") {
      project.type = await this.detectProjectType(project.path);
    }
    project.gitStatus = await this.gitService.getStatus(project.path);
    project.dependencyStatus = await this.checkDependencyStatus(project.path, project.type);
    project.environmentStatus = await this.checkEnvironmentStatus(project.path, project.type);
    await this.databaseService.saveProject(project);
    this.projects.set(project.id, project);
    return project;
  }
  async updateProject(id, updates) {
    const project = this.projects.get(id);
    if (!project) {
      throw new Error(`Project with id ${id} not found`);
    }
    const updatedProject = {
      ...project,
      ...updates,
      updatedAt: /* @__PURE__ */ new Date()
    };
    await this.databaseService.saveProject(updatedProject);
    this.projects.set(id, updatedProject);
    return updatedProject;
  }
  /**
   * 迁移Python项目配置，清理硬编码的python命令
   */
  async migratePythonProjectConfigs() {
    try {
      console.log("开始迁移Python项目配置...");
      const projects = Array.from(this.projects.values());
      for (const project of projects) {
        if (project.type === "python") {
          let needsUpdate = false;
          if (project.config.profiles) {
            for (const profile of project.config.profiles) {
              if (profile.command && (profile.command.startsWith("python ") || profile.command.startsWith("python3 ") || profile.command.startsWith("py "))) {
                console.log(`清理项目 ${project.name} 的硬编码Python命令: ${profile.command}`);
                profile.command = "";
                needsUpdate = true;
              }
            }
          }
          if (project.config.startCommand && (project.config.startCommand.startsWith("python ") || project.config.startCommand.startsWith("python3 ") || project.config.startCommand.startsWith("py "))) {
            console.log(`清理项目 ${project.name} 的硬编码startCommand: ${project.config.startCommand}`);
            project.config.startCommand = "";
            needsUpdate = true;
          }
          if (needsUpdate) {
            await this.updateProject(project.id, { config: project.config });
            console.log(`已更新项目 ${project.name} 的Python配置`);
          }
        }
      }
      console.log("Python项目配置迁移完成");
      await this.refreshAllProjectsStatus();
    } catch (error) {
      console.error("Python项目配置迁移失败:", error);
    }
  }
  /**
   * 刷新所有项目的环境和依赖状态
   */
  async refreshAllProjectsStatus() {
    try {
      console.log("开始刷新所有项目状态...");
      const projects = Array.from(this.projects.values());
      for (const project of projects) {
        await this.refreshProjectStatus(project.id);
      }
      console.log("所有项目状态刷新完成");
    } catch (error) {
      console.error("刷新项目状态失败:", error);
    }
  }
  /**
   * 刷新单个项目的环境和依赖状态
   */
  async refreshProjectStatus(projectId) {
    try {
      const project = this.projects.get(projectId);
      if (!project) {
        return;
      }
      console.log(`刷新项目 ${project.name} 的状态...`);
      const newDependencyStatus = await this.checkDependencyStatus(project.path, project.type);
      const newEnvironmentStatus = await this.checkEnvironmentStatus(project.path, project.type);
      if (project.dependencyStatus !== newDependencyStatus || project.environmentStatus !== newEnvironmentStatus) {
        console.log(`项目 ${project.name} 状态更新: 依赖 ${project.dependencyStatus} -> ${newDependencyStatus}, 环境 ${project.environmentStatus} -> ${newEnvironmentStatus}`);
        await this.updateProject(projectId, {
          dependencyStatus: newDependencyStatus,
          environmentStatus: newEnvironmentStatus
        });
      }
    } catch (error) {
      console.error(`刷新项目 ${projectId} 状态失败:`, error);
    }
  }
  async deleteProject(id, deleteFiles = false) {
    const project = this.projects.get(id);
    if (!project) {
      throw new Error(`Project with id ${id} not found`);
    }
    if (project.status === "RUNNING") {
      await this.stopProject(id);
    }
    if (deleteFiles && fs__namespace.existsSync(project.path)) {
      await this.fileSystemService.deleteDirectory(project.path);
    }
    await this.databaseService.deleteProject(id);
    this.projects.delete(id);
  }
  async startProject(id, profileId) {
    var _a, _b;
    const project = this.projects.get(id);
    if (!project) {
      throw new Error(`Project with id ${id} not found`);
    }
    console.log(`Starting project ${id}, current status: ${project.status}, pid: ${project.pid}`);
    if (project.status === "RUNNING") {
      if (project.pid && !this.processService.isProcessRunning(project.pid)) {
        console.log(`Process ${project.pid} not running, resetting status to STOPPED`);
        project.status = "STOPPED";
        project.pid = void 0;
        await this.updateProjectStatus(id, "STOPPED");
      } else if (project.pid && this.processService.isProcessRunning(project.pid)) {
        throw new Error("Project is already running");
      }
    }
    if (project.status === "STARTING" || project.status === "STOPPING") {
      throw new Error(`Project is ${project.status.toLowerCase()}, please wait`);
    }
    project.status = "STARTING";
    await this.updateProjectStatus(id, "STARTING");
    let executionId;
    try {
      const profile = ((_a = project.config.profiles) == null ? void 0 : _a.find((p) => p.id === profileId)) || ((_b = project.config.profiles) == null ? void 0 : _b[0]);
      let command = (profile == null ? void 0 : profile.command) || project.config.startCommand;
      if (!command || command.trim() === "") {
        command = this.getDefaultStartCommand(project.type, project.path);
      }
      const args = (profile == null ? void 0 : profile.args) || [];
      const environment = { ...project.config.environment || {}, ...(profile == null ? void 0 : profile.env) || {} };
      if (this.executionHistoryService) {
        executionId = await this.executionHistoryService.recordExecutionStart(
          id,
          command,
          args,
          project.path,
          environment,
          profileId
        );
      }
      const pid = await this.processService.startProject(project, profileId);
      console.log(`Project ${id} started with PID ${pid}`);
      if (this.executionHistoryService && executionId) {
        await this.executionHistoryService.updateExecutionPid(executionId, pid);
      }
      project.status = "RUNNING";
      project.pid = pid;
      await this.updateProjectStatus(id, "RUNNING", pid);
      try {
        const { ipcMain } = require("electron");
        ipcMain.emit("performance:start-process-monitoring", null, pid, id);
      } catch (error) {
        console.warn("启动性能监控失败:", error);
      }
      this.processService.onProcessExit(pid, async (exitCode, signal) => {
        console.log(`Process ${pid} exited with code ${exitCode}, signal ${signal}`);
        if (this.executionHistoryService && executionId) {
          const status = exitCode === 0 ? "success" : signal ? "interrupted" : "failed";
          await this.executionHistoryService.recordExecutionEnd(executionId, status, exitCode);
        }
        project.status = "STOPPED";
        const stoppedPid = project.pid;
        project.pid = void 0;
        await this.updateProjectStatus(id, "STOPPED");
        if (stoppedPid) {
          try {
            const { ipcMain } = require("electron");
            ipcMain.emit("performance:stop-process-monitoring", null, stoppedPid);
          } catch (error) {
            console.warn("停止性能监控失败:", error);
          }
        }
      });
      this.processService.onProcessOutput(pid, async (output, type) => {
        if (this.projectLogService) {
          await this.projectLogService.addLog(id, type, output, type === "stderr" ? "error" : "info", pid);
        }
        this.sendToRenderer("project:output", id, output, type);
      });
      this.processService.onProcessError(pid, async (error) => {
        console.error(`Process ${pid} error:`, error);
        if (this.executionHistoryService && executionId) {
          await this.executionHistoryService.recordExecutionEnd(executionId, "failed", -1, "", String(error));
        }
        project.status = "ERROR";
        project.pid = void 0;
        await this.updateProjectStatus(id, "ERROR");
      });
    } catch (error) {
      console.error(`Failed to start project ${id}:`, error);
      if (this.executionHistoryService && executionId) {
        await this.executionHistoryService.recordExecutionEnd(executionId, "failed", -1, "", error instanceof Error ? error.message : String(error));
      }
      project.status = "ERROR";
      project.pid = void 0;
      await this.updateProjectStatus(id, "ERROR");
      throw error;
    }
  }
  async stopProject(id) {
    const project = this.projects.get(id);
    if (!project) {
      throw new Error(`Project with id ${id} not found`);
    }
    console.log(`Stopping project ${id}, current status: ${project.status}, pid: ${project.pid}`);
    if (project.status === "STOPPED") {
      console.log(`Project ${id} is already stopped`);
      return;
    }
    if (project.status === "STOPPING") {
      throw new Error("Project is already stopping");
    }
    if (project.status !== "RUNNING" && project.status !== "STARTING") {
      if (project.pid && this.processService.isProcessRunning(project.pid)) {
        console.log(`Project ${id} has running process ${project.pid}, stopping it`);
      } else {
        project.status = "STOPPED";
        project.pid = void 0;
        await this.updateProjectStatus(id, "STOPPED");
        return;
      }
    }
    if (project.pid && !this.processService.isProcessRunning(project.pid)) {
      console.log(`Process ${project.pid} not running, setting status to STOPPED`);
      project.status = "STOPPED";
      project.pid = void 0;
      await this.updateProjectStatus(id, "STOPPED");
      return;
    }
    project.status = "STOPPING";
    await this.updateProjectStatus(id, "STOPPING");
    try {
      if (project.pid) {
        await this.processService.stopProcess(project.pid);
        console.log(`Successfully stopped process ${project.pid}`);
      }
      project.status = "STOPPED";
      project.pid = void 0;
      await this.updateProjectStatus(id, "STOPPED");
    } catch (error) {
      console.error(`Failed to stop project ${id}:`, error);
      project.status = "ERROR";
      await this.updateProjectStatus(id, "ERROR");
      throw error;
    }
  }
  async restartProject(id) {
    await this.stopProject(id);
    await new Promise((resolve) => setTimeout(resolve, 1e3));
    await this.startProject(id);
  }
  /**
   * 处理进程结束事件
   */
  handleProcessEnded(pid, projectId) {
    try {
      const project = this.projects.get(projectId);
      if (project && project.pid === pid) {
        project.status = "STOPPED";
        project.pid = void 0;
        this.updateProjectStatus(projectId, "STOPPED").catch((error) => {
          console.error("Failed to update project status in database:", error);
        });
        this.sendToRenderer("project:status-changed", {
          id: projectId,
          status: "STOPPED",
          pid: void 0
        });
      }
    } catch (error) {
      console.error("Failed to handle process ended:", error);
    }
  }
  async detectProjectType(projectPath) {
    const detectionRules = {
      python: {
        files: ["requirements.txt", "setup.py", "pyproject.toml", "Pipfile"],
        extensions: [".py"],
        priority: 1
      },
      nodejs: {
        files: ["package.json", "yarn.lock", "pnpm-lock.yaml"],
        extensions: [".js", ".ts", ".jsx", ".tsx"],
        priority: 1
      },
      shell: {
        files: ["run.sh", "start.sh", "build.sh"],
        extensions: [".sh", ".bash"],
        priority: 2
      },
      docker: {
        files: ["Dockerfile", "docker-compose.yml", "docker-compose.yaml"],
        priority: 3
      }
    };
    let detectedType = "other";
    let highestPriority = 999;
    for (const [type, rules] of Object.entries(detectionRules)) {
      if (rules.priority >= highestPriority) continue;
      const hasRequiredFiles = rules.files.some(
        (file) => fs__namespace.existsSync(path__namespace.join(projectPath, file))
      );
      if (hasRequiredFiles) {
        detectedType = type;
        highestPriority = rules.priority;
        continue;
      }
      if ("extensions" in rules && rules.extensions) {
        const hasExtensions = this.hasFilesWithExtensions(projectPath, rules.extensions);
        if (hasExtensions) {
          detectedType = type;
          highestPriority = rules.priority;
        }
      }
    }
    return detectedType;
  }
  getDefaultStartCommand(projectType, projectPath) {
    switch (projectType) {
      case "nodejs":
        try {
          const packageJsonPath = path__namespace.join(projectPath, "package.json");
          if (fs__namespace.existsSync(packageJsonPath)) {
            const packageJson = JSON.parse(fs__namespace.readFileSync(packageJsonPath, "utf8"));
            if (packageJson.scripts) {
              if (packageJson.scripts.dev) return "npm run dev";
              if (packageJson.scripts.start) return "npm start";
              if (packageJson.scripts.serve) return "npm run serve";
            }
          }
        } catch (error) {
          console.warn("Failed to read package.json:", error);
        }
        return "npm start";
      case "python":
        return "";
      case "shell":
        const shellFiles = ["start.sh", "run.sh", "build.sh"];
        for (const file of shellFiles) {
          if (fs__namespace.existsSync(path__namespace.join(projectPath, file))) {
            return `bash ${file}`;
          }
        }
        return "bash start.sh";
      case "docker":
        if (fs__namespace.existsSync(path__namespace.join(projectPath, "docker-compose.yml")) || fs__namespace.existsSync(path__namespace.join(projectPath, "docker-compose.yaml"))) {
          return "docker-compose up";
        }
        return "docker build -t app . && docker run app";
      default:
        return 'echo "请配置启动命令"';
    }
  }
  hasFilesWithExtensions(dirPath, extensions) {
    try {
      const files = fs__namespace.readdirSync(dirPath);
      return files.some((file) => {
        const ext = path__namespace.extname(file);
        return extensions.includes(ext);
      });
    } catch {
      return false;
    }
  }
  async checkDependencyStatus(projectPath, type) {
    try {
      switch (type) {
        case "python":
          return await this.checkPythonDependencies(projectPath);
        case "nodejs":
          return await this.checkNodejsDependencies(projectPath);
        case "docker":
          return await this.checkDockerDependencies(projectPath);
        default:
          return "UNKNOWN";
      }
    } catch (error) {
      console.error("Error checking dependency status:", error);
      return "UNKNOWN";
    }
  }
  async checkPythonDependencies(projectPath) {
    const requirementsPath = path__namespace.join(projectPath, "requirements.txt");
    const pipfilePath = path__namespace.join(projectPath, "Pipfile");
    const pyprojectPath = path__namespace.join(projectPath, "pyproject.toml");
    if (fs__namespace.existsSync(requirementsPath) || fs__namespace.existsSync(pipfilePath) || fs__namespace.existsSync(pyprojectPath)) {
      const venvPath = path__namespace.join(projectPath, "venv");
      const envPath = path__namespace.join(projectPath, ".env");
      if (fs__namespace.existsSync(venvPath) || fs__namespace.existsSync(envPath)) {
        return "INSTALLED";
      } else {
        return "MISSING";
      }
    }
    return "UNKNOWN";
  }
  async checkNodejsDependencies(projectPath) {
    const packageJsonPath = path__namespace.join(projectPath, "package.json");
    if (fs__namespace.existsSync(packageJsonPath)) {
      const nodeModulesPath = path__namespace.join(projectPath, "node_modules");
      if (fs__namespace.existsSync(nodeModulesPath)) {
        return "INSTALLED";
      } else {
        return "MISSING";
      }
    }
    return "UNKNOWN";
  }
  async checkDockerDependencies(projectPath) {
    const dockerfilePath = path__namespace.join(projectPath, "Dockerfile");
    const dockerComposePath = path__namespace.join(projectPath, "docker-compose.yml");
    const dockerComposeYamlPath = path__namespace.join(projectPath, "docker-compose.yaml");
    if (fs__namespace.existsSync(dockerfilePath) || fs__namespace.existsSync(dockerComposePath) || fs__namespace.existsSync(dockerComposeYamlPath)) {
      try {
        await this.processService.executeCommand("docker --version");
        return "INSTALLED";
      } catch {
        return "MISSING";
      }
    }
    return "UNKNOWN";
  }
  async checkEnvironmentStatus(projectPath, type) {
    try {
      switch (type) {
        case "python":
          return await this.checkPythonEnvironment(projectPath);
        case "nodejs":
          return await this.checkNodejsEnvironment(projectPath);
        case "docker":
          return await this.checkDockerEnvironment(projectPath);
        default:
          return "UNKNOWN";
      }
    } catch (error) {
      console.error("Error checking environment status:", error);
      return "UNKNOWN";
    }
  }
  async checkPythonEnvironment(projectPath) {
    try {
      const venvNames = ["venv", ".venv", "env", ".env", "virtualenv"];
      let hasVirtualEnv = false;
      for (const venvName of venvNames) {
        const venvPath = path__namespace.join(projectPath, venvName);
        if (fs__namespace.existsSync(venvPath)) {
          const pythonPath = process.platform === "win32" ? path__namespace.join(venvPath, "Scripts", "python.exe") : path__namespace.join(venvPath, "bin", "python");
          if (fs__namespace.existsSync(pythonPath)) {
            hasVirtualEnv = true;
            console.log(`项目 ${projectPath} 找到虚拟环境: ${venvPath}`);
            break;
          }
        }
      }
      if (hasVirtualEnv) {
        return "READY";
      }
      try {
        await this.processService.executeCommand("python --version", { cwd: projectPath });
        return "READY";
      } catch {
        try {
          await this.processService.executeCommand("python3 --version", { cwd: projectPath });
          return "READY";
        } catch {
          try {
            await this.processService.executeCommand("py --version", { cwd: projectPath });
            return "READY";
          } catch {
            return "NOT_READY";
          }
        }
      }
    } catch (error) {
      console.error("检查Python环境失败:", error);
      return "NOT_READY";
    }
  }
  async checkNodejsEnvironment(projectPath) {
    try {
      await this.processService.executeCommand("node --version", { cwd: projectPath });
      return "READY";
    } catch {
      return "NOT_READY";
    }
  }
  async checkDockerEnvironment(projectPath) {
    try {
      await this.processService.executeCommand("docker --version", { cwd: projectPath });
      return "READY";
    } catch {
      return "NOT_READY";
    }
  }
  async updateProjectStatus(id, status, pid) {
    const updates = { status };
    if (pid !== void 0) {
      updates.pid = pid;
    }
    try {
      await this.updateProject(id, updates);
    } catch (error) {
      console.error(`Failed to update project ${id} in database:`, error);
    }
    this.sendToRenderer("project:status-changed", id, status, pid);
    console.log(`Project ${id} status updated to ${status}${pid ? ` with PID ${pid}` : ""}`);
  }
  sendToRenderer(event, ...args) {
    try {
      const { BrowserWindow } = require("electron");
      const mainWindow = BrowserWindow.getAllWindows()[0];
      if (mainWindow) {
        mainWindow.webContents.send(event, ...args);
      }
    } catch (error) {
      console.warn("Failed to send to renderer:", error);
    }
  }
  async scanProjects(directories) {
    console.log("Starting project scan for directories:", directories);
    let totalFound = 0;
    for (const directory of directories) {
      console.log(`Scanning directory: ${directory}`);
      const beforeCount = this.projects.size;
      await this.scanDirectory(directory);
      const afterCount = this.projects.size;
      const foundInDir = afterCount - beforeCount;
      totalFound += foundInDir;
      console.log(`Found ${foundInDir} Git projects in ${directory}`);
    }
    console.log(`Scan completed. Total Git projects found: ${totalFound}`);
  }
  async scanProjectsWithProgress(directories) {
    console.log("Starting project scan with progress for directories:", directories);
    this.sendScanLog("info", "开始扫描项目...");
    let totalFound = 0;
    let totalScanned = 0;
    let totalDirectories = 0;
    for (const directory of directories) {
      try {
        const subDirs = await this.countDirectories(directory);
        totalDirectories += subDirs;
      } catch (error) {
        this.sendScanLog("warning", `无法访问目录: ${directory}`);
      }
    }
    this.sendScanProgress({
      currentPath: "",
      scannedCount: 0,
      totalCount: totalDirectories,
      foundProjects: 0
    });
    for (const directory of directories) {
      this.sendScanLog("info", `开始扫描目录: ${directory}`);
      try {
        const beforeCount = this.projects.size;
        const scannedInDir = await this.scanDirectoryWithProgress(directory, totalScanned, totalDirectories);
        totalScanned += scannedInDir;
        const afterCount = this.projects.size;
        const foundInDir = afterCount - beforeCount;
        totalFound += foundInDir;
        this.sendScanLog("success", `在 ${directory} 中发现 ${foundInDir} 个Git项目`);
      } catch (error) {
        this.sendScanLog("error", `扫描目录失败 ${directory}: ${error instanceof Error ? error.message : "未知错误"}`);
      }
    }
    this.sendScanComplete(totalFound);
    this.sendScanLog("success", `扫描完成！总共发现 ${totalFound} 个Git项目`);
  }
  async countDirectories(dirPath, maxDepth = 1, currentDepth = 0) {
    if (currentDepth >= maxDepth) return 0;
    try {
      const entries = fs__namespace.readdirSync(dirPath, { withFileTypes: true });
      let count = 0;
      for (const entry of entries) {
        if (entry.isDirectory() && !entry.name.startsWith(".") && entry.name !== "node_modules") {
          count++;
        }
      }
      return count;
    } catch {
      return 0;
    }
  }
  async scanDirectoryWithProgress(dirPath, currentScanned, totalDirectories, maxDepth = 1, currentDepth = 0) {
    if (currentDepth >= maxDepth) return 0;
    let scannedCount = 0;
    try {
      const entries = fs__namespace.readdirSync(dirPath, { withFileTypes: true });
      for (const entry of entries) {
        if (entry.isDirectory() && !entry.name.startsWith(".") && entry.name !== "node_modules") {
          const subDirPath = path__namespace.join(dirPath, entry.name);
          scannedCount++;
          this.sendScanProgress({
            currentPath: subDirPath,
            scannedCount: currentScanned + scannedCount,
            totalCount: totalDirectories,
            foundProjects: this.projects.size
          });
          const gitPath = path__namespace.join(subDirPath, ".git");
          const hasGitFolder = fs__namespace.existsSync(gitPath);
          if (hasGitFolder) {
            const existingProject = Array.from(this.projects.values()).find((p) => p.path === subDirPath);
            if (!existingProject) {
              try {
                const projectType = await this.detectProjectType(subDirPath);
                const projectName = path__namespace.basename(subDirPath);
                const gitStatus = await this.gitService.getStatus(subDirPath);
                const defaultStartCommand = this.getDefaultStartCommand(projectType, subDirPath);
                await this.addProject({
                  name: projectName,
                  path: subDirPath,
                  type: projectType,
                  description: `自动扫描发现的Git项目`,
                  status: "STOPPED",
                  tags: ["auto-scanned", "git-repo"],
                  config: {
                    startCommand: defaultStartCommand,
                    workingDirectory: subDirPath,
                    environment: {},
                    profiles: []
                  },
                  gitStatus,
                  dependencyStatus: "UNKNOWN",
                  environmentStatus: "UNKNOWN"
                });
                this.sendScanLog("success", `发现Git项目: ${projectName}`);
              } catch (error) {
                this.sendScanLog("warning", `处理项目失败 ${subDirPath}: ${error instanceof Error ? error.message : "未知错误"}`);
              }
            } else {
              this.sendScanLog("info", `跳过已存在的项目: ${path__namespace.basename(subDirPath)}`);
            }
          }
          await new Promise((resolve) => setTimeout(resolve, 10));
        }
      }
    } catch (error) {
      this.sendScanLog("error", `扫描目录失败 ${dirPath}: ${error instanceof Error ? error.message : "未知错误"}`);
    }
    return scannedCount;
  }
  sendScanProgress(data) {
    try {
      const { BrowserWindow } = require("electron");
      const mainWindow = BrowserWindow.getAllWindows()[0];
      if (mainWindow) {
        mainWindow.webContents.send("scan:progress", data);
      }
    } catch (error) {
      console.warn("Failed to send scan progress:", error);
    }
  }
  sendScanLog(type, message) {
    try {
      const { BrowserWindow } = require("electron");
      const mainWindow = BrowserWindow.getAllWindows()[0];
      if (mainWindow) {
        mainWindow.webContents.send("scan:log", {
          type,
          message,
          timestamp: /* @__PURE__ */ new Date()
        });
      }
    } catch (error) {
      console.warn("Failed to send scan log:", error);
    }
  }
  sendScanComplete(foundProjects) {
    try {
      const { BrowserWindow } = require("electron");
      const mainWindow = BrowserWindow.getAllWindows()[0];
      if (mainWindow) {
        mainWindow.webContents.send("scan:complete", { foundProjects });
      }
    } catch (error) {
      console.warn("Failed to send scan complete:", error);
    }
  }
  async scanDirectory(dirPath, maxDepth = 1, currentDepth = 0) {
    if (currentDepth >= maxDepth) return;
    try {
      await this.checkAndAddGitProject(dirPath);
      const entries = fs__namespace.readdirSync(dirPath, { withFileTypes: true });
      for (const entry of entries) {
        if (entry.isDirectory() && !entry.name.startsWith(".") && entry.name !== "node_modules") {
          const subDirPath = path__namespace.join(dirPath, entry.name);
          await this.checkAndAddGitProject(subDirPath);
          if (currentDepth < maxDepth - 1) {
            await this.scanDirectory(subDirPath, maxDepth, currentDepth + 1);
          }
        }
      }
    } catch (error) {
      console.warn(`Failed to scan directory ${dirPath}:`, error);
    }
  }
  /**
   * 检查并添加Git项目
   */
  async checkAndAddGitProject(dirPath) {
    try {
      const gitPath = path__namespace.join(dirPath, ".git");
      const hasGitFolder = fs__namespace.existsSync(gitPath);
      if (hasGitFolder) {
        const existingProject = Array.from(this.projects.values()).find((p) => p.path === dirPath);
        if (!existingProject) {
          console.log(`Found Git project directory: ${dirPath}`);
          const projectType = await this.detectProjectType(dirPath);
          const projectName = path__namespace.basename(dirPath);
          const gitStatus = await this.gitService.getStatus(dirPath);
          const defaultStartCommand = this.getDefaultStartCommand(projectType, dirPath);
          await this.addProject({
            name: projectName,
            path: dirPath,
            type: projectType,
            description: `自动扫描发现的Git项目`,
            status: "STOPPED",
            tags: ["auto-scanned", "git-repo"],
            config: {
              startCommand: defaultStartCommand,
              workingDirectory: dirPath,
              environment: {},
              profiles: []
            },
            gitStatus,
            dependencyStatus: "UNKNOWN",
            environmentStatus: "UNKNOWN"
          });
          console.log(`Successfully added Git project: ${projectName} at ${dirPath}`);
        } else {
          console.log(`Git project already exists: ${dirPath}`);
        }
      }
    } catch (error) {
      console.warn(`Failed to check Git project at ${dirPath}:`, error);
    }
  }
}
class DatabaseService {
  constructor() {
    this.db = null;
    const userDataPath = electron.app.getPath("userData");
    this.dbPath = path__namespace.join(userDataPath, "projects.db");
  }
  async initialize() {
    try {
      console.log(`Initializing database at: ${this.dbPath}`);
      this.db = new Database(this.dbPath);
      await this.createTables();
      console.log("Database initialized successfully");
    } catch (error) {
      console.error("Failed to initialize database:", error);
      throw error;
    }
  }
  async createTables() {
    const createProjectsTable = `
      CREATE TABLE IF NOT EXISTS projects (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        path TEXT NOT NULL UNIQUE,
        description TEXT,
        type TEXT NOT NULL,
        status TEXT DEFAULT 'STOPPED',
        tags TEXT,
        groupId TEXT,
        config TEXT,
        dependencyStatus TEXT DEFAULT 'UNKNOWN',
        environmentStatus TEXT DEFAULT 'UNKNOWN',
        gitStatus TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;
    const createExecutionLogsTable = `
      CREATE TABLE IF NOT EXISTS execution_logs (
        id TEXT PRIMARY KEY,
        project_id TEXT,
        start_time DATETIME,
        end_time DATETIME,
        status TEXT,
        exit_code INTEGER,
        output TEXT,
        error TEXT,
        FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
      )
    `;
    const createIndexes = [
      "CREATE INDEX IF NOT EXISTS idx_projects_type ON projects(type)",
      "CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status)",
      "CREATE INDEX IF NOT EXISTS idx_execution_logs_project_id ON execution_logs(project_id)",
      "CREATE INDEX IF NOT EXISTS idx_execution_logs_start_time ON execution_logs(start_time)"
    ];
    try {
      this.db.exec(createProjectsTable);
      this.db.exec(createExecutionLogsTable);
      createIndexes.forEach((indexSql) => {
        this.db.exec(indexSql);
      });
    } catch (error) {
      throw error;
    }
  }
  async getAllProjects() {
    try {
      const sql = "SELECT * FROM projects ORDER BY updated_at DESC";
      const stmt = this.db.prepare(sql);
      const rows = stmt.all();
      const projects = rows.map((row) => this.rowToProject(row));
      return projects;
    } catch (error) {
      throw error;
    }
  }
  async getProject(id) {
    try {
      const sql = "SELECT * FROM projects WHERE id = ?";
      const stmt = this.db.prepare(sql);
      const row = stmt.get(id);
      return row ? this.rowToProject(row) : null;
    } catch (error) {
      throw error;
    }
  }
  async saveProject(project) {
    try {
      const sql = `
        INSERT OR REPLACE INTO projects
        (id, name, path, description, type, status, tags, groupId, config,
         dependencyStatus, environmentStatus, gitStatus, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      const params = [
        project.id,
        project.name,
        project.path,
        project.description,
        project.type,
        project.status,
        JSON.stringify(project.tags),
        project.groupId,
        JSON.stringify(project.config),
        project.dependencyStatus,
        project.environmentStatus,
        JSON.stringify(project.gitStatus),
        project.createdAt.toISOString(),
        project.updatedAt.toISOString()
      ];
      const stmt = this.db.prepare(sql);
      stmt.run(params);
    } catch (error) {
      throw error;
    }
  }
  async deleteProject(id) {
    try {
      const transaction = this.db.transaction(() => {
        this.db.prepare("DELETE FROM execution_logs WHERE project_id = ?").run(id);
        this.db.prepare("DELETE FROM projects WHERE id = ?").run(id);
      });
      transaction();
    } catch (error) {
      throw error;
    }
  }
  rowToProject(row) {
    return {
      id: row.id,
      name: row.name,
      path: row.path,
      description: row.description,
      type: row.type,
      status: row.status,
      tags: JSON.parse(row.tags || "[]"),
      groupId: row.groupId,
      config: JSON.parse(row.config || "{}"),
      dependencyStatus: row.dependencyStatus,
      environmentStatus: row.environmentStatus,
      gitStatus: JSON.parse(row.gitStatus || '{"isRepo": false}'),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }
  /**
   * 执行SQL语句（INSERT, UPDATE, DELETE）
   */
  async run(sql, params = []) {
    try {
      if (!this.db) {
        throw new Error("Database not initialized");
      }
      const stmt = this.db.prepare(sql);
      const result = stmt.run(params);
      return {
        changes: result.changes,
        lastInsertRowid: Number(result.lastInsertRowid)
      };
    } catch (error) {
      throw error;
    }
  }
  /**
   * 获取单行数据
   */
  async get(sql, params = []) {
    try {
      if (!this.db) {
        throw new Error("Database not initialized");
      }
      const stmt = this.db.prepare(sql);
      return stmt.get(params);
    } catch (error) {
      throw error;
    }
  }
  /**
   * 获取多行数据
   */
  async all(sql, params = []) {
    try {
      if (!this.db) {
        throw new Error("Database not initialized");
      }
      const stmt = this.db.prepare(sql);
      return stmt.all(params);
    } catch (error) {
      throw error;
    }
  }
  /**
   * 执行事务
   */
  async transaction(callback) {
    try {
      if (!this.db) {
        throw new Error("Database not initialized");
      }
      const transaction = this.db.transaction(callback);
      transaction();
    } catch (error) {
      throw error;
    }
  }
  async close() {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}
class FileSystemService {
  async selectDirectory() {
    const result = await electron.dialog.showOpenDialog({
      properties: ["openDirectory"],
      title: "选择项目目录"
    });
    if (result.canceled || result.filePaths.length === 0) {
      return null;
    }
    return result.filePaths[0];
  }
  async openDirectory(dirPath) {
    await electron.shell.openPath(dirPath);
  }
  async openTerminal(dirPath) {
    const platform = process.platform;
    try {
      switch (platform) {
        case "win32":
          child_process.spawn("cmd", ["/c", "start", "cmd"], { cwd: dirPath, detached: true });
          break;
        case "darwin":
          child_process.spawn("open", ["-a", "Terminal", dirPath], { detached: true });
          break;
        case "linux":
          const terminals = ["gnome-terminal", "konsole", "xterm", "terminator"];
          for (const terminal of terminals) {
            try {
              child_process.spawn(terminal, ["--working-directory", dirPath], { detached: true });
              break;
            } catch {
              continue;
            }
          }
          break;
        default:
          throw new Error(`Unsupported platform: ${platform}`);
      }
    } catch (error) {
      console.error("Failed to open terminal:", error);
      throw error;
    }
  }
  async deleteDirectory(dirPath) {
    return new Promise((resolve, reject) => {
      fs__namespace.rm(dirPath, { recursive: true, force: true }, (err) => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }
  async ensureDirectory(dirPath) {
    return new Promise((resolve, reject) => {
      fs__namespace.mkdir(dirPath, { recursive: true }, (err) => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }
  async readFile(filePath) {
    return new Promise((resolve, reject) => {
      fs__namespace.readFile(filePath, "utf8", (err, data) => {
        if (err) {
          reject(err);
        } else {
          resolve(data);
        }
      });
    });
  }
  async writeFile(filePath, content) {
    return new Promise((resolve, reject) => {
      fs__namespace.writeFile(filePath, content, "utf8", (err) => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }
  async fileExists(filePath) {
    return new Promise((resolve) => {
      fs__namespace.access(filePath, fs__namespace.constants.F_OK, (err) => {
        resolve(!err);
      });
    });
  }
  async getFileStats(filePath) {
    return new Promise((resolve) => {
      fs__namespace.stat(filePath, (err, stats) => {
        if (err) {
          resolve(null);
        } else {
          resolve(stats);
        }
      });
    });
  }
  async listDirectory(dirPath) {
    return new Promise((resolve, reject) => {
      fs__namespace.readdir(dirPath, (err, files) => {
        if (err) {
          reject(err);
        } else {
          resolve(files);
        }
      });
    });
  }
  async scanDirectoryRecursive(dirPath, maxDepth = 3, currentDepth = 0) {
    if (currentDepth >= maxDepth) {
      return [];
    }
    const results = [];
    try {
      const items = await this.listDirectory(dirPath);
      for (const item of items) {
        const itemPath = path__namespace.join(dirPath, item);
        const stats = await this.getFileStats(itemPath);
        if (stats == null ? void 0 : stats.isDirectory()) {
          if (this.shouldSkipDirectory(item)) {
            continue;
          }
          results.push(itemPath);
          const subResults = await this.scanDirectoryRecursive(
            itemPath,
            maxDepth,
            currentDepth + 1
          );
          results.push(...subResults);
        }
      }
    } catch (error) {
      console.warn(`Failed to scan directory ${dirPath}:`, error);
    }
    return results;
  }
  shouldSkipDirectory(dirName) {
    const skipDirs = [
      "node_modules",
      ".git",
      ".svn",
      ".hg",
      "__pycache__",
      ".pytest_cache",
      ".venv",
      "venv",
      "env",
      "dist",
      "build",
      ".next",
      ".nuxt",
      "target",
      "bin",
      "obj",
      ".vs",
      ".vscode",
      ".idea"
    ];
    return skipDirs.includes(dirName) || dirName.startsWith(".");
  }
}
class GitService {
  async getStatus(projectPath) {
    var _a;
    try {
      const gitDir = path__namespace.join(projectPath, ".git");
      if (!fs__namespace.existsSync(gitDir)) {
        return { isRepo: false };
      }
      const git = simpleGit.simpleGit(projectPath);
      const branch = await git.branch();
      const currentBranch = branch.current;
      const status = await git.status();
      const hasChanges = status.files.length > 0;
      let remoteUrl = "";
      try {
        const remotes = await git.getRemotes(true);
        const origin = remotes.find((remote) => remote.name === "origin");
        remoteUrl = ((_a = origin == null ? void 0 : origin.refs) == null ? void 0 : _a.fetch) || "";
      } catch {
      }
      let gitStatus = "clean";
      if (status.conflicted.length > 0) {
        gitStatus = "conflict";
      } else if (hasChanges) {
        gitStatus = "dirty";
      }
      return {
        isRepo: true,
        branch: currentBranch,
        hasChanges,
        remoteUrl,
        status: gitStatus
      };
    } catch (error) {
      console.error("Failed to get git status:", error);
      return { isRepo: false };
    }
  }
  async pull(projectPath) {
    try {
      const git = simpleGit.simpleGit(projectPath);
      await git.pull();
    } catch (error) {
      console.error("Failed to pull git repository:", error);
      throw new Error(`Git pull failed: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
  async clone(repoUrl, targetPath) {
    try {
      const git = simpleGit.simpleGit();
      await git.clone(repoUrl, targetPath);
    } catch (error) {
      console.error("Failed to clone git repository:", error);
      throw new Error(`Git clone failed: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
  async getBranches(projectPath) {
    try {
      const git = simpleGit.simpleGit(projectPath);
      const branches = await git.branch();
      return branches.all;
    } catch (error) {
      console.error("Failed to get git branches:", error);
      return [];
    }
  }
  async checkout(projectPath, branch) {
    try {
      const git = simpleGit.simpleGit(projectPath);
      await git.checkout(branch);
    } catch (error) {
      console.error("Failed to checkout git branch:", error);
      throw new Error(`Git checkout failed: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
  async commit(projectPath, message) {
    try {
      const git = simpleGit.simpleGit(projectPath);
      await git.add(".");
      await git.commit(message);
    } catch (error) {
      console.error("Failed to commit changes:", error);
      throw new Error(`Git commit failed: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
  async push(projectPath) {
    try {
      const git = simpleGit.simpleGit(projectPath);
      await git.push();
    } catch (error) {
      console.error("Failed to push changes:", error);
      throw new Error(`Git push failed: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
  async getCommitHistory(projectPath, limit = 10) {
    try {
      const git = simpleGit.simpleGit(projectPath);
      const log = await git.log({ maxCount: limit });
      return [...log.all];
    } catch (error) {
      console.error("Failed to get commit history:", error);
      return [];
    }
  }
  async getDiff(projectPath) {
    try {
      const git = simpleGit.simpleGit(projectPath);
      const diff = await git.diff();
      return diff;
    } catch (error) {
      console.error("Failed to get git diff:", error);
      return "";
    }
  }
  async addRemote(projectPath, name, url) {
    try {
      const git = simpleGit.simpleGit(projectPath);
      await git.addRemote(name, url);
    } catch (error) {
      console.error("Failed to add git remote:", error);
      throw new Error(`Git add remote failed: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
  async removeRemote(projectPath, name) {
    try {
      const git = simpleGit.simpleGit(projectPath);
      await git.removeRemote(name);
    } catch (error) {
      console.error("Failed to remove git remote:", error);
      throw new Error(`Git remove remote failed: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
  async init(projectPath) {
    try {
      const git = simpleGit.simpleGit(projectPath);
      await git.init();
    } catch (error) {
      console.error("Failed to initialize git repository:", error);
      throw new Error(`Git init failed: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}
class ProcessService extends events.EventEmitter {
  constructor() {
    super();
    this.processes = /* @__PURE__ */ new Map();
    this.resourceMonitorInterval = null;
    this.execAsync = util.promisify(child_process.exec);
    this.startResourceMonitoring();
  }
  async startProject(project, profileId) {
    try {
      const command = await this.getStartCommand(project, profileId);
      const args = this.getStartArgs(project, profileId);
      const options = this.getSpawnOptions(project);
      const childProcess = child_process.spawn(command, args, options);
      if (!childProcess.pid) {
        throw new Error("Failed to start process");
      }
      const processInfo = {
        pid: childProcess.pid,
        process: childProcess,
        project,
        startTime: /* @__PURE__ */ new Date()
      };
      this.processes.set(childProcess.pid, processInfo);
      childProcess.on("exit", (code, signal) => {
        this.processes.delete(childProcess.pid);
        this.emit("process-exit", childProcess.pid, code, signal);
      });
      if (childProcess.stdout) {
        childProcess.stdout.on("data", (data) => {
          const output = this.decodeOutput(data);
          this.emit("process-output", childProcess.pid, output, "stdout");
        });
      }
      if (childProcess.stderr) {
        childProcess.stderr.on("data", (data) => {
          const output = this.decodeOutput(data);
          this.emit("process-output", childProcess.pid, output, "stderr");
        });
      }
      childProcess.on("error", (error) => {
        console.error(`Process error for PID ${childProcess.pid}:`, error);
        this.emit("process-error", childProcess.pid, error);
      });
      return childProcess.pid;
    } catch (error) {
      console.error("Failed to start project:", error);
      throw error;
    }
  }
  async stopProcess(pid, force = false) {
    const processInfo = this.processes.get(pid);
    if (!processInfo) {
      throw new Error(`Process with PID ${pid} not found`);
    }
    const { process: process2 } = processInfo;
    if (force) {
      process2.kill("SIGKILL");
    } else {
      process2.kill("SIGTERM");
      setTimeout(() => {
        if (this.processes.has(pid)) {
          process2.kill("SIGKILL");
        }
      }, 1e4);
    }
  }
  async getProcessResources(pid) {
    try {
      const processExists = this.processes.has(pid);
      if (!processExists) {
        return { cpu: 0, memory: 0 };
      }
      return {
        cpu: Math.random() * 10,
        // 0-10% CPU使用率
        memory: Math.random() * 100 * 1024 * 1024
        // 0-100MB 内存使用
      };
    } catch (error) {
      return { cpu: 0, memory: 0 };
    }
  }
  onProcessExit(pid, callback) {
    this.on("process-exit", (processPid, exitCode, signal) => {
      if (processPid === pid) {
        callback(exitCode, signal);
      }
    });
  }
  onProcessOutput(pid, callback) {
    this.on("process-output", (processPid, output, type) => {
      if (processPid === pid) {
        callback(output, type);
      }
    });
  }
  onProcessError(pid, callback) {
    this.on("process-error", (processPid, error) => {
      if (processPid === pid) {
        callback(error);
      }
    });
  }
  getRunningProcesses() {
    return Array.from(this.processes.values());
  }
  isProcessRunning(pid) {
    return this.processes.has(pid);
  }
  // 发送输入到进程
  async sendInput(pid, input) {
    const processInfo = this.processes.get(pid);
    if (!processInfo) {
      throw new Error(`Process with PID ${pid} not found`);
    }
    const { process: process2 } = processInfo;
    if (!process2.stdin) {
      throw new Error(`Process ${pid} does not have stdin available`);
    }
    if (process2.stdin.writable) {
      process2.stdin.write(input);
    } else {
      throw new Error(`Process ${pid} stdin is not writable`);
    }
  }
  // 杀死进程
  async killProcess(pid) {
    return this.stopProcess(pid, true);
  }
  async executeCommand(command, options) {
    try {
      const result = await this.execAsync(command, {
        cwd: options == null ? void 0 : options.cwd,
        timeout: (options == null ? void 0 : options.timeout) || 1e4,
        // 默认10秒超时
        encoding: "utf8"
      });
      return {
        stdout: result.stdout,
        stderr: result.stderr
      };
    } catch (error) {
      throw new Error(`Command failed: ${command}
${error.message}`);
    }
  }
  async getStartCommand(project, profileId) {
    var _a, _b, _c;
    const profile = profileId ? (_a = project.config.profiles) == null ? void 0 : _a.find((p) => p.id === profileId) : ((_b = project.config.profiles) == null ? void 0 : _b.find((p) => p.isDefault)) || ((_c = project.config.profiles) == null ? void 0 : _c[0]);
    if (profile && profile.command && profile.command.trim() !== "") {
      return profile.command;
    }
    if (project.config.startCommand && project.config.startCommand.trim() !== "") {
      return project.config.startCommand;
    }
    switch (project.type) {
      case "python":
        return await this.detectPythonCommand(project.path);
      case "nodejs":
        return "npm";
      case "shell":
        return "bash";
      case "docker":
        return "docker";
      default:
        throw new Error(`No start command defined for project type: ${project.type}`);
    }
  }
  getStartArgs(project, profileId) {
    var _a, _b, _c;
    const profile = profileId ? (_a = project.config.profiles) == null ? void 0 : _a.find((p) => p.id === profileId) : ((_b = project.config.profiles) == null ? void 0 : _b.find((p) => p.isDefault)) || ((_c = project.config.profiles) == null ? void 0 : _c[0]);
    if (profile && profile.args) {
      return profile.args;
    }
    switch (project.type) {
      case "python":
        return this.detectPythonMainFile(project.path);
      case "nodejs":
        return ["start"];
      case "shell":
        return ["run.sh"];
      // 或者从项目配置中获取
      case "docker":
        return ["run", "-it", "."];
      // 简化的docker命令
      default:
        return [];
    }
  }
  getSpawnOptions(project) {
    const baseEnv = {
      ...process.env,
      ...project.config.environment
    };
    if (project.type === "python") {
      const venvPath = this.findVirtualEnvPath(project.path);
      if (venvPath) {
        console.log(`为Python项目设置虚拟环境变量: ${venvPath}`);
        baseEnv.VIRTUAL_ENV = venvPath;
        const venvBinPath = process.platform === "win32" ? path__namespace.join(venvPath, "Scripts") : path__namespace.join(venvPath, "bin");
        baseEnv.PATH = `${venvBinPath}${path__namespace.delimiter}${baseEnv.PATH}`;
        const venvSitePackages = process.platform === "win32" ? path__namespace.join(venvPath, "Lib", "site-packages") : path__namespace.join(venvPath, "lib", "python*", "site-packages");
        baseEnv.PYTHONPATH = venvSitePackages;
        console.log(`虚拟环境PATH: ${venvBinPath}`);
        console.log(`VIRTUAL_ENV: ${venvPath}`);
      }
    }
    if (process.platform === "win32") {
      baseEnv.PYTHONIOENCODING = "utf-8";
      baseEnv.PYTHONLEGACYWINDOWSSTDIO = "1";
      baseEnv.CHCP = "65001";
    } else {
      baseEnv.LANG = "en_US.UTF-8";
      baseEnv.LC_ALL = "en_US.UTF-8";
      baseEnv.PYTHONIOENCODING = "utf-8";
    }
    return {
      cwd: project.config.workingDirectory || project.path,
      env: baseEnv,
      stdio: ["pipe", "pipe", "pipe"],
      shell: true,
      // Windows特定设置
      ...process.platform === "win32" && {
        windowsHide: true,
        windowsVerbatimArguments: false
      }
    };
  }
  startResourceMonitoring() {
    this.resourceMonitorInterval = setInterval(async () => {
      for (const [pid] of this.processes) {
        try {
          const resources = await this.getProcessResources(pid);
          this.emit("process-resources", pid, resources);
        } catch (error) {
          console.warn(`Failed to get resources for PID ${pid}:`, error);
        }
      }
    }, 5e3);
  }
  /**
   * 解码进程输出，处理编码问题
   */
  decodeOutput(data) {
    try {
      let output = data.toString("utf8");
      if (output.includes("�")) {
        const encodings = ["cp936", "gbk", "gb2312", "big5", "latin1"];
        for (const encoding of encodings) {
          try {
            const decoded = iconv__namespace.decode(data, encoding);
            if (!decoded.includes("�") && this.isValidText(decoded)) {
              output = decoded;
              break;
            }
          } catch (error) {
            continue;
          }
        }
      }
      output = this.fixEmojiAndSpecialChars(output);
      output = this.cleanAnsiCodes(output);
      return output;
    } catch (error) {
      return data.toString("utf8");
    }
  }
  /**
   * 修复emoji和特殊字符显示问题
   */
  fixEmojiAndSpecialChars(text) {
    const charMap = {
      // 主要的emoji修复
      "馃寪": "🌐",
      // 地球仪
      "馃懁": "👤",
      // 用户图标
      "馃専": "🌟",
      // 星星
      "馃憫": "👑",
      // 皇冠
      "馃殌": "🚀",
      // 火箭
      // 基本符号
      "鉁�": "✓",
      // 对勾
      "鉂�": "❌",
      // 错误
      // 常见乱码清理
      "锟斤拷": ""
    };
    let fixed = text;
    for (const [garbled, correct] of Object.entries(charMap)) {
      fixed = fixed.replace(new RegExp(garbled.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"), "g"), correct);
    }
    fixed = fixed.replace(/\?{3,}/g, "?").replace(/\s{3,}/g, "  ").replace(/\r\n/g, "\n").replace(/\r/g, "\n");
    return fixed;
  }
  /**
   * 检查文本是否有效
   */
  isValidText(text) {
    const replacementChars = (text.match(/\uFFFD/g) || []).length;
    const controlChars = (text.match(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g) || []).length;
    const totalChars = text.length;
    if (totalChars === 0) return false;
    const invalidRatio = (replacementChars + controlChars) / totalChars;
    return invalidRatio < 0.1;
  }
  /**
   * 清理ANSI转义序列和其他控制字符
   */
  cleanAnsiCodes(text) {
    let cleaned = text.replace(/\x1b\[[0-9;]*[mGKHF]/g, "").replace(/\x1b\[[0-9;]*[A-Za-z]/g, "").replace(/\x1b\[[0-9]{1,2}m/g, "").replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, "").replace(/\r\n/g, "\n").replace(/\r/g, "\n");
    return cleaned;
  }
  /**
   * 检测Python项目的主文件
   */
  detectPythonMainFile(projectPath) {
    const fs2 = require("fs");
    const path2 = require("path");
    const mainFiles = ["main.py", "app.py", "run.py", "server.py", "start.py", "index.py"];
    for (const file of mainFiles) {
      if (fs2.existsSync(path2.join(projectPath, file))) {
        console.log(`找到Python主文件: ${file}`);
        return [file];
      }
    }
    try {
      const files = fs2.readdirSync(projectPath);
      const pyFiles = files.filter((file) => file.endsWith(".py"));
      if (pyFiles.length > 0) {
        console.log(`使用第一个Python文件: ${pyFiles[0]}`);
        return [pyFiles[0]];
      }
    } catch (error) {
      console.warn("读取项目目录失败:", error);
    }
    console.log("未找到Python文件，使用默认的main.py");
    return ["main.py"];
  }
  /**
   * 检测可用的Python命令，优先使用虚拟环境
   */
  async detectPythonCommand(projectPath) {
    if (projectPath) {
      const venvPython = await this.findVirtualEnvPython(projectPath);
      if (venvPython) {
        console.log(`使用虚拟环境Python: ${venvPython}`);
        return venvPython;
      }
    }
    const pythonCommands = ["python", "python3", "py", "python.exe", "python3.exe"];
    for (const cmd of pythonCommands) {
      try {
        const { stdout } = await this.execAsync(`${cmd} --version`);
        console.log(`找到Python命令: ${cmd}, 版本: ${stdout.trim()}`);
        return cmd;
      } catch (error) {
        continue;
      }
    }
    throw new Error("未找到可用的Python命令。请确保已安装Python并添加到PATH环境变量中。");
  }
  /**
   * 查找项目虚拟环境路径
   */
  findVirtualEnvPath(projectPath) {
    const venvNames = ["venv", ".venv", "env", ".env", "virtualenv"];
    for (const venvName of venvNames) {
      const venvPath = path__namespace.join(projectPath, venvName);
      if (fs__namespace.existsSync(venvPath)) {
        const pythonPath = process.platform === "win32" ? path__namespace.join(venvPath, "Scripts", "python.exe") : path__namespace.join(venvPath, "bin", "python");
        if (fs__namespace.existsSync(pythonPath)) {
          return venvPath;
        }
      }
    }
    return null;
  }
  /**
   * 查找项目虚拟环境中的Python可执行文件
   */
  async findVirtualEnvPython(projectPath) {
    const venvPath = this.findVirtualEnvPath(projectPath);
    if (!venvPath) {
      console.log(`项目 ${projectPath} 未找到可用的虚拟环境`);
      return null;
    }
    const pythonPath = process.platform === "win32" ? path__namespace.join(venvPath, "Scripts", "python.exe") : path__namespace.join(venvPath, "bin", "python");
    try {
      const { stdout } = await this.execAsync(`"${pythonPath}" --version`);
      console.log(`找到虚拟环境Python: ${pythonPath}, 版本: ${stdout.trim()}`);
      return `"${pythonPath}"`;
    } catch (error) {
      console.warn(`虚拟环境Python不可用: ${pythonPath}`, error);
      return null;
    }
  }
  destroy() {
    if (this.resourceMonitorInterval) {
      clearInterval(this.resourceMonitorInterval);
    }
    for (const [pid] of this.processes) {
      this.stopProcess(pid, true).catch(console.error);
    }
  }
}
const execAsync$3 = util.promisify(child_process.exec);
class DependencyService {
  /**
   * 检查项目依赖状态
   */
  async checkDependencies(projectPath, projectType) {
    try {
      switch (projectType.toLowerCase()) {
        case "python":
          return await this.checkPythonDependencies(projectPath);
        case "nodejs":
        case "node":
          return await this.checkNodeDependencies(projectPath);
        default:
          return {
            dependencies: [],
            hasRequirements: false,
            packageManager: "unknown"
          };
      }
    } catch (error) {
      console.error("检查依赖失败:", error);
      throw new Error(`检查依赖失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 安装依赖
   */
  async installDependency(projectPath, projectType, dependencyName, version) {
    try {
      switch (projectType.toLowerCase()) {
        case "python":
          await this.installPythonDependency(projectPath, dependencyName, version);
          break;
        case "nodejs":
        case "node":
          await this.installNodeDependency(projectPath, dependencyName, version);
          break;
        default:
          throw new Error(`不支持的项目类型: ${projectType}`);
      }
    } catch (error) {
      console.error("安装依赖失败:", error);
      throw new Error(`安装依赖失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 批量安装所有依赖
   */
  async installAllDependencies(projectPath, projectType) {
    try {
      switch (projectType.toLowerCase()) {
        case "python":
          await this.installAllPythonDependencies(projectPath);
          break;
        case "nodejs":
        case "node":
          await this.installAllNodeDependencies(projectPath);
          break;
        default:
          throw new Error(`不支持的项目类型: ${projectType}`);
      }
    } catch (error) {
      console.error("批量安装依赖失败:", error);
      throw new Error(`批量安装依赖失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 完全清理项目依赖
   */
  async cleanAllDependencies(projectPath, projectType) {
    try {
      switch (projectType.toLowerCase()) {
        case "python":
          await this.cleanAllPythonDependencies(projectPath);
          break;
        case "nodejs":
        case "node":
          await this.cleanAllNodeDependencies(projectPath);
          break;
        default:
          throw new Error(`不支持的项目类型: ${projectType}`);
      }
    } catch (error) {
      console.error("清理依赖失败:", error);
      throw new Error(`清理依赖失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 卸载依赖
   */
  async uninstallDependency(projectPath, projectType, dependencyName) {
    try {
      switch (projectType.toLowerCase()) {
        case "python":
          await this.uninstallPythonDependency(projectPath, dependencyName);
          break;
        case "nodejs":
        case "node":
          await this.uninstallNodeDependency(projectPath, dependencyName);
          break;
        default:
          throw new Error(`不支持的项目类型: ${projectType}`);
      }
    } catch (error) {
      console.error("卸载依赖失败:", error);
      throw new Error(`卸载依赖失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 检查Python项目依赖
   */
  async checkPythonDependencies(projectPath) {
    const dependencies = [];
    let hasRequirements = false;
    let packageManager = "pip";
    const venvPath = await this.findPythonVirtualEnvironment(projectPath);
    if (!venvPath) {
      console.log("未找到虚拟环境，返回空依赖列表");
      return {
        dependencies: [],
        hasRequirements: false,
        packageManager: "venv-required"
        // 特殊标记表示需要虚拟环境
      };
    }
    console.log(`找到虚拟环境: ${venvPath}`);
    const requirementsPath = path__namespace.join(projectPath, "requirements.txt");
    if (fs__namespace.existsSync(requirementsPath)) {
      hasRequirements = true;
      const content = fs__namespace.readFileSync(requirementsPath, "utf-8");
      const reqDeps = this.parseRequirementsTxt(content);
      dependencies.push(...reqDeps);
    }
    const pipfilePath = path__namespace.join(projectPath, "Pipfile");
    if (fs__namespace.existsSync(pipfilePath)) {
      hasRequirements = true;
      packageManager = "pipenv";
      const pipDeps = await this.parsePipfile(pipfilePath);
      dependencies.push(...pipDeps);
    }
    const pyprojectPath = path__namespace.join(projectPath, "pyproject.toml");
    if (fs__namespace.existsSync(pyprojectPath)) {
      hasRequirements = true;
      packageManager = "poetry";
      const poetryDeps = await this.parsePyprojectToml(pyprojectPath);
      dependencies.push(...poetryDeps);
    }
    for (const dep of dependencies) {
      try {
        const installedVersion = await this.getPythonPackageVersionInVenv(venvPath, dep.name);
        if (installedVersion) {
          dep.installedVersion = installedVersion;
          dep.status = this.compareVersions(installedVersion, dep.version) >= 0 ? "installed" : "outdated";
        } else {
          dep.status = "missing";
        }
      } catch {
        dep.status = "unknown";
      }
    }
    return {
      dependencies,
      hasRequirements,
      packageManager
    };
  }
  /**
   * 检查Node.js项目依赖
   */
  async checkNodeDependencies(projectPath) {
    const dependencies = [];
    let hasRequirements = false;
    let packageManager = "npm";
    const packageJsonPath = path__namespace.join(projectPath, "package.json");
    if (fs__namespace.existsSync(packageJsonPath)) {
      hasRequirements = true;
      const content = fs__namespace.readFileSync(packageJsonPath, "utf-8");
      const packageJson = JSON.parse(content);
      if (fs__namespace.existsSync(path__namespace.join(projectPath, "yarn.lock"))) {
        packageManager = "yarn";
      } else if (fs__namespace.existsSync(path__namespace.join(projectPath, "pnpm-lock.yaml"))) {
        packageManager = "pnpm";
      }
      const deps = this.parsePackageJson(packageJson);
      dependencies.push(...deps);
    }
    for (const dep of dependencies) {
      try {
        const installedVersion = await this.getNodePackageVersion(projectPath, dep.name);
        if (installedVersion) {
          dep.installedVersion = installedVersion;
          dep.status = this.compareVersions(installedVersion, dep.version) >= 0 ? "installed" : "outdated";
        } else {
          dep.status = "missing";
        }
      } catch {
        dep.status = "unknown";
      }
    }
    return {
      dependencies,
      hasRequirements,
      packageManager
    };
  }
  /**
   * 解析requirements.txt文件
   */
  parseRequirementsTxt(content) {
    const dependencies = [];
    const lines = content.split("\n");
    for (const line of lines) {
      const trimmed = line.trim();
      if (!trimmed || trimmed.startsWith("#") || trimmed.startsWith("-")) {
        continue;
      }
      const match = trimmed.match(/^([a-zA-Z0-9_-]+)([>=<~!]+)?(.+)?$/);
      if (match) {
        const [, name, operator, version] = match;
        dependencies.push({
          name: name.trim(),
          version: version ? version.trim() : "latest",
          status: "unknown"
        });
      }
    }
    return dependencies;
  }
  /**
   * 解析package.json文件
   */
  parsePackageJson(packageJson) {
    const dependencies = [];
    if (packageJson.dependencies) {
      for (const [name, version] of Object.entries(packageJson.dependencies)) {
        dependencies.push({
          name,
          version,
          status: "unknown"
        });
      }
    }
    if (packageJson.devDependencies) {
      for (const [name, version] of Object.entries(packageJson.devDependencies)) {
        dependencies.push({
          name,
          version,
          status: "unknown"
        });
      }
    }
    return dependencies;
  }
  /**
   * 解析Pipfile（简化版）
   */
  async parsePipfile(pipfilePath) {
    return [];
  }
  /**
   * 解析pyproject.toml（简化版）
   */
  async parsePyprojectToml(pyprojectPath) {
    return [];
  }
  /**
   * 查找Python虚拟环境
   */
  async findPythonVirtualEnvironment(projectPath) {
    const venvNames = ["venv", ".venv", "env", ".env", "virtualenv"];
    for (const venvName of venvNames) {
      const venvPath = path__namespace.join(projectPath, venvName);
      if (fs__namespace.existsSync(venvPath)) {
        const pythonPath = process.platform === "win32" ? path__namespace.join(venvPath, "Scripts", "python.exe") : path__namespace.join(venvPath, "bin", "python");
        if (fs__namespace.existsSync(pythonPath)) {
          console.log(`找到虚拟环境: ${venvPath}`);
          return venvPath;
        }
      }
    }
    console.log("未找到虚拟环境");
    return null;
  }
  /**
   * 获取虚拟环境中Python包版本
   */
  async getPythonPackageVersionInVenv(venvPath, packageName) {
    try {
      const pythonPath = process.platform === "win32" ? path__namespace.join(venvPath, "Scripts", "python.exe") : path__namespace.join(venvPath, "bin", "python");
      if (!fs__namespace.existsSync(pythonPath)) {
        console.log(`Python可执行文件不存在: ${pythonPath}`);
        return null;
      }
      const commands = [
        `"${pythonPath}" -c "import ${packageName}; print(${packageName}.__version__)"`,
        `"${pythonPath}" -c "import pkg_resources; print(pkg_resources.get_distribution('${packageName}').version)"`,
        `"${pythonPath}" -m pip show ${packageName} | findstr Version`
        // Windows
      ];
      for (const command of commands) {
        try {
          const { stdout } = await execAsync$3(command, {
            cwd: path__namespace.dirname(venvPath),
            timeout: 1e4
          });
          const output = stdout.trim();
          if (output && !output.includes("Error") && !output.includes("not found")) {
            if (command.includes("pip show")) {
              const versionMatch = output.match(/Version:\s*(.+)/);
              if (versionMatch) {
                console.log(`通过pip show获取到 ${packageName} 版本: ${versionMatch[1]}`);
                return versionMatch[1].trim();
              }
            } else {
              console.log(`获取到 ${packageName} 版本: ${output}`);
              return output;
            }
          }
        } catch (error) {
          continue;
        }
      }
      console.log(`无法获取包 ${packageName} 的版本信息`);
      return null;
    } catch (error) {
      console.error(`获取虚拟环境中包版本失败: ${error}`);
      return null;
    }
  }
  /**
   * 获取Python包版本（已弃用，使用虚拟环境版本）
   */
  async getPythonPackageVersion(projectPath, packageName) {
    try {
      const { stdout } = await execAsync$3(`python -c "import ${packageName}; print(${packageName}.__version__)"`, {
        cwd: projectPath
      });
      return stdout.trim();
    } catch {
      return null;
    }
  }
  /**
   * 获取Node.js包版本
   */
  async getNodePackageVersion(projectPath, packageName) {
    var _a, _b;
    try {
      const packagePath = path__namespace.join(projectPath, "node_modules", packageName, "package.json");
      if (fs__namespace.existsSync(packagePath)) {
        const packageJson = JSON.parse(fs__namespace.readFileSync(packagePath, "utf-8"));
        return packageJson.version;
      }
      try {
        const { stdout } = await execAsync$3(`npm list ${packageName} --depth=0 --json`, {
          cwd: projectPath
        });
        const result = JSON.parse(stdout);
        return ((_b = (_a = result.dependencies) == null ? void 0 : _a[packageName]) == null ? void 0 : _b.version) || null;
      } catch {
      }
      return null;
    } catch {
      return null;
    }
  }
  /**
   * 安装Python依赖到虚拟环境
   */
  async installPythonDependency(projectPath, dependencyName, version) {
    const venvPath = await this.findPythonVirtualEnvironment(projectPath);
    if (!venvPath) {
      throw new Error("未找到虚拟环境。请先创建虚拟环境再安装依赖。");
    }
    const packageSpec = version ? `${dependencyName}==${version}` : dependencyName;
    const pipPath = process.platform === "win32" ? path__namespace.join(venvPath, "Scripts", "pip.exe") : path__namespace.join(venvPath, "bin", "pip");
    const command = `"${pipPath}" install ${packageSpec}`;
    try {
      console.log(`在虚拟环境中安装Python包: ${command}`);
      await execAsync$3(command, { cwd: projectPath });
    } catch (error) {
      throw new Error(`安装Python包失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 安装Node.js依赖
   */
  async installNodeDependency(projectPath, dependencyName, version) {
    const packageManager = this.detectNodePackageManager(projectPath);
    const packageSpec = version ? `${dependencyName}@${version}` : dependencyName;
    let command;
    switch (packageManager) {
      case "yarn":
        command = `yarn add ${packageSpec}`;
        break;
      case "pnpm":
        command = `pnpm add ${packageSpec}`;
        break;
      default:
        command = `npm install ${packageSpec}`;
    }
    try {
      console.log(`安装Node.js包: ${packageSpec} 在 ${projectPath}`);
      const result = await execAsync$3(command, {
        cwd: projectPath,
        timeout: 12e4
        // 2分钟超时
      });
      console.log(`包安装成功: ${result.stdout}`);
      console.log(`运行完整依赖安装...`);
      await this.installAllNodeDependencies(projectPath);
    } catch (error) {
      console.error(`安装Node.js包失败: ${error}`);
      throw new Error(`安装Node.js包失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 批量安装Python依赖到虚拟环境
   */
  async installAllPythonDependencies(projectPath) {
    const venvPath = await this.findPythonVirtualEnvironment(projectPath);
    if (!venvPath) {
      throw new Error("未找到虚拟环境。请先创建虚拟环境再安装依赖。");
    }
    const requirementsPath = path__namespace.join(projectPath, "requirements.txt");
    if (fs__namespace.existsSync(requirementsPath)) {
      const pipPath = process.platform === "win32" ? path__namespace.join(venvPath, "Scripts", "pip.exe") : path__namespace.join(venvPath, "bin", "pip");
      const command = `"${pipPath}" install -r requirements.txt`;
      try {
        console.log(`在虚拟环境中批量安装Python依赖: ${command}`);
        await execAsync$3(command, { cwd: projectPath });
      } catch (error) {
        throw new Error(`安装Python依赖失败: ${error instanceof Error ? error.message : "未知错误"}`);
      }
    } else {
      throw new Error("未找到requirements.txt文件");
    }
  }
  /**
   * 批量安装Node.js依赖
   */
  async installAllNodeDependencies(projectPath) {
    const packageManager = this.detectNodePackageManager(projectPath);
    let command;
    switch (packageManager) {
      case "yarn":
        command = "yarn install --frozen-lockfile";
        break;
      case "pnpm":
        command = "pnpm install --frozen-lockfile";
        break;
      default:
        command = "npm ci || npm install";
    }
    try {
      console.log(`执行依赖安装命令: ${command}`);
      const result = await execAsync$3(command, {
        cwd: projectPath,
        timeout: 3e5
        // 5分钟超时
      });
      console.log(`依赖安装成功: ${result.stdout}`);
    } catch (error) {
      console.error(`依赖安装失败: ${error}`);
      throw new Error(`安装Node.js依赖失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 从虚拟环境中卸载Python依赖
   */
  async uninstallPythonDependency(projectPath, dependencyName) {
    const venvPath = await this.findPythonVirtualEnvironment(projectPath);
    if (!venvPath) {
      throw new Error("未找到虚拟环境。无法卸载依赖。");
    }
    const pipPath = process.platform === "win32" ? path__namespace.join(venvPath, "Scripts", "pip.exe") : path__namespace.join(venvPath, "bin", "pip");
    const command = `"${pipPath}" uninstall ${dependencyName} -y`;
    try {
      console.log(`从虚拟环境中卸载Python包: ${command}`);
      await execAsync$3(command, { cwd: projectPath });
    } catch (error) {
      throw new Error(`卸载Python包失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 卸载Node.js依赖
   */
  async uninstallNodeDependency(projectPath, dependencyName) {
    const packageManager = this.detectNodePackageManager(projectPath);
    let command;
    switch (packageManager) {
      case "yarn":
        command = `yarn remove ${dependencyName}`;
        break;
      case "pnpm":
        command = `pnpm remove ${dependencyName}`;
        break;
      default:
        command = `npm uninstall ${dependencyName}`;
    }
    try {
      await execAsync$3(command, { cwd: projectPath });
    } catch (error) {
      throw new Error(`卸载Node.js包失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 完全清理Node.js依赖
   */
  async cleanAllNodeDependencies(projectPath) {
    try {
      console.log(`开始清理Node.js项目依赖: ${projectPath}`);
      const nodeModulesPath = path__namespace.join(projectPath, "node_modules");
      if (fs__namespace.existsSync(nodeModulesPath)) {
        console.log("删除 node_modules 目录...");
        await this.removeDirectory(nodeModulesPath);
      }
      const lockFiles = [
        "package-lock.json",
        "yarn.lock",
        "pnpm-lock.yaml"
      ];
      for (const lockFile of lockFiles) {
        const lockFilePath = path__namespace.join(projectPath, lockFile);
        if (fs__namespace.existsSync(lockFilePath)) {
          console.log(`删除锁文件: ${lockFile}`);
          fs__namespace.unlinkSync(lockFilePath);
        }
      }
      console.log("Node.js依赖清理完成");
    } catch (error) {
      throw new Error(`清理Node.js依赖失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 完全清理Python依赖（删除虚拟环境）
   */
  async cleanAllPythonDependencies(projectPath) {
    try {
      console.log(`开始清理Python项目依赖: ${projectPath}`);
      const venvPath = await this.findPythonVirtualEnvironment(projectPath);
      if (!venvPath) {
        console.log("未找到虚拟环境，无需清理");
        return;
      }
      console.log(`删除虚拟环境: ${venvPath}`);
      await this.removeDirectory(venvPath);
      console.log("Python虚拟环境清理完成");
    } catch (error) {
      throw new Error(`清理Python依赖失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 递归删除目录
   */
  async removeDirectory(dirPath) {
    try {
      if (fs__namespace.rmSync) {
        fs__namespace.rmSync(dirPath, { recursive: true, force: true });
      } else {
        await execAsync$3(`rmdir /s /q "${dirPath}"`, { shell: true });
      }
    } catch (error) {
      throw new Error(`删除目录失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 检测Node.js包管理器
   */
  detectNodePackageManager(projectPath) {
    if (fs__namespace.existsSync(path__namespace.join(projectPath, "yarn.lock"))) {
      return "yarn";
    }
    if (fs__namespace.existsSync(path__namespace.join(projectPath, "pnpm-lock.yaml"))) {
      return "pnpm";
    }
    return "npm";
  }
  /**
   * 比较版本号
   */
  compareVersions(version1, version2) {
    const v1Parts = version1.replace(/[^0-9.]/g, "").split(".").map(Number);
    const v2Parts = version2.replace(/[^0-9.]/g, "").split(".").map(Number);
    const maxLength = Math.max(v1Parts.length, v2Parts.length);
    for (let i = 0; i < maxLength; i++) {
      const v1 = v1Parts[i] || 0;
      const v2 = v2Parts[i] || 0;
      if (v1 > v2) return 1;
      if (v1 < v2) return -1;
    }
    return 0;
  }
}
const execAsync$2 = util.promisify(child_process.exec);
class VirtualEnvironmentService {
  /**
   * 检查Python环境
   */
  async checkPythonEnvironment() {
    try {
      const pythonCmd = await this.detectPythonCommand();
      const { stdout } = await execAsync$2(`${pythonCmd} --version`);
      return {
        available: true,
        version: stdout.trim(),
        command: pythonCmd
      };
    } catch (error) {
      return {
        available: false,
        error: error instanceof Error ? error.message : "未知错误"
      };
    }
  }
  /**
   * 列出项目的虚拟环境
   */
  async listVirtualEnvironments(projectPath) {
    try {
      const environments = [];
      let activeEnv;
      const venvDirs = ["venv", ".venv", "env", ".env", "virtualenv"];
      for (const dirName of venvDirs) {
        const venvPath = path__namespace.join(projectPath, dirName);
        if (fs__namespace.existsSync(venvPath) && this.isValidVirtualEnv(venvPath)) {
          const env = await this.getVirtualEnvInfo(dirName, venvPath);
          environments.push(env);
        }
      }
      try {
        const { stdout } = await execAsync$2('python -c "import sys; print(sys.prefix)"', {
          cwd: projectPath
        });
        const currentPrefix = stdout.trim();
        for (const env of environments) {
          if (currentPrefix.includes(env.path)) {
            activeEnv = env.name;
            env.isActive = true;
            break;
          }
        }
      } catch {
      }
      return {
        environments,
        activeEnv
      };
    } catch (error) {
      console.error("列出虚拟环境失败:", error);
      throw new Error(`列出虚拟环境失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 检测可用的Python命令
   */
  async detectPythonCommand(pythonVersion) {
    const pythonCommands = [];
    if (pythonVersion) {
      pythonCommands.push(`python${pythonVersion}`, `py -${pythonVersion}`, `python${pythonVersion}.exe`);
    }
    pythonCommands.push("python", "python3", "py", "python.exe", "python3.exe");
    for (const cmd of pythonCommands) {
      try {
        const { stdout } = await execAsync$2(`${cmd} --version`);
        console.log(`找到Python命令: ${cmd}, 版本: ${stdout.trim()}`);
        return cmd;
      } catch (error) {
        continue;
      }
    }
    throw new Error("未找到可用的Python命令。请确保已安装Python并添加到PATH环境变量中。");
  }
  /**
   * 创建虚拟环境
   */
  async createVirtualEnvironment(projectPath, envName = "venv", pythonVersion) {
    try {
      const venvPath = path__namespace.join(projectPath, envName);
      if (fs__namespace.existsSync(venvPath)) {
        throw new Error(`虚拟环境 ${envName} 已存在`);
      }
      const pythonCmd = await this.detectPythonCommand(pythonVersion);
      const command = `${pythonCmd} -m venv "${venvPath}"`;
      console.log(`创建虚拟环境: ${command}`);
      try {
        await execAsync$2(command, {
          cwd: projectPath,
          timeout: 6e4
          // 1分钟超时
        });
      } catch (error) {
        if (error.code === 9009) {
          throw new Error(`Python命令 "${pythonCmd}" 未找到。请确保已安装Python并添加到PATH环境变量中。`);
        } else if (error.stderr && error.stderr.includes("No module named venv")) {
          throw new Error(`Python venv模块未找到。请安装完整的Python环境或使用 "pip install virtualenv" 安装virtualenv。`);
        } else {
          throw new Error(`执行命令失败: ${error.message || error}`);
        }
      }
      if (!this.isValidVirtualEnv(venvPath)) {
        throw new Error("虚拟环境创建失败，验证失败");
      }
      console.log(`虚拟环境 ${envName} 创建成功`);
    } catch (error) {
      console.error("创建虚拟环境失败:", error);
      throw new Error(`创建虚拟环境失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 激活虚拟环境
   */
  async activateVirtualEnvironment(projectPath, envName) {
    try {
      const venvPath = path__namespace.join(projectPath, envName);
      if (!fs__namespace.existsSync(venvPath)) {
        throw new Error(`虚拟环境 ${envName} 不存在`);
      }
      if (!this.isValidVirtualEnv(venvPath)) {
        throw new Error(`${envName} 不是有效的虚拟环境`);
      }
      const activateScript = this.getActivateScript(venvPath);
      if (!fs__namespace.existsSync(activateScript)) {
        throw new Error("激活脚本不存在");
      }
      return activateScript;
    } catch (error) {
      console.error("激活虚拟环境失败:", error);
      throw new Error(`激活虚拟环境失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 删除虚拟环境
   */
  async deleteVirtualEnvironment(projectPath, envName) {
    try {
      const venvPath = path__namespace.join(projectPath, envName);
      if (!fs__namespace.existsSync(venvPath)) {
        throw new Error(`虚拟环境 ${envName} 不存在`);
      }
      await this.removeDirectory(venvPath);
      console.log(`虚拟环境 ${envName} 已删除`);
    } catch (error) {
      console.error("删除虚拟环境失败:", error);
      throw new Error(`删除虚拟环境失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 在虚拟环境中安装包
   */
  async installPackageInVenv(projectPath, envName, packageName, version) {
    try {
      const venvPath = path__namespace.join(projectPath, envName);
      const pythonPath = this.getPythonExecutable(venvPath);
      if (!fs__namespace.existsSync(pythonPath)) {
        throw new Error(`虚拟环境 ${envName} 中的Python可执行文件不存在`);
      }
      const packageSpec = version ? `${packageName}==${version}` : packageName;
      const command = `"${pythonPath}" -m pip install ${packageSpec}`;
      await execAsync$2(command, { cwd: projectPath });
      console.log(`在虚拟环境 ${envName} 中安装包 ${packageSpec} 成功`);
    } catch (error) {
      console.error("在虚拟环境中安装包失败:", error);
      throw new Error(`在虚拟环境中安装包失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 获取虚拟环境中已安装的包列表
   */
  async getInstalledPackages(projectPath, envName) {
    try {
      const venvPath = path__namespace.join(projectPath, envName);
      const pythonPath = this.getPythonExecutable(venvPath);
      if (!fs__namespace.existsSync(pythonPath)) {
        return [];
      }
      const { stdout } = await execAsync$2(`"${pythonPath}" -m pip list --format=freeze`, {
        cwd: projectPath
      });
      return stdout.trim().split("\n").filter((line) => line.trim());
    } catch (error) {
      console.error("获取已安装包列表失败:", error);
      return [];
    }
  }
  /**
   * 检查是否为有效的虚拟环境
   */
  isValidVirtualEnv(venvPath) {
    const pythonPath = this.getPythonExecutable(venvPath);
    const activateScript = this.getActivateScript(venvPath);
    return fs__namespace.existsSync(pythonPath) && fs__namespace.existsSync(activateScript);
  }
  /**
   * 获取虚拟环境信息
   */
  async getVirtualEnvInfo(name, venvPath) {
    const pythonPath = this.getPythonExecutable(venvPath);
    let pythonVersion = "unknown";
    let packages = [];
    let size = 0;
    let createdAt = /* @__PURE__ */ new Date();
    try {
      const { stdout } = await execAsync$2(`"${pythonPath}" --version`);
      pythonVersion = stdout.trim();
    } catch {
    }
    try {
      packages = await this.getInstalledPackages(path__namespace.dirname(venvPath), name);
    } catch {
    }
    try {
      const stats = fs__namespace.statSync(venvPath);
      createdAt = stats.birthtime;
      size = await this.getDirectorySize(venvPath);
    } catch {
    }
    return {
      name,
      path: venvPath,
      pythonVersion,
      isActive: false,
      createdAt,
      size,
      packages
    };
  }
  /**
   * 获取Python可执行文件路径
   */
  getPythonExecutable(venvPath) {
    if (process.platform === "win32") {
      return path__namespace.join(venvPath, "Scripts", "python.exe");
    } else {
      return path__namespace.join(venvPath, "bin", "python");
    }
  }
  /**
   * 获取激活脚本路径
   */
  getActivateScript(venvPath) {
    if (process.platform === "win32") {
      return path__namespace.join(venvPath, "Scripts", "activate.bat");
    } else {
      return path__namespace.join(venvPath, "bin", "activate");
    }
  }
  /**
   * 递归删除目录
   */
  async removeDirectory(dirPath) {
    if (fs__namespace.existsSync(dirPath)) {
      const files = fs__namespace.readdirSync(dirPath);
      for (const file of files) {
        const filePath = path__namespace.join(dirPath, file);
        const stat2 = fs__namespace.statSync(filePath);
        if (stat2.isDirectory()) {
          await this.removeDirectory(filePath);
        } else {
          fs__namespace.unlinkSync(filePath);
        }
      }
      fs__namespace.rmdirSync(dirPath);
    }
  }
  /**
   * 计算目录大小
   */
  async getDirectorySize(dirPath) {
    let totalSize = 0;
    try {
      const files = fs__namespace.readdirSync(dirPath);
      for (const file of files) {
        const filePath = path__namespace.join(dirPath, file);
        const stat2 = fs__namespace.statSync(filePath);
        if (stat2.isDirectory()) {
          totalSize += await this.getDirectorySize(filePath);
        } else {
          totalSize += stat2.size;
        }
      }
    } catch {
    }
    return totalSize;
  }
}
class ConfigurationService {
  constructor() {
    this.CONFIG_DIR = ".workspace";
    this.CONFIG_FILE = "config.json";
    this.ENV_FILE = "env.json";
  }
  /**
   * 获取项目配置
   */
  async getProjectConfiguration(projectPath) {
    try {
      const configPath = this.getConfigPath(projectPath);
      if (!fs__namespace.existsSync(configPath)) {
        return await this.createDefaultConfiguration(projectPath);
      }
      const configContent = fs__namespace.readFileSync(configPath, "utf-8");
      const config = JSON.parse(configContent);
      return this.validateAndFixConfiguration(config);
    } catch (error) {
      console.error("获取项目配置失败:", error);
      throw new Error(`获取项目配置失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 保存项目配置
   */
  async saveProjectConfiguration(projectPath, configuration) {
    try {
      const configDir = this.getConfigDir(projectPath);
      const configPath = this.getConfigPath(projectPath);
      if (!fs__namespace.existsSync(configDir)) {
        fs__namespace.mkdirSync(configDir, { recursive: true });
      }
      configuration.profiles.forEach((profile) => {
        profile.updatedAt = /* @__PURE__ */ new Date();
      });
      fs__namespace.writeFileSync(configPath, JSON.stringify(configuration, null, 2), "utf-8");
      console.log(`项目配置已保存: ${configPath}`);
    } catch (error) {
      console.error("保存项目配置失败:", error);
      throw new Error(`保存项目配置失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 创建新的配置文件
   */
  async createProfile(projectPath, profile) {
    try {
      const configuration = await this.getProjectConfiguration(projectPath);
      const newProfile = {
        ...profile,
        id: this.generateProfileId(),
        createdAt: /* @__PURE__ */ new Date(),
        updatedAt: /* @__PURE__ */ new Date()
      };
      configuration.profiles.push(newProfile);
      if (configuration.profiles.length === 1) {
        newProfile.isDefault = true;
        configuration.defaultProfile = newProfile.id;
      }
      await this.saveProjectConfiguration(projectPath, configuration);
      return newProfile;
    } catch (error) {
      console.error("创建配置文件失败:", error);
      throw new Error(`创建配置文件失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 更新配置文件
   */
  async updateProfile(projectPath, profileId, updates) {
    try {
      const configuration = await this.getProjectConfiguration(projectPath);
      const profileIndex = configuration.profiles.findIndex((p) => p.id === profileId);
      if (profileIndex === -1) {
        throw new Error(`配置文件 ${profileId} 不存在`);
      }
      const updatedProfile = {
        ...configuration.profiles[profileIndex],
        ...updates,
        id: profileId,
        // 确保ID不被修改
        updatedAt: /* @__PURE__ */ new Date()
      };
      configuration.profiles[profileIndex] = updatedProfile;
      if (updates.isDefault) {
        configuration.profiles.forEach((p) => {
          if (p.id !== profileId) {
            p.isDefault = false;
          }
        });
        configuration.defaultProfile = profileId;
      }
      await this.saveProjectConfiguration(projectPath, configuration);
      return updatedProfile;
    } catch (error) {
      console.error("更新配置文件失败:", error);
      throw new Error(`更新配置文件失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 删除配置文件
   */
  async deleteProfile(projectPath, profileId) {
    try {
      const configuration = await this.getProjectConfiguration(projectPath);
      const profileIndex = configuration.profiles.findIndex((p) => p.id === profileId);
      if (profileIndex === -1) {
        throw new Error(`配置文件 ${profileId} 不存在`);
      }
      const profile = configuration.profiles[profileIndex];
      if (configuration.profiles.length === 1) {
        throw new Error("不能删除唯一的配置文件");
      }
      configuration.profiles.splice(profileIndex, 1);
      if (profile.isDefault && configuration.profiles.length > 0) {
        configuration.profiles[0].isDefault = true;
        configuration.defaultProfile = configuration.profiles[0].id;
      }
      await this.saveProjectConfiguration(projectPath, configuration);
      console.log(`配置文件 ${profileId} 已删除`);
    } catch (error) {
      console.error("删除配置文件失败:", error);
      throw new Error(`删除配置文件失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 获取默认配置文件
   */
  async getDefaultProfile(projectPath) {
    try {
      const configuration = await this.getProjectConfiguration(projectPath);
      if (configuration.defaultProfile) {
        return configuration.profiles.find((p) => p.id === configuration.defaultProfile) || null;
      }
      return configuration.profiles.length > 0 ? configuration.profiles[0] : null;
    } catch (error) {
      console.error("获取默认配置失败:", error);
      return null;
    }
  }
  /**
   * 复制配置文件
   */
  async duplicateProfile(projectPath, profileId, newName) {
    try {
      const configuration = await this.getProjectConfiguration(projectPath);
      const sourceProfile = configuration.profiles.find((p) => p.id === profileId);
      if (!sourceProfile) {
        throw new Error(`配置文件 ${profileId} 不存在`);
      }
      const duplicatedProfile = {
        ...sourceProfile,
        id: this.generateProfileId(),
        name: newName,
        isDefault: false,
        createdAt: /* @__PURE__ */ new Date(),
        updatedAt: /* @__PURE__ */ new Date()
      };
      configuration.profiles.push(duplicatedProfile);
      await this.saveProjectConfiguration(projectPath, configuration);
      return duplicatedProfile;
    } catch (error) {
      console.error("复制配置文件失败:", error);
      throw new Error(`复制配置文件失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 导出配置
   */
  async exportConfiguration(projectPath) {
    try {
      const configuration = await this.getProjectConfiguration(projectPath);
      return JSON.stringify(configuration, null, 2);
    } catch (error) {
      console.error("导出配置失败:", error);
      throw new Error(`导出配置失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 导入配置
   */
  async importConfiguration(projectPath, configJson) {
    try {
      const importedConfig = JSON.parse(configJson);
      const validatedConfig = this.validateAndFixConfiguration(importedConfig);
      await this.saveProjectConfiguration(projectPath, validatedConfig);
      console.log("配置导入成功");
    } catch (error) {
      console.error("导入配置失败:", error);
      throw new Error(`导入配置失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 创建默认配置
   */
  async createDefaultConfiguration(projectPath) {
    const defaultProfile = {
      id: this.generateProfileId(),
      name: "默认配置",
      description: "项目的默认运行配置",
      command: this.detectDefaultCommand(projectPath),
      args: [],
      workingDirectory: projectPath,
      environment: {},
      isDefault: true,
      createdAt: /* @__PURE__ */ new Date(),
      updatedAt: /* @__PURE__ */ new Date()
    };
    const configuration = {
      profiles: [defaultProfile],
      defaultProfile: defaultProfile.id,
      globalEnvironment: {},
      settings: {}
    };
    await this.saveProjectConfiguration(projectPath, configuration);
    return configuration;
  }
  /**
   * 检测默认启动命令
   */
  detectDefaultCommand(projectPath) {
    var _a, _b;
    if (fs__namespace.existsSync(path__namespace.join(projectPath, "main.py")) || fs__namespace.existsSync(path__namespace.join(projectPath, "app.py")) || fs__namespace.existsSync(path__namespace.join(projectPath, "manage.py")) || fs__namespace.existsSync(path__namespace.join(projectPath, "requirements.txt"))) {
      return "";
    }
    if (fs__namespace.existsSync(path__namespace.join(projectPath, "package.json"))) {
      try {
        const packageJson = JSON.parse(fs__namespace.readFileSync(path__namespace.join(projectPath, "package.json"), "utf-8"));
        if ((_a = packageJson.scripts) == null ? void 0 : _a.start) {
          return "npm start";
        }
        if ((_b = packageJson.scripts) == null ? void 0 : _b.dev) {
          return "npm run dev";
        }
      } catch {
      }
    }
    if (fs__namespace.existsSync(path__namespace.join(projectPath, "start.sh"))) {
      return "./start.sh";
    }
    if (fs__namespace.existsSync(path__namespace.join(projectPath, "run.sh"))) {
      return "./run.sh";
    }
    return 'echo "请配置启动命令"';
  }
  /**
   * 验证和修复配置结构
   */
  validateAndFixConfiguration(config) {
    const validatedConfig = {
      profiles: [],
      globalEnvironment: config.globalEnvironment || {},
      settings: config.settings || {}
    };
    if (Array.isArray(config.profiles)) {
      validatedConfig.profiles = config.profiles.map((profile) => ({
        id: profile.id || this.generateProfileId(),
        name: profile.name || "未命名配置",
        description: profile.description || "",
        command: profile.command || "",
        args: Array.isArray(profile.args) ? profile.args : [],
        workingDirectory: profile.workingDirectory || "",
        environment: profile.environment || {},
        isDefault: Boolean(profile.isDefault),
        createdAt: profile.createdAt ? new Date(profile.createdAt) : /* @__PURE__ */ new Date(),
        updatedAt: profile.updatedAt ? new Date(profile.updatedAt) : /* @__PURE__ */ new Date()
      }));
    }
    if (validatedConfig.profiles.length > 0) {
      const hasDefault = validatedConfig.profiles.some((p) => p.isDefault);
      if (!hasDefault) {
        validatedConfig.profiles[0].isDefault = true;
      }
      const defaultProfile = validatedConfig.profiles.find((p) => p.isDefault);
      validatedConfig.defaultProfile = defaultProfile == null ? void 0 : defaultProfile.id;
    }
    return validatedConfig;
  }
  /**
   * 生成配置文件ID
   */
  generateProfileId() {
    return `profile_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  /**
   * 获取配置目录路径
   */
  getConfigDir(projectPath) {
    return path__namespace.join(projectPath, this.CONFIG_DIR);
  }
  /**
   * 获取配置文件路径
   */
  getConfigPath(projectPath) {
    return path__namespace.join(this.getConfigDir(projectPath), this.CONFIG_FILE);
  }
}
const execAsync$1 = util.promisify(child_process.exec);
class RepositoryService {
  constructor() {
    this.GITHUB_API_BASE = "https://api.github.com";
    this.GITEE_API_BASE = "https://gitee.com/api/v5";
  }
  /**
   * 获取用户的仓库列表
   */
  async getUserRepositories(platform, username, token) {
    try {
      switch (platform) {
        case "github":
          return await this.getGitHubRepositories(username, token);
        case "gitee":
          return await this.getGiteeRepositories(username, token);
        default:
          throw new Error(`不支持的平台: ${platform}`);
      }
    } catch (error) {
      console.error("获取仓库列表失败:", error);
      throw new Error(`获取仓库列表失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 批量拉取仓库
   */
  async pullRepositories(repositories, targetDirectory, onProgress) {
    const clonedPaths = [];
    const errors = [];
    if (!fs__namespace.existsSync(targetDirectory)) {
      fs__namespace.mkdirSync(targetDirectory, { recursive: true });
    }
    for (let i = 0; i < repositories.length; i++) {
      const repo = repositories[i];
      try {
        onProgress == null ? void 0 : onProgress({
          total: repositories.length,
          completed: i,
          current: repo.name,
          status: "cloning",
          errors
        });
        const clonedPath = await this.cloneRepository(repo, targetDirectory);
        clonedPaths.push(clonedPath);
        console.log(`成功克隆仓库: ${repo.name}`);
      } catch (error) {
        const errorMsg = `克隆 ${repo.name} 失败: ${error instanceof Error ? error.message : "未知错误"}`;
        errors.push(errorMsg);
        console.error(errorMsg);
      }
    }
    onProgress == null ? void 0 : onProgress({
      total: repositories.length,
      completed: repositories.length,
      current: "",
      status: "completed",
      errors
    });
    return clonedPaths;
  }
  /**
   * 克隆单个仓库
   */
  async cloneRepository(repository, targetDirectory) {
    const repoPath = path__namespace.join(targetDirectory, repository.name);
    if (fs__namespace.existsSync(repoPath)) {
      throw new Error(`目录 ${repository.name} 已存在`);
    }
    try {
      const command = `git clone "${repository.cloneUrl}" "${repoPath}"`;
      await execAsync$1(command, { cwd: targetDirectory });
      return repoPath;
    } catch (error) {
      if (fs__namespace.existsSync(repoPath)) {
        try {
          fs__namespace.rmSync(repoPath, { recursive: true, force: true });
        } catch {
        }
      }
      throw error;
    }
  }
  /**
   * 获取GitHub仓库列表
   */
  async getGitHubRepositories(username, token) {
    var _a, _b;
    const headers = {
      "Accept": "application/vnd.github.v3+json",
      "User-Agent": "Project-Manager"
    };
    if (token) {
      headers["Authorization"] = `token ${token}`;
    }
    try {
      const response = await axios.get(`${this.GITHUB_API_BASE}/users/${username}/repos`, {
        headers,
        params: {
          per_page: 100,
          sort: "updated",
          direction: "desc"
        }
      });
      return response.data.map((repo) => ({
        id: repo.id.toString(),
        name: repo.name,
        fullName: repo.full_name,
        description: repo.description || "",
        url: repo.html_url,
        cloneUrl: repo.clone_url,
        language: repo.language || "Unknown",
        size: repo.size,
        starCount: repo.stargazers_count,
        forkCount: repo.forks_count,
        updatedAt: new Date(repo.updated_at),
        isPrivate: repo.private
      }));
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (((_a = error.response) == null ? void 0 : _a.status) === 404) {
          throw new Error(`用户 ${username} 不存在`);
        }
        if (((_b = error.response) == null ? void 0 : _b.status) === 403) {
          throw new Error("API访问受限，请检查Token或稍后重试");
        }
      }
      throw error;
    }
  }
  /**
   * 获取Gitee仓库列表
   */
  async getGiteeRepositories(username, token) {
    var _a, _b;
    const params = {
      per_page: 100,
      sort: "updated",
      direction: "desc"
    };
    if (token) {
      params.access_token = token;
    }
    try {
      const response = await axios.get(`${this.GITEE_API_BASE}/users/${username}/repos`, {
        params
      });
      return response.data.map((repo) => ({
        id: repo.id.toString(),
        name: repo.name,
        fullName: repo.full_name,
        description: repo.description || "",
        url: repo.html_url,
        cloneUrl: repo.clone_url,
        language: repo.language || "Unknown",
        size: repo.size,
        starCount: repo.stargazers_count,
        forkCount: repo.forks_count,
        updatedAt: new Date(repo.updated_at),
        isPrivate: repo.private
      }));
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (((_a = error.response) == null ? void 0 : _a.status) === 404) {
          throw new Error(`用户 ${username} 不存在`);
        }
        if (((_b = error.response) == null ? void 0 : _b.status) === 403) {
          throw new Error("API访问受限，请检查Token或稍后重试");
        }
      }
      throw error;
    }
  }
  /**
   * 过滤仓库列表
   */
  filterRepositories(repositories, filters) {
    if (!filters) {
      return repositories;
    }
    return repositories.filter((repo) => {
      if (filters.language && repo.language.toLowerCase() !== filters.language.toLowerCase()) {
        return false;
      }
      if (filters.maxSize && repo.size > filters.maxSize) {
        return false;
      }
      if (filters.minStars && repo.starCount < filters.minStars) {
        return false;
      }
      if (filters.updatedAfter && repo.updatedAt < filters.updatedAfter) {
        return false;
      }
      return true;
    });
  }
  /**
   * 验证Git是否可用
   */
  async validateGitAvailability() {
    try {
      await execAsync$1("git --version");
      return true;
    } catch {
      return false;
    }
  }
  /**
   * 验证网络连接
   */
  async validateNetworkConnection(platform) {
    try {
      const url = platform === "github" ? "https://github.com" : "https://gitee.com";
      await axios.get(url, { timeout: 5e3 });
      return true;
    } catch {
      return false;
    }
  }
  /**
   * 获取仓库详细信息
   */
  async getRepositoryDetails(platform, fullName, token) {
    try {
      const headers = {
        "User-Agent": "Project-Manager"
      };
      let url;
      if (platform === "github") {
        url = `${this.GITHUB_API_BASE}/repos/${fullName}`;
        if (token) {
          headers["Authorization"] = `token ${token}`;
        }
      } else {
        url = `${this.GITEE_API_BASE}/repos/${fullName}`;
        if (token) {
          url += `?access_token=${token}`;
        }
      }
      const response = await axios.get(url, { headers });
      const repo = response.data;
      return {
        id: repo.id.toString(),
        name: repo.name,
        fullName: repo.full_name,
        description: repo.description || "",
        url: repo.html_url,
        cloneUrl: repo.clone_url,
        language: repo.language || "Unknown",
        size: repo.size,
        starCount: repo.stargazers_count,
        forkCount: repo.forks_count,
        updatedAt: new Date(repo.updated_at),
        isPrivate: repo.private
      };
    } catch (error) {
      console.error("获取仓库详情失败:", error);
      return null;
    }
  }
}
class ExecutionHistoryService {
  constructor(databaseService) {
    this.databaseService = databaseService;
  }
  /**
   * 初始化执行历史表
   */
  async initialize() {
    try {
      const tableInfo = await this.databaseService.get(
        "SELECT sql FROM sqlite_master WHERE type='table' AND name='execution_history'"
      );
      if (tableInfo && tableInfo.sql && tableInfo.sql.includes("FOREIGN KEY")) {
        const existingData = await this.databaseService.all("SELECT * FROM execution_history");
        await this.databaseService.run("DROP TABLE execution_history");
        const createTableQuery = `
          CREATE TABLE execution_history (
            id TEXT PRIMARY KEY,
            project_id TEXT NOT NULL,
            profile_id TEXT,
            command TEXT NOT NULL,
            args TEXT,
            working_directory TEXT,
            environment TEXT,
            start_time DATETIME NOT NULL,
            end_time DATETIME,
            duration INTEGER,
            status TEXT NOT NULL,
            exit_code INTEGER,
            output TEXT,
            error_output TEXT,
            pid INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
          )
        `;
        await this.databaseService.run(createTableQuery);
        if (existingData.length > 0) {
          const insertQuery = `
            INSERT INTO execution_history (
              id, project_id, profile_id, command, args, working_directory,
              environment, start_time, end_time, duration, status, exit_code,
              output, error_output, pid, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `;
          for (const row of existingData) {
            await this.databaseService.run(insertQuery, [
              row.id,
              row.project_id,
              row.profile_id,
              row.command,
              row.args,
              row.working_directory,
              row.environment,
              row.start_time,
              row.end_time,
              row.duration,
              row.status,
              row.exit_code,
              row.output,
              row.error_output,
              row.pid,
              row.created_at
            ]);
          }
        }
      } else {
        const createTableQuery = `
          CREATE TABLE IF NOT EXISTS execution_history (
            id TEXT PRIMARY KEY,
            project_id TEXT NOT NULL,
            profile_id TEXT,
            command TEXT NOT NULL,
            args TEXT,
            working_directory TEXT,
            environment TEXT,
            start_time DATETIME NOT NULL,
            end_time DATETIME,
            duration INTEGER,
            status TEXT NOT NULL,
            exit_code INTEGER,
            output TEXT,
            error_output TEXT,
            pid INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
          )
        `;
        await this.databaseService.run(createTableQuery);
      }
      const createIndexQueries = [
        "CREATE INDEX IF NOT EXISTS idx_execution_project_id ON execution_history (project_id)",
        "CREATE INDEX IF NOT EXISTS idx_execution_status ON execution_history (status)",
        "CREATE INDEX IF NOT EXISTS idx_execution_start_time ON execution_history (start_time)",
        "CREATE INDEX IF NOT EXISTS idx_execution_created_at ON execution_history (created_at)"
      ];
      for (const query of createIndexQueries) {
        await this.databaseService.run(query);
      }
      console.log("ExecutionHistoryService initialized successfully");
    } catch (error) {
      console.error("Failed to initialize ExecutionHistoryService:", error);
    }
  }
  /**
   * 记录执行开始
   */
  async recordExecutionStart(projectId, command, args = [], workingDirectory, environment = {}, profileId, pid) {
    const id = this.generateExecutionId();
    const startTime = /* @__PURE__ */ new Date();
    const query = `
      INSERT INTO execution_history (
        id, project_id, profile_id, command, args, working_directory, 
        environment, start_time, status, pid, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    const params = [
      id,
      projectId,
      profileId || null,
      command,
      JSON.stringify(args),
      workingDirectory,
      JSON.stringify(environment),
      startTime.toISOString(),
      "running",
      pid || null,
      startTime.toISOString()
    ];
    try {
      await this.databaseService.run(query, params);
      return id;
    } catch (error) {
      console.error("Failed to record execution start:", error);
      throw error;
    }
  }
  /**
   * 记录执行结束
   */
  async recordExecutionEnd(executionId, status, exitCode, output = "", errorOutput = "") {
    const endTime = /* @__PURE__ */ new Date();
    const getStartTimeQuery = "SELECT start_time FROM execution_history WHERE id = ?";
    const startTimeResult = await this.databaseService.get(getStartTimeQuery, [executionId]);
    let duration;
    if (startTimeResult) {
      const startTime = new Date(startTimeResult.start_time);
      duration = endTime.getTime() - startTime.getTime();
    }
    const query = `
      UPDATE execution_history 
      SET end_time = ?, duration = ?, status = ?, exit_code = ?, output = ?, error_output = ?
      WHERE id = ?
    `;
    const params = [
      endTime.toISOString(),
      duration || null,
      status,
      exitCode || null,
      output,
      errorOutput,
      executionId
    ];
    await this.databaseService.run(query, params);
  }
  /**
   * 更新执行PID
   */
  async updateExecutionPid(executionId, pid) {
    const query = "UPDATE execution_history SET pid = ? WHERE id = ?";
    await this.databaseService.run(query, [pid, executionId]);
  }
  /**
   * 更新执行输出
   */
  async updateExecutionOutput(executionId, output, errorOutput) {
    let query = "UPDATE execution_history SET output = ? WHERE id = ?";
    let params = [output, executionId];
    if (errorOutput !== void 0) {
      query = "UPDATE execution_history SET output = ?, error_output = ? WHERE id = ?";
      params = [output, errorOutput, executionId];
    }
    await this.databaseService.run(query, params);
  }
  /**
   * 获取执行历史记录
   */
  async getExecutionHistory(filter = {}) {
    let query = `
      SELECT * FROM execution_history
      WHERE 1=1
    `;
    const params = [];
    if (filter.projectId) {
      query += " AND project_id = ?";
      params.push(filter.projectId);
    }
    if (filter.status) {
      query += " AND status = ?";
      params.push(filter.status);
    }
    if (filter.dateFrom) {
      query += " AND start_time >= ?";
      params.push(filter.dateFrom.toISOString());
    }
    if (filter.dateTo) {
      query += " AND start_time <= ?";
      params.push(filter.dateTo.toISOString());
    }
    if (filter.command) {
      query += " AND command LIKE ?";
      params.push(`%${filter.command}%`);
    }
    query += " ORDER BY start_time DESC";
    if (filter.limit) {
      query += " LIMIT ?";
      params.push(filter.limit);
      if (filter.offset) {
        query += " OFFSET ?";
        params.push(filter.offset);
      }
    }
    const rows = await this.databaseService.all(query, params);
    return rows.map((row) => this.mapRowToExecutionRecord(row));
  }
  /**
   * 获取单个执行记录
   */
  async getExecutionRecord(executionId) {
    const query = "SELECT * FROM execution_history WHERE id = ?";
    const row = await this.databaseService.get(query, [executionId]);
    return row ? this.mapRowToExecutionRecord(row) : null;
  }
  /**
   * 获取项目执行统计
   */
  async getExecutionStats(projectId) {
    const statsQuery = `
      SELECT 
        COUNT(*) as total_executions,
        SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success_count,
        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failure_count,
        AVG(duration) as average_duration,
        MAX(start_time) as last_execution
      FROM execution_history 
      WHERE project_id = ?
    `;
    const statsResult = await this.databaseService.get(statsQuery, [projectId]);
    const commandQuery = `
      SELECT command, COUNT(*) as count 
      FROM execution_history 
      WHERE project_id = ? 
      GROUP BY command 
      ORDER BY count DESC 
      LIMIT 1
    `;
    const commandResult = await this.databaseService.get(commandQuery, [projectId]);
    return {
      totalExecutions: (statsResult == null ? void 0 : statsResult.total_executions) || 0,
      successCount: (statsResult == null ? void 0 : statsResult.success_count) || 0,
      failureCount: (statsResult == null ? void 0 : statsResult.failure_count) || 0,
      averageDuration: (statsResult == null ? void 0 : statsResult.average_duration) || 0,
      lastExecution: (statsResult == null ? void 0 : statsResult.last_execution) ? new Date(statsResult.last_execution) : void 0,
      mostUsedCommand: commandResult == null ? void 0 : commandResult.command
    };
  }
  /**
   * 删除执行记录
   */
  async deleteExecutionRecord(executionId) {
    const query = "DELETE FROM execution_history WHERE id = ?";
    await this.databaseService.run(query, [executionId]);
  }
  /**
   * 删除项目的所有执行记录
   */
  async deleteProjectExecutionHistory(projectId) {
    const query = "DELETE FROM execution_history WHERE project_id = ?";
    await this.databaseService.run(query, [projectId]);
  }
  /**
   * 清理旧的执行记录
   */
  async cleanupOldRecords(daysToKeep = 30) {
    const cutoffDate = /* @__PURE__ */ new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    const query = "DELETE FROM execution_history WHERE created_at < ?";
    const result = await this.databaseService.run(query, [cutoffDate.toISOString()]);
    return result.changes || 0;
  }
  /**
   * 获取正在运行的执行记录
   */
  async getRunningExecutions() {
    const query = 'SELECT * FROM execution_history WHERE status = "running" ORDER BY start_time DESC';
    const rows = await this.databaseService.all(query);
    return rows.map((row) => this.mapRowToExecutionRecord(row));
  }
  /**
   * 将数据库行映射为执行记录对象
   */
  mapRowToExecutionRecord(row) {
    return {
      id: row.id,
      projectId: row.project_id,
      profileId: row.profile_id,
      command: row.command,
      args: row.args ? JSON.parse(row.args) : [],
      workingDirectory: row.working_directory,
      environment: row.environment ? JSON.parse(row.environment) : {},
      startTime: new Date(row.start_time),
      endTime: row.end_time ? new Date(row.end_time) : void 0,
      duration: row.duration,
      status: row.status,
      exitCode: row.exit_code,
      output: row.output || "",
      errorOutput: row.error_output || "",
      pid: row.pid,
      createdAt: new Date(row.created_at)
    };
  }
  /**
   * 生成执行记录ID
   */
  generateExecutionId() {
    return `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
class ProjectLogService extends events.EventEmitter {
  // 每个项目最多保留1000条日志
  constructor(databaseService) {
    super();
    this.projectLogs = /* @__PURE__ */ new Map();
    this.maxLogsPerProject = 1e3;
    this.databaseService = databaseService;
  }
  /**
   * 初始化日志服务
   */
  async initialize() {
    try {
      const createTableQuery = `
        CREATE TABLE IF NOT EXISTS project_logs (
          id TEXT PRIMARY KEY,
          project_id TEXT NOT NULL,
          timestamp DATETIME NOT NULL,
          type TEXT NOT NULL,
          content TEXT NOT NULL,
          level TEXT NOT NULL,
          pid INTEGER,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
        )
      `;
      await this.databaseService.run(createTableQuery);
      const createIndexQueries = [
        "CREATE INDEX IF NOT EXISTS idx_project_logs_project_id ON project_logs (project_id)",
        "CREATE INDEX IF NOT EXISTS idx_project_logs_timestamp ON project_logs (timestamp)",
        "CREATE INDEX IF NOT EXISTS idx_project_logs_created_at ON project_logs (created_at)"
      ];
      for (const query of createIndexQueries) {
        await this.databaseService.run(query);
      }
      console.log("ProjectLogService initialized successfully");
    } catch (error) {
      console.error("Failed to initialize ProjectLogService:", error);
      throw error;
    }
  }
  /**
   * 添加日志条目
   */
  async addLog(projectId, type, content, level = "info", pid) {
    const logEntry = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      projectId,
      timestamp: /* @__PURE__ */ new Date(),
      type,
      content: content.trim(),
      level,
      pid
    };
    if (!this.projectLogs.has(projectId)) {
      this.projectLogs.set(projectId, []);
    }
    const logs = this.projectLogs.get(projectId);
    logs.push(logEntry);
    if (logs.length > this.maxLogsPerProject) {
      logs.splice(0, logs.length - this.maxLogsPerProject);
    }
    try {
      const query = `
        INSERT INTO project_logs (id, project_id, timestamp, type, content, level, pid)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `;
      await this.databaseService.run(query, [
        logEntry.id,
        logEntry.projectId,
        logEntry.timestamp.toISOString(),
        logEntry.type,
        logEntry.content,
        logEntry.level,
        logEntry.pid || null
      ]);
    } catch (error) {
      console.error("Failed to save log to database:", error);
    }
    this.emit("log-added", logEntry);
  }
  /**
   * 获取项目的日志历史
   */
  async getProjectLogs(projectId, limit = 500) {
    const cachedLogs = this.projectLogs.get(projectId);
    if (cachedLogs && cachedLogs.length > 0) {
      return cachedLogs.slice(-limit);
    }
    try {
      const query = `
        SELECT * FROM project_logs 
        WHERE project_id = ? 
        ORDER BY timestamp DESC 
        LIMIT ?
      `;
      const rows = await this.databaseService.all(query, [projectId, limit]);
      const logs = rows.map((row) => ({
        id: row.id,
        projectId: row.project_id,
        timestamp: new Date(row.timestamp),
        type: row.type,
        content: row.content,
        level: row.level,
        pid: row.pid
      })).reverse();
      this.projectLogs.set(projectId, logs);
      return logs;
    } catch (error) {
      console.error("Failed to get project logs from database:", error);
      return [];
    }
  }
  /**
   * 清空项目日志
   */
  async clearProjectLogs(projectId) {
    try {
      const query = "DELETE FROM project_logs WHERE project_id = ?";
      await this.databaseService.run(query, [projectId]);
      this.projectLogs.delete(projectId);
      console.log(`Cleared logs for project ${projectId}`);
    } catch (error) {
      console.error("Failed to clear project logs:", error);
      throw error;
    }
  }
  /**
   * 清理旧日志（保留最近的日志）
   */
  async cleanupOldLogs(projectId, keepCount = 1e3) {
    try {
      const query = `
        DELETE FROM project_logs 
        WHERE project_id = ? 
        AND id NOT IN (
          SELECT id FROM project_logs 
          WHERE project_id = ? 
          ORDER BY timestamp DESC 
          LIMIT ?
        )
      `;
      await this.databaseService.run(query, [projectId, projectId, keepCount]);
      console.log(`Cleaned up old logs for project ${projectId}, kept ${keepCount} recent logs`);
    } catch (error) {
      console.error("Failed to cleanup old logs:", error);
    }
  }
  /**
   * 获取内存中的实时日志
   */
  getRealtimeLogs(projectId) {
    return this.projectLogs.get(projectId) || [];
  }
  /**
   * 添加系统日志
   */
  async addSystemLog(projectId, message, level = "info") {
    await this.addLog(projectId, "system", message, level);
  }
}
class GitConfigService {
  constructor(databaseService) {
    this.databaseService = databaseService;
  }
  /**
   * 初始化Git配置表
   */
  async initialize() {
    try {
      const createTableQuery = `
        CREATE TABLE IF NOT EXISTS git_configs (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          username TEXT NOT NULL,
          token TEXT NOT NULL,
          base_url TEXT NOT NULL,
          provider TEXT NOT NULL,
          is_default INTEGER DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `;
      await this.databaseService.run(createTableQuery);
      const createUsernameHistoryQuery = `
        CREATE TABLE IF NOT EXISTS username_history (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          username TEXT NOT NULL UNIQUE,
          provider TEXT NOT NULL,
          last_used DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `;
      await this.databaseService.run(createUsernameHistoryQuery);
      console.log("GitConfigService: Checking token_history table...");
      const tableInfo = await this.databaseService.all("PRAGMA table_info(token_history)");
      const hasUsernameColumn = tableInfo.some((col) => col.name === "username");
      if (hasUsernameColumn) {
        console.log("GitConfigService: Migrating token_history table to remove username column...");
        const oldData = await this.databaseService.all("SELECT * FROM token_history");
        await this.databaseService.run("DROP TABLE IF EXISTS token_history");
        const createTokenHistoryQuery = `
          CREATE TABLE token_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            provider TEXT NOT NULL,
            token TEXT NOT NULL,
            token_name TEXT,
            last_used DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(provider, token)
          )
        `;
        await this.databaseService.run(createTokenHistoryQuery);
        const uniqueTokens = /* @__PURE__ */ new Map();
        for (const row of oldData) {
          const key = `${row.provider}-${row.token}`;
          if (!uniqueTokens.has(key) || new Date(row.last_used) > new Date(uniqueTokens.get(key).last_used)) {
            uniqueTokens.set(key, row);
          }
        }
        for (const row of uniqueTokens.values()) {
          await this.databaseService.run(
            "INSERT INTO token_history (provider, token, token_name, last_used) VALUES (?, ?, ?, ?)",
            [row.provider, row.token, row.token_name, row.last_used]
          );
        }
        console.log("GitConfigService: token_history table migrated successfully");
      } else {
        const createTokenHistoryQuery = `
          CREATE TABLE IF NOT EXISTS token_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            provider TEXT NOT NULL,
            token TEXT NOT NULL,
            token_name TEXT,
            last_used DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(provider, token)
          )
        `;
        console.log("GitConfigService: Creating token_history table...");
        await this.databaseService.run(createTokenHistoryQuery);
        console.log("GitConfigService: token_history table created successfully");
      }
      const createDirectoryHistoryQuery = `
        CREATE TABLE IF NOT EXISTS directory_history (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          directory_path TEXT NOT NULL UNIQUE,
          last_used DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `;
      console.log("GitConfigService: Creating directory_history table...");
      await this.databaseService.run(createDirectoryHistoryQuery);
      console.log("GitConfigService: directory_history table created successfully");
      const createIndexQueries = [
        "CREATE INDEX IF NOT EXISTS idx_git_configs_provider ON git_configs (provider)",
        "CREATE INDEX IF NOT EXISTS idx_git_configs_username ON git_configs (username)",
        "CREATE INDEX IF NOT EXISTS idx_git_configs_is_default ON git_configs (is_default)",
        "CREATE INDEX IF NOT EXISTS idx_username_history_provider ON username_history (provider)",
        "CREATE INDEX IF NOT EXISTS idx_username_history_last_used ON username_history (last_used)",
        "CREATE INDEX IF NOT EXISTS idx_token_history_provider ON token_history (provider)",
        "CREATE INDEX IF NOT EXISTS idx_token_history_last_used ON token_history (last_used)",
        "CREATE INDEX IF NOT EXISTS idx_directory_history_last_used ON directory_history (last_used)"
      ];
      for (const query of createIndexQueries) {
        await this.databaseService.run(query);
      }
      console.log("GitConfigService initialized successfully");
    } catch (error) {
      console.error("Failed to initialize GitConfigService:", error);
    }
  }
  /**
   * 添加Git配置
   */
  async addGitConfig(config) {
    const id = uuid.v4();
    const now = /* @__PURE__ */ new Date();
    if (config.isDefault) {
      await this.databaseService.run("UPDATE git_configs SET is_default = 0");
    }
    const query = `
      INSERT INTO git_configs (
        id, name, username, token, base_url, provider, is_default, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    const params = [
      id,
      config.name,
      config.username,
      config.token,
      config.baseUrl,
      config.provider,
      config.isDefault ? 1 : 0,
      now.toISOString(),
      now.toISOString()
    ];
    await this.databaseService.run(query, params);
    return id;
  }
  /**
   * 更新Git配置
   */
  async updateGitConfig(id, updates) {
    const now = /* @__PURE__ */ new Date();
    if (updates.isDefault) {
      await this.databaseService.run("UPDATE git_configs SET is_default = 0");
    }
    const fields = [];
    const params = [];
    if (updates.name !== void 0) {
      fields.push("name = ?");
      params.push(updates.name);
    }
    if (updates.username !== void 0) {
      fields.push("username = ?");
      params.push(updates.username);
    }
    if (updates.token !== void 0) {
      fields.push("token = ?");
      params.push(updates.token);
    }
    if (updates.baseUrl !== void 0) {
      fields.push("base_url = ?");
      params.push(updates.baseUrl);
    }
    if (updates.provider !== void 0) {
      fields.push("provider = ?");
      params.push(updates.provider);
    }
    if (updates.isDefault !== void 0) {
      fields.push("is_default = ?");
      params.push(updates.isDefault ? 1 : 0);
    }
    fields.push("updated_at = ?");
    params.push(now.toISOString());
    params.push(id);
    const query = `UPDATE git_configs SET ${fields.join(", ")} WHERE id = ?`;
    await this.databaseService.run(query, params);
  }
  /**
   * 获取Git配置列表
   */
  async getGitConfigs(filter = {}) {
    let query = "SELECT * FROM git_configs WHERE 1=1";
    const params = [];
    if (filter.provider) {
      query += " AND provider = ?";
      params.push(filter.provider);
    }
    if (filter.username) {
      query += " AND username LIKE ?";
      params.push(`%${filter.username}%`);
    }
    query += " ORDER BY is_default DESC, created_at DESC";
    if (filter.limit) {
      query += " LIMIT ?";
      params.push(filter.limit);
      if (filter.offset) {
        query += " OFFSET ?";
        params.push(filter.offset);
      }
    }
    const rows = await this.databaseService.all(query, params);
    return rows.map((row) => this.mapRowToGitConfig(row));
  }
  /**
   * 获取单个Git配置
   */
  async getGitConfig(id) {
    const query = "SELECT * FROM git_configs WHERE id = ?";
    const row = await this.databaseService.get(query, [id]);
    return row ? this.mapRowToGitConfig(row) : null;
  }
  /**
   * 获取默认Git配置
   */
  async getDefaultGitConfig() {
    const query = "SELECT * FROM git_configs WHERE is_default = 1 LIMIT 1";
    const row = await this.databaseService.get(query);
    return row ? this.mapRowToGitConfig(row) : null;
  }
  /**
   * 删除Git配置
   */
  async deleteGitConfig(id) {
    const query = "DELETE FROM git_configs WHERE id = ?";
    await this.databaseService.run(query, [id]);
  }
  /**
   * 设置默认Git配置
   */
  async setDefaultGitConfig(id) {
    await this.databaseService.run("UPDATE git_configs SET is_default = 0");
    await this.databaseService.run("UPDATE git_configs SET is_default = 1 WHERE id = ?", [id]);
  }
  /**
   * 保存用户名到历史记录
   */
  async saveUsernameHistory(username, provider) {
    const query = `
      INSERT OR REPLACE INTO username_history (username, provider, last_used)
      VALUES (?, ?, CURRENT_TIMESTAMP)
    `;
    await this.databaseService.run(query, [username, provider]);
  }
  /**
   * 获取用户名历史记录
   */
  async getUsernameHistory(provider, limit = 10) {
    let query = "SELECT username FROM username_history";
    const params = [];
    if (provider) {
      query += " WHERE provider = ?";
      params.push(provider);
    }
    query += " ORDER BY last_used DESC LIMIT ?";
    params.push(limit);
    const rows = await this.databaseService.all(query, params);
    return rows.map((row) => row.username);
  }
  /**
   * 清理旧的用户名历史记录
   */
  async cleanupUsernameHistory(keepCount = 50) {
    const query = `
      DELETE FROM username_history
      WHERE id NOT IN (
        SELECT id FROM username_history
        ORDER BY last_used DESC
        LIMIT ?
      )
    `;
    await this.databaseService.run(query, [keepCount]);
  }
  /**
   * 保存令牌到历史记录
   */
  async saveTokenHistory(provider, token, tokenName) {
    try {
      console.log("GitConfigService: Saving token history", { provider, tokenLength: token.length, tokenName });
      const query = `
        INSERT OR REPLACE INTO token_history (provider, token, token_name, last_used)
        VALUES (?, ?, ?, CURRENT_TIMESTAMP)
      `;
      const result = await this.databaseService.run(query, [provider, token, tokenName || null]);
      console.log("GitConfigService: Token saved successfully", result);
    } catch (error) {
      console.error("GitConfigService: Failed to save token history", error);
      throw error;
    }
  }
  /**
   * 获取令牌历史记录
   */
  async getTokenHistory(provider, limit = 10) {
    try {
      console.log("GitConfigService: Getting token history", { provider, limit });
      const query = `
        SELECT token, token_name, last_used
        FROM token_history
        WHERE provider = ?
        ORDER BY last_used DESC
        LIMIT ?
      `;
      const rows = await this.databaseService.all(query, [provider, limit]);
      console.log("GitConfigService: Token history retrieved", rows);
      return rows.map((row) => ({
        token: row.token,
        tokenName: row.token_name,
        lastUsed: row.last_used
      }));
    } catch (error) {
      console.error("GitConfigService: Failed to get token history", error);
      throw error;
    }
  }
  /**
   * 删除令牌历史记录
   */
  async deleteTokenHistory(provider, token) {
    const query = `
      DELETE FROM token_history
      WHERE provider = ? AND token = ?
    `;
    await this.databaseService.run(query, [provider, token]);
  }
  /**
   * 清理旧的令牌历史记录
   */
  async cleanupTokenHistory(keepCount = 50) {
    const query = `
      DELETE FROM token_history
      WHERE id NOT IN (
        SELECT id FROM token_history
        ORDER BY last_used DESC
        LIMIT ?
      )
    `;
    await this.databaseService.run(query, [keepCount]);
  }
  /**
   * 保存目录到历史记录
   */
  async saveDirectoryHistory(directoryPath) {
    try {
      console.log("GitConfigService: Saving directory history", { directoryPath });
      const query = `
        INSERT OR REPLACE INTO directory_history (directory_path, last_used)
        VALUES (?, CURRENT_TIMESTAMP)
      `;
      const result = await this.databaseService.run(query, [directoryPath]);
      console.log("GitConfigService: Directory saved successfully", result);
    } catch (error) {
      console.error("GitConfigService: Failed to save directory history", error);
      throw error;
    }
  }
  /**
   * 获取目录历史记录
   */
  async getDirectoryHistory(limit = 10) {
    try {
      console.log("GitConfigService: Getting directory history", { limit });
      const query = `
        SELECT directory_path
        FROM directory_history
        ORDER BY last_used DESC
        LIMIT ?
      `;
      const rows = await this.databaseService.all(query, [limit]);
      console.log("GitConfigService: Directory history retrieved", rows);
      return rows.map((row) => row.directory_path);
    } catch (error) {
      console.error("GitConfigService: Failed to get directory history", error);
      throw error;
    }
  }
  /**
   * 获取最后使用的目录
   */
  async getLastUsedDirectory() {
    try {
      const query = `
        SELECT directory_path
        FROM directory_history
        ORDER BY last_used DESC
        LIMIT 1
      `;
      const row = await this.databaseService.get(query);
      return row ? row.directory_path : null;
    } catch (error) {
      console.error("GitConfigService: Failed to get last used directory", error);
      return null;
    }
  }
  /**
   * 清理旧的目录历史记录
   */
  async cleanupDirectoryHistory(keepCount = 20) {
    const query = `
      DELETE FROM directory_history
      WHERE id NOT IN (
        SELECT id FROM directory_history
        ORDER BY last_used DESC
        LIMIT ?
      )
    `;
    await this.databaseService.run(query, [keepCount]);
  }
  /**
   * 将数据库行映射为Git配置对象
   */
  mapRowToGitConfig(row) {
    return {
      id: row.id,
      name: row.name,
      username: row.username,
      token: row.token,
      baseUrl: row.base_url,
      provider: row.provider,
      isDefault: Boolean(row.is_default),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }
}
class GitRepositoryService {
  constructor(gitConfigService, projectService) {
    this.gitConfigService = gitConfigService;
    this.projectService = projectService;
  }
  /**
   * 获取仓库列表
   */
  async getRepositories(configId, filter = {}) {
    const config = await this.gitConfigService.getGitConfig(configId);
    if (!config) {
      throw new Error("Git configuration not found");
    }
    switch (config.provider) {
      case "github":
        return this.getGitHubRepositories(config, filter);
      case "gitlab":
        return this.getGitLabRepositories(config, filter);
      case "gitee":
        return this.getGiteeRepositories(config, filter);
      default:
        throw new Error(`Unsupported provider: ${config.provider}`);
    }
  }
  /**
   * 获取GitHub仓库
   */
  async getGitHubRepositories(config, filter) {
    const baseUrl = config.baseUrl || "https://api.github.com";
    const headers = {
      "Authorization": `token ${config.token}`,
      "Accept": "application/vnd.github.v3+json"
    };
    let url = `${baseUrl}/user/repos`;
    const params = new URLSearchParams();
    if (filter.type) params.append("type", filter.type);
    if (filter.sort) params.append("sort", filter.sort);
    if (filter.order) params.append("direction", filter.order);
    if (filter.limit) params.append("per_page", filter.limit.toString());
    if (filter.page) params.append("page", filter.page.toString());
    if (params.toString()) {
      url += `?${params.toString()}`;
    }
    const response = await axios.get(url, { headers });
    return response.data.map((repo) => ({
      id: repo.id.toString(),
      name: repo.name,
      fullName: repo.full_name,
      description: repo.description,
      url: repo.html_url,
      cloneUrl: repo.clone_url,
      sshUrl: repo.ssh_url,
      defaultBranch: repo.default_branch,
      isPrivate: repo.private,
      language: repo.language,
      size: repo.size,
      starCount: repo.stargazers_count,
      forkCount: repo.forks_count,
      updatedAt: new Date(repo.updated_at),
      owner: {
        login: repo.owner.login,
        avatarUrl: repo.owner.avatar_url
      }
    }));
  }
  /**
   * 获取GitLab仓库
   */
  async getGitLabRepositories(config, filter) {
    const baseUrl = config.baseUrl || "https://gitlab.com/api/v4";
    const headers = {
      "Authorization": `Bearer ${config.token}`
    };
    let url = `${baseUrl}/projects`;
    const params = new URLSearchParams();
    params.append("membership", "true");
    if (filter.sort) params.append("order_by", filter.sort === "updated" ? "last_activity_at" : filter.sort);
    if (filter.order) params.append("sort", filter.order);
    if (filter.limit) params.append("per_page", filter.limit.toString());
    if (filter.page) params.append("page", filter.page.toString());
    url += `?${params.toString()}`;
    const response = await axios.get(url, { headers });
    return response.data.map((repo) => ({
      id: repo.id.toString(),
      name: repo.name,
      fullName: repo.path_with_namespace,
      description: repo.description,
      url: repo.web_url,
      cloneUrl: repo.http_url_to_repo,
      sshUrl: repo.ssh_url_to_repo,
      defaultBranch: repo.default_branch,
      isPrivate: repo.visibility === "private",
      language: repo.language,
      size: 0,
      // GitLab API doesn't provide size
      starCount: repo.star_count,
      forkCount: repo.forks_count,
      updatedAt: new Date(repo.last_activity_at),
      owner: {
        login: repo.namespace.name,
        avatarUrl: repo.namespace.avatar_url
      }
    }));
  }
  /**
   * 获取Gitee仓库
   */
  async getGiteeRepositories(config, filter) {
    const baseUrl = config.baseUrl || "https://gitee.com/api/v5";
    const headers = {
      "Authorization": `token ${config.token}`
    };
    let url = `${baseUrl}/user/repos`;
    const params = new URLSearchParams();
    if (filter.type) params.append("type", filter.type);
    if (filter.sort) params.append("sort", filter.sort);
    if (filter.order) params.append("direction", filter.order);
    if (filter.limit) params.append("per_page", filter.limit.toString());
    if (filter.page) params.append("page", filter.page.toString());
    url += `?${params.toString()}`;
    const response = await axios.get(url, { headers });
    return response.data.map((repo) => ({
      id: repo.id.toString(),
      name: repo.name,
      fullName: repo.full_name,
      description: repo.description,
      url: repo.html_url,
      cloneUrl: repo.clone_url,
      sshUrl: repo.ssh_url,
      defaultBranch: repo.default_branch,
      isPrivate: repo.private,
      language: repo.language,
      size: repo.size,
      starCount: repo.stargazers_count,
      forkCount: repo.forks_count,
      updatedAt: new Date(repo.updated_at),
      owner: {
        login: repo.owner.login,
        avatarUrl: repo.owner.avatar_url
      }
    }));
  }
  /**
   * 拉取项目
   */
  async pullProjects(request, onProgress) {
    const results = [];
    if (!fs__namespace.existsSync(request.targetDirectory)) {
      fs__namespace.mkdirSync(request.targetDirectory, { recursive: true });
    }
    for (const repo of request.repositories) {
      const progress = {
        repositoryId: repo.id,
        repositoryName: repo.name,
        status: "pending",
        progress: 0
      };
      results.push(progress);
      onProgress == null ? void 0 : onProgress(progress);
      try {
        progress.status = "cloning";
        progress.message = "Starting clone...";
        onProgress == null ? void 0 : onProgress(progress);
        const localPath = path__namespace.join(request.targetDirectory, repo.name);
        if (fs__namespace.existsSync(localPath)) {
          progress.status = "failed";
          progress.error = "Directory already exists";
          onProgress == null ? void 0 : onProgress(progress);
          continue;
        }
        const cloneUrl = request.cloneMethod === "ssh" ? repo.sshUrl : repo.cloneUrl;
        await this.cloneRepository(cloneUrl, localPath, request, progress, onProgress);
        progress.status = "success";
        progress.progress = 100;
        progress.localPath = localPath;
        progress.message = "Clone completed";
        onProgress == null ? void 0 : onProgress(progress);
        await this.addToProjectList(repo, localPath);
      } catch (error) {
        progress.status = "failed";
        progress.error = error instanceof Error ? error.message : String(error);
        onProgress == null ? void 0 : onProgress(progress);
      }
    }
    return results;
  }
  /**
   * 克隆仓库
   */
  async cloneRepository(cloneUrl, localPath, request, progress, onProgress) {
    return new Promise((resolve, reject) => {
      const args = ["clone"];
      if (request.shallow && request.depth) {
        args.push("--depth", request.depth.toString());
      }
      if (request.includeSubmodules) {
        args.push("--recurse-submodules");
      }
      args.push(cloneUrl, localPath);
      const gitProcess = child_process.spawn("git", args);
      gitProcess.stderr.on("data", (data) => {
        const output = data.toString();
        const progressMatch = output.match(/(\d+)%/);
        if (progressMatch) {
          progress.progress = parseInt(progressMatch[1]);
          progress.message = output.trim();
          onProgress == null ? void 0 : onProgress(progress);
        }
      });
      gitProcess.on("close", (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Git clone failed with code ${code}`));
        }
      });
      gitProcess.on("error", (error) => {
        reject(error);
      });
    });
  }
  /**
   * 添加到项目列表
   */
  async addToProjectList(repo, localPath) {
    try {
      await this.projectService.addProject({
        name: repo.name,
        path: localPath,
        description: repo.description || `从 ${repo.fullName} 拉取的项目`,
        type: "other",
        // 将在后续检测中确定类型
        status: "STOPPED",
        tags: ["git-pulled", repo.owner.login],
        config: {
          startCommand: "",
          workingDirectory: localPath,
          environment: {},
          profiles: []
        },
        gitStatus: {
          isGitRepo: true,
          currentBranch: repo.defaultBranch,
          hasUncommittedChanges: false,
          hasUnpushedCommits: false,
          remoteUrl: repo.cloneUrl
        },
        dependencyStatus: "UNKNOWN",
        environmentStatus: "UNKNOWN"
      });
      const projects = await this.projectService.getProjects();
      const addedProject = projects.find((p) => p.path === localPath);
      if (addedProject) {
        this.projectService.checkProjectDependencies(addedProject.id).catch(console.error);
        this.projectService.checkProjectEnvironment(addedProject.id).catch(console.error);
      }
    } catch (error) {
      console.error("Failed to add project to list:", error);
    }
  }
}
const readdir = util.promisify(fs__namespace.readdir);
const stat = util.promisify(fs__namespace.stat);
class ProjectAnalysisService {
  /**
   * 分析项目文件结构
   */
  async analyzeProjectFiles(projectPath) {
    try {
      const fileTypes = {};
      const largestFiles = [];
      let totalFiles = 0;
      let totalSize = 0;
      await this.scanDirectory(projectPath, projectPath, fileTypes, largestFiles, (size) => {
        totalFiles++;
        totalSize += size;
      });
      largestFiles.sort((a, b) => b.size - a.size);
      const topLargestFiles = largestFiles.slice(0, 10);
      return {
        totalFiles,
        totalSize,
        fileTypes,
        largestFiles: topLargestFiles
      };
    } catch (error) {
      console.error("分析项目文件失败:", error);
      throw new Error(`分析项目文件失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 获取项目环境信息
   */
  async getProjectEnvironment(projectPath) {
    try {
      const environment = {
        environmentVariables: {},
        systemInfo: {
          platform: process.platform,
          arch: process.arch,
          nodeVersion: process.version
        }
      };
      try {
        const { exec } = require("child_process");
        const { promisify: promisify2 } = require("util");
        const execAsync2 = promisify2(exec);
        const { stdout: pythonVersion } = await execAsync2("python --version", { cwd: projectPath });
        environment.pythonVersion = pythonVersion.trim();
      } catch {
      }
      try {
        const { exec } = require("child_process");
        const { promisify: promisify2 } = require("util");
        const execAsync2 = promisify2(exec);
        const { stdout: nodeVersion } = await execAsync2("node --version", { cwd: projectPath });
        environment.nodeVersion = nodeVersion.trim();
      } catch {
      }
      try {
        const { exec } = require("child_process");
        const { promisify: promisify2 } = require("util");
        const execAsync2 = promisify2(exec);
        const { stdout: gitVersion } = await execAsync2("git --version", { cwd: projectPath });
        environment.gitVersion = gitVersion.trim();
      } catch {
      }
      const venvDirs = ["venv", ".venv", "env", ".env"];
      for (const dir of venvDirs) {
        const venvPath = path__namespace.join(projectPath, dir);
        if (fs__namespace.existsSync(venvPath)) {
          environment.virtualEnv = dir;
          break;
        }
      }
      const projectEnvVars = ["PATH", "PYTHONPATH", "NODE_PATH", "VIRTUAL_ENV"];
      for (const varName of projectEnvVars) {
        if (process.env[varName]) {
          environment.environmentVariables[varName] = process.env[varName];
        }
      }
      return environment;
    } catch (error) {
      console.error("获取项目环境信息失败:", error);
      throw new Error(`获取项目环境信息失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 获取Git信息
   */
  async getGitInfo(projectPath) {
    try {
      const gitDir = path__namespace.join(projectPath, ".git");
      if (!fs__namespace.existsSync(gitDir)) {
        return { isRepo: false };
      }
      const { exec } = require("child_process");
      const { promisify: promisify2 } = require("util");
      const execAsync2 = promisify2(exec);
      const gitInfo = { isRepo: true };
      try {
        const { stdout: branch } = await execAsync2("git branch --show-current", { cwd: projectPath });
        gitInfo.currentBranch = branch.trim();
      } catch {
      }
      try {
        const { stdout: remoteUrl } = await execAsync2("git remote get-url origin", { cwd: projectPath });
        gitInfo.remoteUrl = remoteUrl.trim();
      } catch {
      }
      try {
        const { stdout: lastCommit } = await execAsync2(
          'git log -1 --pretty=format:"%H|%s|%an|%ad" --date=iso',
          { cwd: projectPath }
        );
        const [hash, message, author, dateStr] = lastCommit.split("|");
        gitInfo.lastCommit = {
          hash: hash.trim(),
          message: message.trim(),
          author: author.trim(),
          date: new Date(dateStr.trim())
        };
      } catch {
      }
      try {
        const { stdout: status } = await execAsync2("git status --porcelain", { cwd: projectPath });
        const lines = status.trim().split("\n").filter((line) => line.trim());
        gitInfo.status = {
          modified: lines.filter((line) => line.startsWith(" M")).length,
          added: lines.filter((line) => line.startsWith("A ")).length,
          deleted: lines.filter((line) => line.startsWith(" D")).length,
          untracked: lines.filter((line) => line.startsWith("??")).length
        };
      } catch {
      }
      return gitInfo;
    } catch (error) {
      console.error("获取Git信息失败:", error);
      return { isRepo: false };
    }
  }
  /**
   * 递归扫描目录
   */
  async scanDirectory(dirPath, basePath, fileTypes, largestFiles, onFile, maxDepth = 5, currentDepth = 0) {
    if (currentDepth > maxDepth) return;
    try {
      const items = await readdir(dirPath);
      for (const item of items) {
        if (item.startsWith(".") && !item.endsWith(".py") && !item.endsWith(".js")) {
          continue;
        }
        if (["node_modules", "__pycache__", ".git", "dist", "build"].includes(item)) {
          continue;
        }
        const itemPath = path__namespace.join(dirPath, item);
        try {
          const stats = await stat(itemPath);
          if (stats.isDirectory()) {
            await this.scanDirectory(
              itemPath,
              basePath,
              fileTypes,
              largestFiles,
              onFile,
              maxDepth,
              currentDepth + 1
            );
          } else if (stats.isFile()) {
            const ext = path__namespace.extname(item).toLowerCase();
            const fileType = ext || "no-extension";
            fileTypes[fileType] = (fileTypes[fileType] || 0) + 1;
            onFile(stats.size);
            if (stats.size > 1024) {
              largestFiles.push({
                name: item,
                size: stats.size,
                path: path__namespace.relative(basePath, itemPath)
              });
            }
          }
        } catch (error) {
          continue;
        }
      }
    } catch (error) {
    }
  }
  /**
   * 检测项目类型
   */
  async detectProjectType(projectPath) {
    try {
      if (fs__namespace.existsSync(path__namespace.join(projectPath, "requirements.txt")) || fs__namespace.existsSync(path__namespace.join(projectPath, "setup.py")) || fs__namespace.existsSync(path__namespace.join(projectPath, "pyproject.toml"))) {
        return "Python";
      }
      if (fs__namespace.existsSync(path__namespace.join(projectPath, "package.json"))) {
        return "Node.js";
      }
      if (fs__namespace.existsSync(path__namespace.join(projectPath, "Dockerfile")) || fs__namespace.existsSync(path__namespace.join(projectPath, "docker-compose.yml"))) {
        return "Docker";
      }
      if (fs__namespace.existsSync(path__namespace.join(projectPath, "pom.xml")) || fs__namespace.existsSync(path__namespace.join(projectPath, "build.gradle"))) {
        return "Java";
      }
      if (fs__namespace.existsSync(path__namespace.join(projectPath, "go.mod"))) {
        return "Go";
      }
      if (fs__namespace.existsSync(path__namespace.join(projectPath, "Cargo.toml"))) {
        return "Rust";
      }
      return "Other";
    } catch (error) {
      return "Unknown";
    }
  }
  /**
   * 格式化文件大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }
}
class WalletConfigService {
  constructor() {
    this.CONFIG_DIR = ".workspace";
    this.WALLET_CONFIG_FILE = "wallet-config.json";
    this.ENCRYPTION_KEY = "project-wallet-config-key-2024";
  }
  /**
   * 获取项目钱包配置
   */
  async getProjectWalletConfig(projectPath) {
    try {
      const configPath = this.getWalletConfigPath(projectPath);
      if (!fs__namespace.existsSync(configPath)) {
        return this.createDefaultWalletConfig(projectPath);
      }
      const encryptedContent = fs__namespace.readFileSync(configPath, "utf-8");
      const decryptedContent = this.decrypt(encryptedContent);
      const config = JSON.parse(decryptedContent);
      return this.validateAndFixWalletConfig(config);
    } catch (error) {
      console.error("获取钱包配置失败:", error);
      throw new Error(`获取钱包配置失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 保存项目钱包配置
   */
  async saveProjectWalletConfig(projectPath, config) {
    try {
      const configDir = this.getConfigDir(projectPath);
      const configPath = this.getWalletConfigPath(projectPath);
      if (!fs__namespace.existsSync(configDir)) {
        fs__namespace.mkdirSync(configDir, { recursive: true });
      }
      config.wallets.forEach((wallet) => {
        wallet.updatedAt = /* @__PURE__ */ new Date();
      });
      const configJson = JSON.stringify(config, null, 2);
      const encryptedContent = this.encrypt(configJson);
      fs__namespace.writeFileSync(configPath, encryptedContent, "utf-8");
      console.log(`钱包配置已保存: ${configPath}`);
    } catch (error) {
      console.error("保存钱包配置失败:", error);
      throw new Error(`保存钱包配置失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 添加钱包
   */
  async addWallet(projectPath, wallet) {
    try {
      const config = await this.getProjectWalletConfig(projectPath);
      const newWallet = {
        ...wallet,
        id: this.generateWalletId(),
        createdAt: /* @__PURE__ */ new Date(),
        updatedAt: /* @__PURE__ */ new Date()
      };
      if (config.wallets.length === 0) {
        newWallet.isDefault = true;
        config.defaultWallet = newWallet.id;
      }
      if (newWallet.isDefault) {
        config.wallets.forEach((w) => w.isDefault = false);
        config.defaultWallet = newWallet.id;
      }
      config.wallets.push(newWallet);
      await this.saveProjectWalletConfig(projectPath, config);
      return newWallet;
    } catch (error) {
      console.error("添加钱包失败:", error);
      throw new Error(`添加钱包失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 更新钱包
   */
  async updateWallet(projectPath, walletId, updates) {
    try {
      const config = await this.getProjectWalletConfig(projectPath);
      const walletIndex = config.wallets.findIndex((w) => w.id === walletId);
      if (walletIndex === -1) {
        throw new Error(`钱包 ${walletId} 不存在`);
      }
      const updatedWallet = {
        ...config.wallets[walletIndex],
        ...updates,
        id: walletId,
        // 确保ID不被修改
        updatedAt: /* @__PURE__ */ new Date()
      };
      config.wallets[walletIndex] = updatedWallet;
      if (updates.isDefault) {
        config.wallets.forEach((w) => {
          if (w.id !== walletId) {
            w.isDefault = false;
          }
        });
        config.defaultWallet = walletId;
      }
      await this.saveProjectWalletConfig(projectPath, config);
      return updatedWallet;
    } catch (error) {
      console.error("更新钱包失败:", error);
      throw new Error(`更新钱包失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 删除钱包
   */
  async deleteWallet(projectPath, walletId) {
    try {
      const config = await this.getProjectWalletConfig(projectPath);
      const walletIndex = config.wallets.findIndex((w) => w.id === walletId);
      if (walletIndex === -1) {
        throw new Error(`钱包 ${walletId} 不存在`);
      }
      const wallet = config.wallets[walletIndex];
      config.wallets.splice(walletIndex, 1);
      if (wallet.isDefault && config.wallets.length > 0) {
        config.wallets[0].isDefault = true;
        config.defaultWallet = config.wallets[0].id;
      } else if (config.wallets.length === 0) {
        config.defaultWallet = void 0;
      }
      await this.saveProjectWalletConfig(projectPath, config);
      console.log(`钱包 ${walletId} 已删除`);
    } catch (error) {
      console.error("删除钱包失败:", error);
      throw new Error(`删除钱包失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 获取默认钱包
   */
  async getDefaultWallet(projectPath) {
    try {
      const config = await this.getProjectWalletConfig(projectPath);
      if (config.defaultWallet) {
        return config.wallets.find((w) => w.id === config.defaultWallet) || null;
      }
      return config.wallets.length > 0 ? config.wallets[0] : null;
    } catch (error) {
      console.error("获取默认钱包失败:", error);
      return null;
    }
  }
  /**
   * 验证钱包连接
   */
  async validateWalletConnection(wallet) {
    try {
      if (wallet.type === "private_key" && wallet.privateKey) {
        return wallet.privateKey.length === 64 || wallet.privateKey.length === 66;
      }
      if (wallet.type === "mnemonic" && wallet.mnemonic) {
        const words = wallet.mnemonic.trim().split(" ");
        return words.length === 12 || words.length === 24;
      }
      if (wallet.address) {
        return /^0x[a-fA-F0-9]{40}$/.test(wallet.address);
      }
      return true;
    } catch (error) {
      console.error("验证钱包连接失败:", error);
      return false;
    }
  }
  /**
   * 创建默认钱包配置
   */
  async createDefaultWalletConfig(projectPath) {
    const config = {
      projectId: path__namespace.basename(projectPath),
      wallets: [],
      settings: {
        autoConnect: false,
        networkTimeout: 3e4,
        gasLimit: 21e3,
        gasPrice: "20"
      }
    };
    await this.saveProjectWalletConfig(projectPath, config);
    return config;
  }
  /**
   * 验证和修复钱包配置
   */
  validateAndFixWalletConfig(config) {
    var _a, _b, _c, _d;
    const validatedConfig = {
      projectId: config.projectId || "unknown",
      wallets: [],
      settings: {
        autoConnect: ((_a = config.settings) == null ? void 0 : _a.autoConnect) || false,
        networkTimeout: ((_b = config.settings) == null ? void 0 : _b.networkTimeout) || 3e4,
        gasLimit: ((_c = config.settings) == null ? void 0 : _c.gasLimit) || 21e3,
        gasPrice: ((_d = config.settings) == null ? void 0 : _d.gasPrice) || "20"
      }
    };
    if (Array.isArray(config.wallets)) {
      validatedConfig.wallets = config.wallets.map((wallet) => ({
        id: wallet.id || this.generateWalletId(),
        name: wallet.name || "未命名钱包",
        type: wallet.type || "metamask",
        address: wallet.address || "",
        privateKey: wallet.privateKey || "",
        mnemonic: wallet.mnemonic || "",
        rpcUrl: wallet.rpcUrl || "",
        chainId: wallet.chainId || 1,
        isDefault: Boolean(wallet.isDefault),
        createdAt: wallet.createdAt ? new Date(wallet.createdAt) : /* @__PURE__ */ new Date(),
        updatedAt: wallet.updatedAt ? new Date(wallet.updatedAt) : /* @__PURE__ */ new Date()
      }));
    }
    if (validatedConfig.wallets.length > 0) {
      const hasDefault = validatedConfig.wallets.some((w) => w.isDefault);
      if (!hasDefault) {
        validatedConfig.wallets[0].isDefault = true;
      }
      const defaultWallet = validatedConfig.wallets.find((w) => w.isDefault);
      validatedConfig.defaultWallet = defaultWallet == null ? void 0 : defaultWallet.id;
    }
    return validatedConfig;
  }
  /**
   * 生成钱包ID
   */
  generateWalletId() {
    return `wallet_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  /**
   * 获取配置目录路径
   */
  getConfigDir(projectPath) {
    return path__namespace.join(projectPath, this.CONFIG_DIR);
  }
  /**
   * 获取钱包配置文件路径
   */
  getWalletConfigPath(projectPath) {
    return path__namespace.join(this.getConfigDir(projectPath), this.WALLET_CONFIG_FILE);
  }
  /**
   * 加密数据
   */
  encrypt(text) {
    const algorithm = "aes-256-cbc";
    const key = crypto__namespace.scryptSync(this.ENCRYPTION_KEY, "salt", 32);
    const iv = crypto__namespace.randomBytes(16);
    const cipher = crypto__namespace.createCipheriv(algorithm, key, iv);
    let encrypted = cipher.update(text, "utf8", "hex");
    encrypted += cipher.final("hex");
    return iv.toString("hex") + ":" + encrypted;
  }
  /**
   * 解密数据
   */
  decrypt(encryptedText) {
    const algorithm = "aes-256-cbc";
    const key = crypto__namespace.scryptSync(this.ENCRYPTION_KEY, "salt", 32);
    const textParts = encryptedText.split(":");
    const iv = Buffer.from(textParts.shift(), "hex");
    const encrypted = textParts.join(":");
    const decipher = crypto__namespace.createDecipheriv(algorithm, key, iv);
    let decrypted = decipher.update(encrypted, "hex", "utf8");
    decrypted += decipher.final("utf8");
    return decrypted;
  }
}
class GlobalWalletService {
  constructor() {
    this.configDir = path__namespace.join(electron.app.getPath("userData"), "wallet-config");
    this.configPath = path__namespace.join(this.configDir, "global-wallets.json");
    this.encryptionKey = this.getOrCreateEncryptionKey();
    if (!fs__namespace.existsSync(this.configDir)) {
      fs__namespace.mkdirSync(this.configDir, { recursive: true });
    }
  }
  /**
   * 获取全局钱包配置
   */
  async getGlobalWalletConfig() {
    try {
      if (!fs__namespace.existsSync(this.configPath)) {
        return this.createDefaultConfig();
      }
      const encryptedContent = fs__namespace.readFileSync(this.configPath, "utf-8");
      const decryptedContent = this.decrypt(encryptedContent);
      const config = JSON.parse(decryptedContent);
      return this.validateAndFixConfig(config);
    } catch (error) {
      console.error("获取全局钱包配置失败:", error);
      return this.createDefaultConfig();
    }
  }
  /**
   * 保存全局钱包配置
   */
  async saveGlobalWalletConfig(config) {
    try {
      config.wallets.forEach((wallet) => {
        wallet.updatedAt = /* @__PURE__ */ new Date();
      });
      const configJson = JSON.stringify(config, null, 2);
      const encryptedContent = this.encrypt(configJson);
      fs__namespace.writeFileSync(this.configPath, encryptedContent, "utf-8");
      console.log("全局钱包配置已保存");
    } catch (error) {
      console.error("保存全局钱包配置失败:", error);
      throw new Error(`保存全局钱包配置失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 添加钱包
   */
  async addWallet(wallet) {
    try {
      const config = await this.getGlobalWalletConfig();
      const newWallet = {
        ...wallet,
        id: this.generateWalletId(),
        createdAt: /* @__PURE__ */ new Date(),
        updatedAt: /* @__PURE__ */ new Date()
      };
      config.wallets.push(newWallet);
      await this.saveGlobalWalletConfig(config);
      return newWallet;
    } catch (error) {
      console.error("添加钱包失败:", error);
      throw new Error(`添加钱包失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 批量添加钱包
   */
  async addWallets(wallets) {
    try {
      const config = await this.getGlobalWalletConfig();
      const newWallets = [];
      for (const wallet of wallets) {
        const newWallet = {
          ...wallet,
          id: this.generateWalletId(),
          createdAt: /* @__PURE__ */ new Date(),
          updatedAt: /* @__PURE__ */ new Date()
        };
        newWallets.push(newWallet);
        config.wallets.push(newWallet);
      }
      await this.saveGlobalWalletConfig(config);
      return newWallets;
    } catch (error) {
      console.error("批量添加钱包失败:", error);
      throw new Error(`批量添加钱包失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 更新钱包
   */
  async updateWallet(walletId, updates) {
    try {
      const config = await this.getGlobalWalletConfig();
      const walletIndex = config.wallets.findIndex((w) => w.id === walletId);
      if (walletIndex === -1) {
        throw new Error(`钱包 ${walletId} 不存在`);
      }
      const updatedWallet = {
        ...config.wallets[walletIndex],
        ...updates,
        id: walletId,
        updatedAt: /* @__PURE__ */ new Date()
      };
      config.wallets[walletIndex] = updatedWallet;
      await this.saveGlobalWalletConfig(config);
      return updatedWallet;
    } catch (error) {
      console.error("更新钱包失败:", error);
      throw new Error(`更新钱包失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 删除钱包
   */
  async deleteWallet(walletId) {
    try {
      const config = await this.getGlobalWalletConfig();
      const walletIndex = config.wallets.findIndex((w) => w.id === walletId);
      if (walletIndex === -1) {
        throw new Error(`钱包 ${walletId} 不存在`);
      }
      config.wallets.splice(walletIndex, 1);
      await this.saveGlobalWalletConfig(config);
      console.log(`钱包 ${walletId} 已删除`);
    } catch (error) {
      console.error("删除钱包失败:", error);
      throw new Error(`删除钱包失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 批量删除钱包
   */
  async deleteWallets(walletIds) {
    try {
      const config = await this.getGlobalWalletConfig();
      config.wallets = config.wallets.filter((w) => !walletIds.includes(w.id));
      await this.saveGlobalWalletConfig(config);
      console.log(`已删除 ${walletIds.length} 个钱包`);
    } catch (error) {
      console.error("批量删除钱包失败:", error);
      throw new Error(`批量删除钱包失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 从文件导入钱包
   */
  async importWalletsFromFile(filePath, chain) {
    try {
      if (!fs__namespace.existsSync(filePath)) {
        throw new Error("文件不存在");
      }
      const content = fs__namespace.readFileSync(filePath, "utf-8");
      const lines = content.split("\n").filter((line) => line.trim());
      const wallets = [];
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;
        try {
          const walletData = this.parseWalletData(line, chain);
          if (walletData) {
            wallets.push({
              name: `导入钱包 ${i + 1}`,
              chain,
              type: walletData.type,
              address: walletData.address,
              privateKey: walletData.privateKey,
              mnemonic: walletData.mnemonic,
              publicKey: walletData.publicKey,
              derivationPath: walletData.derivationPath,
              isEnabled: true,
              tags: ["导入"]
            });
          }
        } catch (error) {
          console.warn(`解析第 ${i + 1} 行失败:`, error);
        }
      }
      if (wallets.length === 0) {
        throw new Error("未找到有效的钱包数据");
      }
      return await this.addWallets(wallets);
    } catch (error) {
      console.error("从文件导入钱包失败:", error);
      throw new Error(`从文件导入钱包失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 批量创建新钱包
   */
  async createWallets(count, chain) {
    try {
      const wallets = [];
      for (let i = 0; i < count; i++) {
        const walletData = await this.generateWallet(chain);
        wallets.push({
          name: `新钱包 ${i + 1}`,
          chain,
          type: "generated",
          address: walletData.address,
          privateKey: walletData.privateKey,
          mnemonic: walletData.mnemonic,
          publicKey: walletData.publicKey,
          derivationPath: walletData.derivationPath,
          isEnabled: true,
          tags: ["生成"]
        });
      }
      return await this.addWallets(wallets);
    } catch (error) {
      console.error("批量创建钱包失败:", error);
      throw new Error(`批量创建钱包失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 根据助记词获取地址
   */
  async getAddressFromMnemonic(mnemonic, chain, derivationPath) {
    try {
      if (!bip39__namespace.validateMnemonic(mnemonic)) {
        throw new Error("无效的助记词");
      }
      const seed = bip39__namespace.mnemonicToSeedSync(mnemonic);
      switch (chain) {
        case "eth":
        case "bnb":
        case "polygon":
        case "avalanche":
        case "fantom":
        case "arbitrum":
        case "optimism":
          const ethPath = derivationPath || "m/44'/60'/0'/0/0";
          const ethWallet = ethers.ethers.HDNodeWallet.fromSeed(seed).derivePath(ethPath);
          return ethWallet.address;
        case "sol":
          const solPath = derivationPath || "m/44'/501'/0'/0'";
          const root = hdkey__namespace.fromMasterSeed(seed);
          const solNode = root.derive(solPath);
          const solKeypair = web3_js.Keypair.fromSeed(solNode.privateKey);
          return solKeypair.publicKey.toBase58();
        case "sui":
          const suiPath = derivationPath || "m/44'/784'/0'/0'/0'";
          const suiRoot = hdkey__namespace.fromMasterSeed(seed);
          const suiNode = suiRoot.derive(suiPath);
          const suiAddress = "0x" + crypto__namespace.createHash("sha256").update(suiNode.publicKey).digest("hex").substring(0, 64);
          return suiAddress;
        default:
          const defaultPath = derivationPath || "m/44'/60'/0'/0/0";
          const defaultWallet = ethers.ethers.HDNodeWallet.fromSeed(seed).derivePath(defaultPath);
          return defaultWallet.address;
      }
    } catch (error) {
      console.error("从助记词获取地址失败:", error);
      throw new Error(`从助记词获取地址失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 解析钱包数据
   */
  parseWalletData(data, chain) {
    if (data.match(/^[0-9a-fA-F]{64}$/) || data.match(/^0x[0-9a-fA-F]{64}$/)) {
      const privateKey = data.startsWith("0x") ? data : "0x" + data;
      const address = this.getAddressFromPrivateKey(privateKey, chain);
      return {
        type: "private_key",
        privateKey,
        address
      };
    }
    const words = data.trim().split(/\s+/);
    if (words.length === 12 || words.length === 24) {
      const mnemonic = words.join(" ");
      const address = this.getAddressFromMnemonic(mnemonic, chain);
      return {
        type: "mnemonic",
        mnemonic,
        address
      };
    }
    return null;
  }
  /**
   * 从私钥获取地址
   */
  getAddressFromPrivateKey(privateKey, chain) {
    try {
      switch (chain) {
        case "eth":
        case "bnb":
        case "polygon":
        case "avalanche":
        case "fantom":
        case "arbitrum":
        case "optimism":
          const ethWallet = new ethers.ethers.Wallet(privateKey);
          return ethWallet.address;
        case "sol":
          const privateKeyBytes = Buffer.from(privateKey.replace("0x", ""), "hex");
          if (privateKeyBytes.length !== 32) {
            throw new Error("Solana私钥必须是32字节");
          }
          const solKeypair = web3_js.Keypair.fromSecretKey(privateKeyBytes);
          return solKeypair.publicKey.toBase58();
        case "sui":
          const suiPrivateKeyBytes = Buffer.from(privateKey.replace("0x", ""), "hex");
          if (suiPrivateKeyBytes.length !== 32) {
            throw new Error("Sui私钥必须是32字节");
          }
          const suiAddress = "0x" + crypto__namespace.createHash("sha256").update(suiPrivateKeyBytes).digest("hex").substring(0, 64);
          return suiAddress;
        default:
          const defaultWallet = new ethers.ethers.Wallet(privateKey);
          return defaultWallet.address;
      }
    } catch (error) {
      console.error("从私钥获取地址失败:", error);
      throw new Error(`从私钥获取地址失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 生成新钱包
   */
  async generateWallet(chain) {
    try {
      const mnemonic = bip39__namespace.generateMnemonic(128);
      const seed = bip39__namespace.mnemonicToSeedSync(mnemonic);
      let privateKey;
      let address;
      let publicKey;
      let derivationPath;
      switch (chain) {
        case "eth":
        case "bnb":
        case "polygon":
        case "avalanche":
        case "fantom":
        case "arbitrum":
        case "optimism":
          derivationPath = "m/44'/60'/0'/0/0";
          const ethWallet = ethers.ethers.HDNodeWallet.fromSeed(seed).derivePath(derivationPath);
          privateKey = ethWallet.privateKey;
          address = ethWallet.address;
          publicKey = ethWallet.publicKey;
          break;
        case "sol":
          derivationPath = "m/44'/501'/0'/0'";
          const root = hdkey__namespace.fromMasterSeed(seed);
          const solNode = root.derive(derivationPath);
          const solKeypair = web3_js.Keypair.fromSeed(solNode.privateKey);
          privateKey = "0x" + Buffer.from(solKeypair.secretKey).toString("hex");
          address = solKeypair.publicKey.toBase58();
          publicKey = solKeypair.publicKey.toBase58();
          break;
        case "sui":
          derivationPath = "m/44'/784'/0'/0'/0'";
          const suiRoot = hdkey__namespace.fromMasterSeed(seed);
          const suiNode = suiRoot.derive(derivationPath);
          privateKey = "0x" + suiNode.privateKey.toString("hex");
          address = "0x" + crypto__namespace.createHash("sha256").update(suiNode.publicKey).digest("hex").substring(0, 64);
          publicKey = "0x" + suiNode.publicKey.toString("hex");
          break;
        default:
          derivationPath = "m/44'/60'/0'/0/0";
          const defaultWallet = ethers.ethers.HDNodeWallet.fromSeed(seed).derivePath(derivationPath);
          privateKey = defaultWallet.privateKey;
          address = defaultWallet.address;
          publicKey = defaultWallet.publicKey;
          break;
      }
      return {
        privateKey,
        mnemonic,
        address,
        publicKey,
        derivationPath
      };
    } catch (error) {
      console.error("生成钱包失败:", error);
      throw new Error(`生成钱包失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 创建默认配置
   */
  createDefaultConfig() {
    return {
      wallets: [],
      settings: {
        defaultChain: "eth",
        autoBackup: true,
        encryptionEnabled: true
      }
    };
  }
  /**
   * 验证并修复配置
   */
  validateAndFixConfig(config) {
    var _a, _b, _c;
    const validatedConfig = {
      wallets: [],
      settings: {
        defaultChain: ((_a = config.settings) == null ? void 0 : _a.defaultChain) || "eth",
        autoBackup: ((_b = config.settings) == null ? void 0 : _b.autoBackup) !== false,
        encryptionEnabled: ((_c = config.settings) == null ? void 0 : _c.encryptionEnabled) !== false
      }
    };
    if (Array.isArray(config.wallets)) {
      validatedConfig.wallets = config.wallets.map((wallet) => ({
        id: wallet.id || this.generateWalletId(),
        name: wallet.name || "未命名钱包",
        chain: wallet.chain || "eth",
        type: wallet.type || "generated",
        address: wallet.address || "",
        privateKey: wallet.privateKey || "",
        mnemonic: wallet.mnemonic || "",
        publicKey: wallet.publicKey || "",
        derivationPath: wallet.derivationPath || "",
        isEnabled: wallet.isEnabled !== false,
        tags: Array.isArray(wallet.tags) ? wallet.tags : [],
        createdAt: wallet.createdAt ? new Date(wallet.createdAt) : /* @__PURE__ */ new Date(),
        updatedAt: wallet.updatedAt ? new Date(wallet.updatedAt) : /* @__PURE__ */ new Date()
      }));
    }
    return validatedConfig;
  }
  /**
   * 生成钱包ID
   */
  generateWalletId() {
    return "wallet_" + Date.now() + "_" + Math.random().toString(36).substr(2, 9);
  }
  /**
   * 获取或创建加密密钥
   */
  getOrCreateEncryptionKey() {
    const keyPath = path__namespace.join(this.configDir, ".key");
    if (fs__namespace.existsSync(keyPath)) {
      return fs__namespace.readFileSync(keyPath, "utf-8");
    }
    if (!fs__namespace.existsSync(this.configDir)) {
      fs__namespace.mkdirSync(this.configDir, { recursive: true });
    }
    const key = crypto__namespace.randomBytes(32).toString("hex");
    fs__namespace.writeFileSync(keyPath, key, "utf-8");
    return key;
  }
  /**
   * 加密数据
   */
  encrypt(text) {
    const iv = crypto__namespace.randomBytes(16);
    const key = crypto__namespace.scryptSync(this.encryptionKey, "salt", 32);
    const cipher = crypto__namespace.createCipheriv("aes-256-cbc", key, iv);
    let encrypted = cipher.update(text, "utf8", "hex");
    encrypted += cipher.final("hex");
    return iv.toString("hex") + ":" + encrypted;
  }
  /**
   * 解密数据
   */
  decrypt(text) {
    const parts = text.split(":");
    const iv = Buffer.from(parts.shift(), "hex");
    const encryptedText = parts.join(":");
    const key = crypto__namespace.scryptSync(this.encryptionKey, "salt", 32);
    const decipher = crypto__namespace.createDecipheriv("aes-256-cbc", key, iv);
    let decrypted = decipher.update(encryptedText, "hex", "utf8");
    decrypted += decipher.final("utf8");
    return decrypted;
  }
}
class ProxyConfigService {
  constructor() {
    this.CONFIG_DIR = ".workspace";
    this.PROXY_CONFIG_FILE = "proxy-config.json";
    this.ENCRYPTION_KEY = "project-proxy-config-key-2024";
  }
  /**
   * 获取项目代理配置
   */
  async getProjectProxyConfig(projectPath) {
    try {
      const configPath = this.getProxyConfigPath(projectPath);
      if (!fs__namespace.existsSync(configPath)) {
        return await this.createDefaultProxyConfig(projectPath);
      }
      const encryptedContent = fs__namespace.readFileSync(configPath, "utf-8");
      const decryptedContent = this.decrypt(encryptedContent);
      const config = JSON.parse(decryptedContent);
      return this.validateAndFixProxyConfig(config);
    } catch (error) {
      console.error("获取代理配置失败:", error);
      throw new Error(`获取代理配置失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 保存项目代理配置
   */
  async saveProjectProxyConfig(projectPath, config) {
    try {
      const configDir = this.getConfigDir(projectPath);
      const configPath = this.getProxyConfigPath(projectPath);
      if (!fs__namespace.existsSync(configDir)) {
        fs__namespace.mkdirSync(configDir, { recursive: true });
      }
      config.proxies.forEach((proxy) => {
        proxy.updatedAt = /* @__PURE__ */ new Date();
      });
      const configJson = JSON.stringify(config, null, 2);
      const encryptedContent = this.encrypt(configJson);
      fs__namespace.writeFileSync(configPath, encryptedContent, "utf-8");
      console.log(`代理配置已保存: ${configPath}`);
    } catch (error) {
      console.error("保存代理配置失败:", error);
      throw new Error(`保存代理配置失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 添加代理
   */
  async addProxy(projectPath, proxy) {
    try {
      const config = await this.getProjectProxyConfig(projectPath);
      const newProxy = {
        ...proxy,
        id: this.generateProxyId(),
        createdAt: /* @__PURE__ */ new Date(),
        updatedAt: /* @__PURE__ */ new Date()
      };
      if (config.proxies.length === 0) {
        newProxy.isDefault = true;
        config.defaultProxy = newProxy.id;
      }
      if (newProxy.isDefault) {
        config.proxies.forEach((p) => p.isDefault = false);
        config.defaultProxy = newProxy.id;
      }
      config.proxies.push(newProxy);
      await this.saveProjectProxyConfig(projectPath, config);
      return newProxy;
    } catch (error) {
      console.error("添加代理失败:", error);
      throw new Error(`添加代理失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 更新代理
   */
  async updateProxy(projectPath, proxyId, updates) {
    try {
      const config = await this.getProjectProxyConfig(projectPath);
      const proxyIndex = config.proxies.findIndex((p) => p.id === proxyId);
      if (proxyIndex === -1) {
        throw new Error(`代理 ${proxyId} 不存在`);
      }
      const updatedProxy = {
        ...config.proxies[proxyIndex],
        ...updates,
        id: proxyId,
        // 确保ID不被修改
        updatedAt: /* @__PURE__ */ new Date()
      };
      config.proxies[proxyIndex] = updatedProxy;
      if (updates.isDefault) {
        config.proxies.forEach((p) => {
          if (p.id !== proxyId) {
            p.isDefault = false;
          }
        });
        config.defaultProxy = proxyId;
      }
      await this.saveProjectProxyConfig(projectPath, config);
      return updatedProxy;
    } catch (error) {
      console.error("更新代理失败:", error);
      throw new Error(`更新代理失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 删除代理
   */
  async deleteProxy(projectPath, proxyId) {
    try {
      const config = await this.getProjectProxyConfig(projectPath);
      const proxyIndex = config.proxies.findIndex((p) => p.id === proxyId);
      if (proxyIndex === -1) {
        throw new Error(`代理 ${proxyId} 不存在`);
      }
      const proxy = config.proxies[proxyIndex];
      config.proxies.splice(proxyIndex, 1);
      if (proxy.isDefault && config.proxies.length > 0) {
        config.proxies[0].isDefault = true;
        config.defaultProxy = config.proxies[0].id;
      } else if (config.proxies.length === 0) {
        config.defaultProxy = void 0;
      }
      await this.saveProjectProxyConfig(projectPath, config);
      console.log(`代理 ${proxyId} 已删除`);
    } catch (error) {
      console.error("删除代理失败:", error);
      throw new Error(`删除代理失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 获取默认代理
   */
  async getDefaultProxy(projectPath) {
    try {
      const config = await this.getProjectProxyConfig(projectPath);
      if (config.defaultProxy) {
        return config.proxies.find((p) => p.id === config.defaultProxy) || null;
      }
      return config.proxies.find((p) => p.isEnabled) || null;
    } catch (error) {
      console.error("获取默认代理失败:", error);
      return null;
    }
  }
  /**
   * 测试代理连接
   */
  async testProxyConnection(proxy) {
    try {
      const net = require("net");
      return new Promise((resolve) => {
        const socket = new net.Socket();
        const timeout = 5e3;
        socket.setTimeout(timeout);
        socket.on("connect", () => {
          socket.destroy();
          resolve(true);
        });
        socket.on("timeout", () => {
          socket.destroy();
          resolve(false);
        });
        socket.on("error", () => {
          socket.destroy();
          resolve(false);
        });
        socket.connect(proxy.port, proxy.host);
      });
    } catch (error) {
      console.error("测试代理连接失败:", error);
      return false;
    }
  }
  /**
   * 获取系统代理设置
   */
  async getSystemProxySettings() {
    try {
      const systemProxy = {
        enabled: false,
        host: "",
        port: 0,
        type: "http",
        autoDetected: false
      };
      const httpProxy = process.env.HTTP_PROXY || process.env.http_proxy;
      const httpsProxy = process.env.HTTPS_PROXY || process.env.https_proxy;
      if (httpProxy) {
        const url = new URL(httpProxy);
        systemProxy.enabled = true;
        systemProxy.host = url.hostname;
        systemProxy.port = parseInt(url.port) || 80;
        systemProxy.autoDetected = true;
      } else if (httpsProxy) {
        const url = new URL(httpsProxy);
        systemProxy.enabled = true;
        systemProxy.host = url.hostname;
        systemProxy.port = parseInt(url.port) || 443;
        systemProxy.type = "https";
        systemProxy.autoDetected = true;
      }
      return systemProxy;
    } catch (error) {
      console.error("获取系统代理设置失败:", error);
      return {
        enabled: false,
        host: "",
        port: 0,
        type: "http",
        autoDetected: false
      };
    }
  }
  /**
   * 创建默认代理配置
   */
  async createDefaultProxyConfig(projectPath) {
    const config = {
      projectId: path__namespace.basename(projectPath),
      proxies: [],
      settings: {
        enableProxy: false,
        autoDetect: true,
        timeout: 3e4,
        retryCount: 3,
        bypassList: ["localhost", "127.0.0.1", "::1"]
      }
    };
    await this.saveProjectProxyConfig(projectPath, config);
    return config;
  }
  /**
   * 验证和修复代理配置
   */
  validateAndFixProxyConfig(config) {
    var _a, _b, _c, _d, _e;
    const validatedConfig = {
      projectId: config.projectId || "unknown",
      proxies: [],
      settings: {
        enableProxy: ((_a = config.settings) == null ? void 0 : _a.enableProxy) || false,
        autoDetect: ((_b = config.settings) == null ? void 0 : _b.autoDetect) || true,
        timeout: ((_c = config.settings) == null ? void 0 : _c.timeout) || 3e4,
        retryCount: ((_d = config.settings) == null ? void 0 : _d.retryCount) || 3,
        bypassList: ((_e = config.settings) == null ? void 0 : _e.bypassList) || ["localhost", "127.0.0.1", "::1"]
      }
    };
    if (Array.isArray(config.proxies)) {
      validatedConfig.proxies = config.proxies.map((proxy) => ({
        id: proxy.id || this.generateProxyId(),
        name: proxy.name || "未命名代理",
        type: proxy.type || "http",
        host: proxy.host || "",
        port: proxy.port || 8080,
        username: proxy.username || "",
        password: proxy.password || "",
        isDefault: Boolean(proxy.isDefault),
        isEnabled: Boolean(proxy.isEnabled),
        createdAt: proxy.createdAt ? new Date(proxy.createdAt) : /* @__PURE__ */ new Date(),
        updatedAt: proxy.updatedAt ? new Date(proxy.updatedAt) : /* @__PURE__ */ new Date()
      }));
    }
    if (validatedConfig.proxies.length > 0) {
      const hasDefault = validatedConfig.proxies.some((p) => p.isDefault);
      if (!hasDefault) {
        validatedConfig.proxies[0].isDefault = true;
      }
      const defaultProxy = validatedConfig.proxies.find((p) => p.isDefault);
      validatedConfig.defaultProxy = defaultProxy == null ? void 0 : defaultProxy.id;
    }
    return validatedConfig;
  }
  /**
   * 生成代理ID
   */
  generateProxyId() {
    return `proxy_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  /**
   * 获取配置目录路径
   */
  getConfigDir(projectPath) {
    return path__namespace.join(projectPath, this.CONFIG_DIR);
  }
  /**
   * 获取代理配置文件路径
   */
  getProxyConfigPath(projectPath) {
    return path__namespace.join(this.getConfigDir(projectPath), this.PROXY_CONFIG_FILE);
  }
  /**
   * 加密数据
   */
  encrypt(text) {
    const algorithm = "aes-256-cbc";
    const key = crypto__namespace.scryptSync(this.ENCRYPTION_KEY, "salt", 32);
    const iv = crypto__namespace.randomBytes(16);
    const cipher = crypto__namespace.createCipheriv(algorithm, key, iv);
    let encrypted = cipher.update(text, "utf8", "hex");
    encrypted += cipher.final("hex");
    return iv.toString("hex") + ":" + encrypted;
  }
  /**
   * 解密数据
   */
  decrypt(encryptedText) {
    const algorithm = "aes-256-cbc";
    const key = crypto__namespace.scryptSync(this.ENCRYPTION_KEY, "salt", 32);
    const textParts = encryptedText.split(":");
    const iv = Buffer.from(textParts.shift(), "hex");
    const encrypted = textParts.join(":");
    const decipher = crypto__namespace.createDecipheriv(algorithm, key, iv);
    let decrypted = decipher.update(encrypted, "hex", "utf8");
    decrypted += decipher.final("utf8");
    return decrypted;
  }
}
class ProxyManagerService {
  constructor() {
    this.defaultSources = [
      {
        name: "TheSpeedX (4.4k⭐ 每日更新)",
        url: "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt",
        format: "txt",
        parser: "simple",
        enabled: true
      },
      {
        name: "ErcinDedeoglu (每日更新+测试)",
        url: "https://raw.githubusercontent.com/ErcinDedeoglu/proxies/main/proxies/http.txt",
        format: "txt",
        parser: "simple",
        enabled: true
      },
      {
        name: "Monosans (每小时更新)",
        url: "https://raw.githubusercontent.com/monosans/proxy-list/main/proxies/http.txt",
        format: "txt",
        parser: "simple",
        enabled: true
      },
      {
        name: "Clarketm (每日更新)",
        url: "https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt",
        format: "txt",
        parser: "simple",
        enabled: true
      },
      {
        name: "Anonym0usWork1221 (每2小时更新)",
        url: "https://raw.githubusercontent.com/Anonym0usWork1221/Free-Proxies/main/proxy_files/http_proxies.txt",
        format: "txt",
        parser: "simple",
        enabled: true
      },
      {
        name: "ProxyScrape API (高速)",
        url: "https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=2000&country=all&ssl=all&anonymity=elite,anonymous&format=textplain",
        format: "txt",
        parser: "simple",
        enabled: true
      }
    ];
    this.configDir = path__namespace.join(electron.app.getPath("userData"), "proxy-config");
    this.configPath = path__namespace.join(this.configDir, "proxy-manager.json");
    this.encryptionKey = this.getOrCreateEncryptionKey();
    if (!fs__namespace.existsSync(this.configDir)) {
      fs__namespace.mkdirSync(this.configDir, { recursive: true });
    }
  }
  /**
   * 获取代理配置
   */
  async getProxyConfig() {
    try {
      if (!fs__namespace.existsSync(this.configPath)) {
        return this.createDefaultConfig();
      }
      const encryptedContent = fs__namespace.readFileSync(this.configPath, "utf-8");
      const decryptedContent = this.decrypt(encryptedContent);
      const config = JSON.parse(decryptedContent);
      return this.validateAndFixConfig(config);
    } catch (error) {
      console.error("获取代理配置失败:", error);
      return this.createDefaultConfig();
    }
  }
  /**
   * 保存代理配置
   */
  async saveProxyConfig(config) {
    try {
      config.proxies.forEach((proxy) => {
        proxy.updatedAt = /* @__PURE__ */ new Date();
      });
      const configJson = JSON.stringify(config, null, 2);
      const encryptedContent = this.encrypt(configJson);
      fs__namespace.writeFileSync(this.configPath, encryptedContent, "utf-8");
      console.log("代理配置已保存");
    } catch (error) {
      console.error("保存代理配置失败:", error);
      throw new Error(`保存代理配置失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 从所有源拉取代理 - 增量添加，不覆盖现有代理
   */
  async fetchProxiesFromAllSources() {
    try {
      const config = await this.getProxyConfig();
      const enabledSources = config.sources.filter((source) => source.enabled);
      const maxProxiesPerSource = 30;
      console.log(`开始并发拉取 ${enabledSources.length} 个源的高质量代理 (每源最多${maxProxiesPerSource}个)...`);
      const existingProxies = config.proxies || [];
      console.log(`当前已有 ${existingProxies.length} 个代理`);
      const sourcePromises = enabledSources.map(async (source) => {
        try {
          console.log(`正在从 ${source.name} 拉取代理...`);
          const rawProxies = await this.fetchProxiesFromSource(source);
          const qualityProxies = this.selectBestProxies(rawProxies, maxProxiesPerSource);
          console.log(`从 ${source.name} 获取到 ${qualityProxies.length} 个优质代理`);
          return qualityProxies;
        } catch (error) {
          console.error(`从 ${source.name} 拉取代理失败:`, error);
          return [];
        }
      });
      const allSourceResults = await Promise.all(sourcePromises);
      const newProxies = allSourceResults.flat();
      const allProxies = [...existingProxies, ...newProxies];
      const uniqueProxies = this.deduplicateProxies(allProxies);
      console.log(`新拉取 ${newProxies.length} 个代理，合并后共 ${allProxies.length} 个，去重后 ${uniqueProxies.length} 个`);
      const sortedProxies = this.sortProxiesByQuality(uniqueProxies);
      console.log(`⚡ 跳过自动测试，直接保存代理列表以提高速度`);
      console.log(`💡 用户可以手动选择代理进行测试`);
      config.proxies = sortedProxies;
      await this.saveProxyConfig(config);
      const workingCount = sortedProxies.filter((p) => p.isWorking === true).length;
      const unknownCount = sortedProxies.filter((p) => p.isWorking === void 0).length;
      const newCount = newProxies.length;
      console.log(`拉取完成! 新增 ${newCount} 个代理，总计 ${sortedProxies.length} 个 (已验证可用: ${workingCount}, 待测试: ${unknownCount})`);
      return sortedProxies;
    } catch (error) {
      console.error("拉取代理失败:", error);
      throw new Error(`拉取代理失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 从单个源拉取代理
   */
  async fetchProxiesFromSource(source) {
    var _a, _b;
    try {
      const userAgents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
      ];
      const randomUserAgent = userAgents[Math.floor(Math.random() * userAgents.length)];
      const response = await axios.get(source.url, {
        timeout: 8e3,
        // 进一步减少超时时间到8秒
        headers: {
          "User-Agent": randomUserAgent,
          "Accept": "*/*",
          "Accept-Language": "en-US,en;q=0.9",
          "Accept-Encoding": "gzip, deflate",
          "Connection": "close",
          // 使用短连接，提高速度
          "Cache-Control": "no-cache"
        },
        validateStatus: (status) => status < 500
      });
      if (response.status !== 200) {
        console.warn(`从 ${source.name} 获取到非200状态码: ${response.status}`);
        return [];
      }
      return this.parseProxyData(response.data, source);
    } catch (error) {
      if (((_a = error.response) == null ? void 0 : _a.status) === 403) {
        console.warn(`从 ${source.name} 拉取代理被拒绝 (403 Forbidden)，可能被反爬虫保护`);
      } else if (((_b = error.response) == null ? void 0 : _b.status) === 429) {
        console.warn(`从 ${source.name} 拉取代理被限流 (429 Too Many Requests)`);
      } else {
        console.error(`从 ${source.name} 拉取代理失败:`, error.message);
      }
      return [];
    }
  }
  /**
   * 解析代理数据
   */
  parseProxyData(data, source) {
    const proxies = [];
    try {
      switch (source.parser) {
        case "geonode":
          if (data.data && Array.isArray(data.data)) {
            data.data.forEach((item) => {
              var _a;
              proxies.push({
                id: this.generateProxyId(),
                host: item.ip,
                port: parseInt(item.port),
                type: ((_a = item.protocols) == null ? void 0 : _a[0]) || "http",
                country: item.country,
                region: item.region,
                city: item.city,
                isp: item.org,
                anonymity: item.anonymityLevel,
                uptime: item.upTime,
                source: source.name,
                createdAt: /* @__PURE__ */ new Date(),
                updatedAt: /* @__PURE__ */ new Date()
              });
            });
          }
          break;
        case "simple":
          this.parseSimpleFormat(data, "http", source.name, proxies);
          break;
        case "freeproxyworld":
          if (data.data && Array.isArray(data.data)) {
            data.data.forEach((item) => {
              var _a, _b;
              proxies.push({
                id: this.generateProxyId(),
                host: item.ip,
                port: parseInt(item.port),
                type: ((_a = item.type) == null ? void 0 : _a.toLowerCase()) || "http",
                country: item.country,
                anonymity: (_b = item.anonymity) == null ? void 0 : _b.toLowerCase(),
                source: source.name,
                createdAt: /* @__PURE__ */ new Date(),
                updatedAt: /* @__PURE__ */ new Date()
              });
            });
          }
          break;
        case "proxy11":
          if (data.data && Array.isArray(data.data)) {
            data.data.forEach((item) => {
              var _a, _b;
              proxies.push({
                id: this.generateProxyId(),
                host: item.ip,
                port: parseInt(item.port),
                type: ((_a = item.type) == null ? void 0 : _a.toLowerCase()) || "http",
                country: item.country_code,
                region: item.region,
                city: item.city,
                anonymity: (_b = item.anonymity_level) == null ? void 0 : _b.toLowerCase(),
                speed: item.response_time,
                uptime: item.uptime_percentage,
                source: source.name,
                createdAt: /* @__PURE__ */ new Date(),
                updatedAt: /* @__PURE__ */ new Date()
              });
            });
          }
          break;
        case "proxifly":
          if (data.data && Array.isArray(data.data)) {
            data.data.forEach((item) => {
              var _a, _b;
              proxies.push({
                id: this.generateProxyId(),
                host: item.ip,
                port: parseInt(item.port),
                type: ((_a = item.protocol) == null ? void 0 : _a.toLowerCase()) || "http",
                country: item.country,
                region: item.region,
                city: item.city,
                anonymity: (_b = item.anonymity) == null ? void 0 : _b.toLowerCase(),
                speed: item.response_time,
                uptime: item.uptime,
                isWorking: item.working === true,
                // Proxifly已测试过
                source: source.name,
                createdAt: /* @__PURE__ */ new Date(),
                updatedAt: /* @__PURE__ */ new Date()
              });
            });
          }
          break;
        default:
          console.warn(`未知的解析器类型: ${source.parser}`);
          break;
      }
    } catch (error) {
      console.error(`解析代理数据失败:`, error);
    }
    return proxies;
  }
  /**
   * 解析简单格式的代理数据 (host:port)
   */
  parseSimpleFormat(data, type, sourceName, proxies) {
    const lines = data.split("\n").filter((line) => line.trim());
    lines.forEach((line) => {
      const [host, port] = line.trim().split(":");
      if (host && port && !isNaN(parseInt(port))) {
        proxies.push({
          id: this.generateProxyId(),
          host: host.trim(),
          port: parseInt(port),
          type,
          source: sourceName,
          createdAt: /* @__PURE__ */ new Date(),
          updatedAt: /* @__PURE__ */ new Date()
        });
      }
    });
  }
  /**
   * 测试HTTP代理 - 优化版本，快速简单可靠
   */
  async testProxy(proxy, testUrl = "http://httpbin.org/ip", timeout = 5e3) {
    var _a;
    const startTime = Date.now();
    try {
      if (proxy.type === "socks4" || proxy.type === "socks5") {
        proxy.isWorking = false;
        proxy.lastChecked = /* @__PURE__ */ new Date();
        console.debug(`⏭️ 跳过SOCKS代理 ${proxy.host}:${proxy.port}`);
        return false;
      }
      const testUrls = [
        "http://httpbin.org/ip",
        "http://icanhazip.com",
        "http://checkip.amazonaws.com"
      ];
      const targetUrl = testUrls[0];
      const proxyUrl = `http://${proxy.host}:${proxy.port}`;
      const agent = new httpsProxyAgent.HttpsProxyAgent(proxyUrl);
      const response = await axios.get(targetUrl, {
        httpsAgent: agent,
        httpAgent: agent,
        timeout,
        validateStatus: (status) => status >= 200 && status < 300,
        headers: {
          "User-Agent": "curl/7.68.0",
          "Accept": "*/*"
        },
        maxRedirects: 0
        // 禁用重定向，提高速度
      });
      if (response.status === 200 && response.data) {
        const endTime2 = Date.now();
        proxy.speed = endTime2 - startTime;
        proxy.isWorking = true;
        proxy.lastChecked = /* @__PURE__ */ new Date();
        console.debug(`✅ 代理 ${proxy.host}:${proxy.port} 可用，速度: ${proxy.speed}ms`);
        return true;
      }
      const endTime = Date.now();
      proxy.speed = void 0;
      proxy.isWorking = false;
      proxy.lastChecked = /* @__PURE__ */ new Date();
      return false;
    } catch (error) {
      proxy.speed = void 0;
      proxy.isWorking = false;
      proxy.lastChecked = /* @__PURE__ */ new Date();
      if (error.code === "ECONNREFUSED") {
        console.debug(`❌ 代理 ${proxy.host}:${proxy.port} 连接被拒绝`);
      } else if (error.code === "ETIMEDOUT") {
        console.debug(`⏰ 代理 ${proxy.host}:${proxy.port} 连接超时`);
      } else if (((_a = error.response) == null ? void 0 : _a.status) === 407) {
        console.debug(`🔐 代理 ${proxy.host}:${proxy.port} 需要认证`);
      }
      return false;
    }
  }
  /**
   * 批量测试代理 - 优化版本，控制并发避免卡顿
   */
  async testProxiesBatch(proxies, maxConcurrent = 10, progressCallback) {
    const timeout = 5e3;
    const maxSpeed = 8e3;
    console.log(`🚀 开始批量测试 ${proxies.length} 个代理，并发数: ${maxConcurrent}`);
    let completedCount = 0;
    const startTime = Date.now();
    const results = [];
    new Array(maxConcurrent).fill(null);
    const testWithSemaphore = async (proxy) => {
      try {
        const isWorking = await this.testProxy(proxy, "http://httpbin.org/ip", timeout);
        completedCount++;
        if (progressCallback) {
          progressCallback({
            completed: completedCount,
            total: proxies.length,
            current: proxy,
            elapsed: ((Date.now() - startTime) / 1e3).toFixed(1)
          });
        }
        if (completedCount % 10 === 0) {
          const elapsed = ((Date.now() - startTime) / 1e3).toFixed(1);
          console.log(`⚡ 测试进度: ${completedCount}/${proxies.length} (${elapsed}s)`);
        }
        return proxy;
      } catch (error) {
        completedCount++;
        proxy.isWorking = false;
        proxy.lastChecked = /* @__PURE__ */ new Date();
        return proxy;
      }
    };
    const batchSize = maxConcurrent;
    for (let i = 0; i < proxies.length; i += batchSize) {
      const batch = proxies.slice(i, i + batchSize);
      console.log(`测试批次 ${Math.floor(i / batchSize) + 1}/${Math.ceil(proxies.length / batchSize)}: ${batch.length} 个代理`);
      const batchPromises = batch.map((proxy) => testWithSemaphore(proxy));
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      if (i + batchSize < proxies.length) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
    }
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1e3).toFixed(1);
    const cleanedResults = results.filter((proxy) => {
      if (proxy.isWorking === false) {
        console.log(`🗑️ 删除失败代理: ${proxy.host}:${proxy.port}`);
        return false;
      }
      if (!proxy.speed) {
        console.log(`🗑️ 删除无速度数据代理: ${proxy.host}:${proxy.port}`);
        return false;
      }
      if (proxy.speed > maxSpeed) {
        console.log(`🗑️ 删除慢速代理: ${proxy.host}:${proxy.port} (${proxy.speed}ms)`);
        return false;
      }
      return true;
    });
    cleanedResults.sort((a, b) => (a.speed || 999999) - (b.speed || 999999));
    const proxyConfig = await this.getProxyConfig();
    const updatedProxies = proxyConfig.proxies.map((existingProxy) => {
      const testResult = cleanedResults.find(
        (r) => r.host === existingProxy.host && r.port === existingProxy.port
      );
      return testResult;
    }).filter(Boolean);
    proxyConfig.proxies = updatedProxies;
    await this.saveProxyConfig(proxyConfig);
    const originalCount = results.length;
    const workingCount = cleanedResults.length;
    const deletedCount = originalCount - workingCount;
    const avgSpeed = cleanedResults.length > 0 ? cleanedResults.reduce((sum, p) => sum + (p.speed || 0), 0) / cleanedResults.length : 0;
    console.log(`🎉 批量测试完成! 用时${duration}秒`);
    console.log(`📊 原始: ${originalCount}, 可用: ${workingCount}, 已清理: ${deletedCount}`);
    console.log(`⚡ 平均速度: ${avgSpeed == null ? void 0 : avgSpeed.toFixed(0)}ms`);
    return cleanedResults;
  }
  /**
   * 删除代理
   */
  async deleteProxies(proxyIds) {
    try {
      const config = await this.getProxyConfig();
      config.proxies = config.proxies.filter((p) => !proxyIds.includes(p.id));
      await this.saveProxyConfig(config);
      console.log(`已删除 ${proxyIds.length} 个代理`);
    } catch (error) {
      console.error("删除代理失败:", error);
      throw new Error(`删除代理失败: ${error instanceof Error ? error.message : "未知错误"}`);
    }
  }
  /**
   * 构建代理URL
   */
  buildProxyUrl(proxy) {
    const auth = proxy.username && proxy.password ? `${proxy.username}:${proxy.password}@` : "";
    return `${proxy.type}://${auth}${proxy.host}:${proxy.port}`;
  }
  /**
   * 去重代理 - 优化版本，保留质量更好的代理
   */
  deduplicateProxies(proxies) {
    const proxyMap = /* @__PURE__ */ new Map();
    proxies.forEach((proxy) => {
      const key = `${proxy.host}:${proxy.port}`;
      const existing = proxyMap.get(key);
      if (!existing) {
        proxyMap.set(key, proxy);
      } else {
        const newScore = this.calculateProxyScore(proxy);
        const existingScore = this.calculateProxyScore(existing);
        if (newScore > existingScore) {
          proxyMap.set(key, proxy);
        }
      }
    });
    const uniqueProxies = Array.from(proxyMap.values());
    console.log(`去重完成: ${proxies.length} -> ${uniqueProxies.length} 个代理`);
    return uniqueProxies;
  }
  /**
   * 过滤高质量代理
   */
  filterHighQualityProxies(proxies) {
    return proxies.filter((proxy) => {
      if (!this.isValidIP(proxy.host)) {
        return false;
      }
      if (proxy.port < 1 || proxy.port > 65535) {
        return false;
      }
      const invalidPorts = [22, 23, 25, 53, 110, 143, 993, 995];
      if (invalidPorts.includes(proxy.port)) {
        return false;
      }
      if (proxy.isWorking === true) {
        return true;
      }
      if (proxy.isWorking === false) {
        return false;
      }
      if (proxy.anonymity === "elite" || proxy.anonymity === "anonymous") {
        return true;
      }
      if (proxy.anonymity === "transparent") {
        return false;
      }
      if (proxy.speed && proxy.speed < 5e3) {
        return true;
      }
      if (proxy.uptime && proxy.uptime > 80) {
        return true;
      }
      const reliableSources = ["Proxifly", "ProxyList.geonode.com"];
      if (reliableSources.some((source) => proxy.source.includes(source))) {
        return true;
      }
      return true;
    });
  }
  /**
   * 验证IP地址格式
   */
  isValidIP(ip) {
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    if (ipv4Regex.test(ip)) {
      const parts = ip.split(".").map(Number);
      if (parts[0] === 10) return false;
      if (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31) return false;
      if (parts[0] === 192 && parts[1] === 168) return false;
      if (parts[0] === 127) return false;
      if (parts[0] === 0) return false;
      if (parts[0] >= 224) return false;
      return true;
    }
    return false;
  }
  /**
   * 数组分块
   */
  chunkArray(array, size) {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }
  /**
   * 生成代理ID
   */
  generateProxyId() {
    return "proxy_" + Date.now() + "_" + Math.random().toString(36).substr(2, 9);
  }
  /**
   * 创建默认配置
   */
  createDefaultConfig() {
    return {
      proxies: [],
      sources: this.defaultSources,
      settings: {
        testTimeout: 1e4,
        testUrl: "https://httpbin.org/ip",
        autoRefresh: false,
        refreshInterval: 36e5,
        // 1小时
        maxConcurrentTests: 10
      }
    };
  }
  /**
   * 验证并修复配置
   */
  validateAndFixConfig(config) {
    var _a, _b, _c, _d, _e;
    const validatedConfig = {
      proxies: [],
      sources: config.sources || this.defaultSources,
      settings: {
        testTimeout: ((_a = config.settings) == null ? void 0 : _a.testTimeout) || 1e4,
        testUrl: ((_b = config.settings) == null ? void 0 : _b.testUrl) || "https://httpbin.org/ip",
        autoRefresh: ((_c = config.settings) == null ? void 0 : _c.autoRefresh) || false,
        refreshInterval: ((_d = config.settings) == null ? void 0 : _d.refreshInterval) || 36e5,
        maxConcurrentTests: ((_e = config.settings) == null ? void 0 : _e.maxConcurrentTests) || 10
      }
    };
    if (Array.isArray(config.proxies)) {
      validatedConfig.proxies = config.proxies.map((proxy) => ({
        id: proxy.id || this.generateProxyId(),
        host: proxy.host || "",
        port: proxy.port || 0,
        type: proxy.type || "http",
        username: proxy.username,
        password: proxy.password,
        country: proxy.country,
        region: proxy.region,
        city: proxy.city,
        isp: proxy.isp,
        anonymity: proxy.anonymity,
        speed: proxy.speed,
        uptime: proxy.uptime,
        lastChecked: proxy.lastChecked ? new Date(proxy.lastChecked) : void 0,
        isWorking: proxy.isWorking,
        source: proxy.source || "未知",
        createdAt: proxy.createdAt ? new Date(proxy.createdAt) : /* @__PURE__ */ new Date(),
        updatedAt: proxy.updatedAt ? new Date(proxy.updatedAt) : /* @__PURE__ */ new Date()
      }));
    }
    return validatedConfig;
  }
  /**
   * 获取或创建加密密钥
   */
  getOrCreateEncryptionKey() {
    const keyPath = path__namespace.join(this.configDir, ".proxy-key");
    if (fs__namespace.existsSync(keyPath)) {
      return fs__namespace.readFileSync(keyPath, "utf-8");
    }
    if (!fs__namespace.existsSync(this.configDir)) {
      fs__namespace.mkdirSync(this.configDir, { recursive: true });
    }
    const key = crypto__namespace.randomBytes(32).toString("hex");
    fs__namespace.writeFileSync(keyPath, key, "utf-8");
    return key;
  }
  /**
   * 加密数据
   */
  encrypt(text) {
    const iv = crypto__namespace.randomBytes(16);
    const key = crypto__namespace.scryptSync(this.encryptionKey, "salt", 32);
    const cipher = crypto__namespace.createCipheriv("aes-256-cbc", key, iv);
    let encrypted = cipher.update(text, "utf8", "hex");
    encrypted += cipher.final("hex");
    return iv.toString("hex") + ":" + encrypted;
  }
  /**
   * 解密数据
   */
  decrypt(text) {
    const parts = text.split(":");
    const iv = Buffer.from(parts.shift(), "hex");
    const encryptedText = parts.join(":");
    const key = crypto__namespace.scryptSync(this.encryptionKey, "salt", 32);
    const decipher = crypto__namespace.createDecipheriv("aes-256-cbc", key, iv);
    let decrypted = decipher.update(encryptedText, "hex", "utf8");
    decrypted += decipher.final("utf8");
    return decrypted;
  }
  /**
   * 从代理列表中选择最佳的N个代理
   */
  selectBestProxies(proxies, maxCount) {
    const filteredProxies = this.filterHighQualityProxies(proxies);
    const scoredProxies = filteredProxies.map((proxy) => ({
      proxy,
      score: this.calculateProxyScore(proxy)
    }));
    scoredProxies.sort((a, b) => b.score - a.score);
    return scoredProxies.slice(0, maxCount).map((item) => item.proxy);
  }
  /**
   * 计算代理质量评分
   */
  calculateProxyScore(proxy) {
    let score = 0;
    if (proxy.isWorking === true) {
      score += 100;
    }
    switch (proxy.anonymity) {
      case "elite":
        score += 50;
        break;
      case "anonymous":
        score += 30;
        break;
      case "transparent":
        score -= 20;
        break;
    }
    if (proxy.speed) {
      if (proxy.speed < 1e3) score += 40;
      else if (proxy.speed < 3e3) score += 30;
      else if (proxy.speed < 5e3) score += 20;
      else if (proxy.speed < 1e4) score += 10;
    }
    if (proxy.uptime) {
      if (proxy.uptime > 95) score += 30;
      else if (proxy.uptime > 90) score += 25;
      else if (proxy.uptime > 80) score += 20;
      else if (proxy.uptime > 70) score += 15;
      else if (proxy.uptime > 60) score += 10;
    }
    switch (proxy.type) {
      case "http":
      case "https":
        score += 20;
        break;
      case "socks5":
        score += 25;
        break;
      case "socks4":
        score += 15;
        break;
    }
    const reliableSources = [
      "Proxifly",
      "ProxyList.geonode.com",
      "GitHub TheSpeedX",
      "ProxyScrape",
      "GitHub Monosans"
    ];
    if (reliableSources.some((source) => proxy.source.includes(source))) {
      score += 15;
    }
    const preferredCountries = ["US", "DE", "NL", "SG", "JP", "UK", "CA", "FR"];
    if (proxy.country && preferredCountries.includes(proxy.country.toUpperCase())) {
      score += 10;
    }
    return score;
  }
  /**
   * 快速测试代理可用性 (小批量并发测试)
   */
  async quickTestProxies(proxies) {
    if (proxies.length === 0) return [];
    console.log(`快速测试 ${proxies.length} 个代理的可用性...`);
    const results = [];
    const batchSize = 5;
    for (let i = 0; i < proxies.length; i += batchSize) {
      const batch = proxies.slice(i, i + batchSize);
      const batchPromises = batch.map(async (proxy) => {
        try {
          const isWorking = await this.testProxy(proxy, "http://httpbin.org/ip", 3e3);
          return { ...proxy, isWorking, lastChecked: /* @__PURE__ */ new Date() };
        } catch (error) {
          return { ...proxy, isWorking: false, lastChecked: /* @__PURE__ */ new Date() };
        }
      });
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      if (i + batchSize < proxies.length) {
        await new Promise((resolve) => setTimeout(resolve, 200));
      }
    }
    const workingCount = results.filter((p) => p.isWorking).length;
    console.log(`快速测试完成: ${workingCount}/${results.length} 个代理可用`);
    return results;
  }
  /**
   * 按质量对代理进行排序
   */
  sortProxiesByQuality(proxies) {
    return proxies.sort((a, b) => {
      if (a.isWorking === true && b.isWorking !== true) return -1;
      if (b.isWorking === true && a.isWorking !== true) return 1;
      if (a.isWorking === false && b.isWorking !== false) return 1;
      if (b.isWorking === false && a.isWorking !== false) return -1;
      const scoreA = this.calculateProxyScore(a);
      const scoreB = this.calculateProxyScore(b);
      return scoreB - scoreA;
    });
  }
}
class PerformanceMonitorService extends events.EventEmitter {
  constructor() {
    super(...arguments);
    this.isMonitoring = false;
    this.monitoringInterval = null;
    this.processMonitors = /* @__PURE__ */ new Map();
    this.metricsHistory = [];
    this.processMetricsHistory = /* @__PURE__ */ new Map();
    this.alerts = [];
    this.lastAlerts = /* @__PURE__ */ new Map();
    this.DEFAULT_THRESHOLDS = {
      cpu: { warning: 85, critical: 98 },
      // 提高CPU阈值，避免正常高负载时报警
      memory: { warning: 80, critical: 95 },
      disk: { warning: 85, critical: 95 },
      processMemory: { warning: 500, critical: 1e3 }
      // MB
    };
    this.thresholds = { ...this.DEFAULT_THRESHOLDS };
    this.MAX_HISTORY_SIZE = 1e3;
    this.MONITORING_INTERVAL = 1e4;
    this.PROCESS_MONITORING_INTERVAL = 5e3;
  }
  // 5秒，减少频率
  /**
   * 开始系统性能监控
   */
  startMonitoring() {
    if (this.isMonitoring) {
      return;
    }
    this.isMonitoring = true;
    this.monitoringInterval = setInterval(() => {
      this.collectSystemMetrics();
    }, this.MONITORING_INTERVAL);
    console.log("性能监控已启动");
    this.emit("monitoring-started");
  }
  /**
   * 停止系统性能监控
   */
  stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }
    this.isMonitoring = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.processMonitors.forEach((interval) => {
      clearInterval(interval);
    });
    this.processMonitors.clear();
    console.log("性能监控已停止");
    this.emit("monitoring-stopped");
  }
  /**
   * 开始监控特定进程
   */
  startProcessMonitoring(pid, projectId) {
    if (this.processMonitors.has(pid)) {
      return;
    }
    const interval = setInterval(async () => {
      try {
        const result = await this.collectProcessMetrics(pid, projectId);
        if (result === null) {
          this.stopProcessMonitoring(pid);
          this.notifyProcessEnded(pid, projectId);
        }
      } catch (error) {
        this.stopProcessMonitoring(pid);
        this.notifyProcessEnded(pid, projectId);
      }
    }, this.PROCESS_MONITORING_INTERVAL);
    this.processMonitors.set(pid, interval);
  }
  /**
   * 停止监控特定进程
   */
  stopProcessMonitoring(pid) {
    const interval = this.processMonitors.get(pid);
    if (interval) {
      clearInterval(interval);
      this.processMonitors.delete(pid);
      this.processMetricsHistory.delete(pid);
    }
  }
  /**
   * 通知进程已结束
   */
  notifyProcessEnded(pid, projectId) {
    try {
      const { ipcMain } = require("electron");
      ipcMain.emit("performance:process-ended", null, pid, projectId);
    } catch (error) {
      console.warn("通知进程结束失败:", error);
    }
  }
  /**
   * 获取系统性能指标
   */
  async getSystemMetrics() {
    return await this.collectSystemMetrics();
  }
  /**
   * 获取进程性能指标
   */
  async getProcessMetrics(pid) {
    return await this.collectProcessMetrics(pid, "unknown");
  }
  /**
   * 获取性能历史数据
   */
  getMetricsHistory(limit) {
    const history = this.metricsHistory;
    return limit ? history.slice(-limit) : history;
  }
  /**
   * 获取进程性能历史数据
   */
  getProcessMetricsHistory(pid, limit) {
    const history = this.processMetricsHistory.get(pid) || [];
    return limit ? history.slice(-limit) : history;
  }
  /**
   * 获取性能警报
   */
  getAlerts(limit) {
    const alerts = this.alerts;
    return limit ? alerts.slice(-limit) : alerts;
  }
  /**
   * 清除警报
   */
  clearAlerts() {
    this.alerts = [];
    this.emit("alerts-cleared");
  }
  /**
   * 设置性能阈值
   */
  setThresholds(thresholds) {
    this.thresholds = { ...this.thresholds, ...thresholds };
    console.log("性能阈值已更新:", this.thresholds);
  }
  /**
   * 获取性能阈值
   */
  getThresholds() {
    return { ...this.thresholds };
  }
  /**
   * 收集系统性能指标
   */
  async collectSystemMetrics() {
    var _a, _b;
    try {
      const cpuUsage = await this.getCpuUsage();
      const memoryInfo = this.getMemoryInfo();
      const diskInfo = await this.getDiskInfo();
      const networkInfo = await this.getNetworkInfo();
      const metrics = {
        timestamp: /* @__PURE__ */ new Date(),
        cpu: {
          usage: cpuUsage,
          cores: os__namespace.cpus().length,
          model: ((_a = os__namespace.cpus()[0]) == null ? void 0 : _a.model) || "Unknown",
          speed: ((_b = os__namespace.cpus()[0]) == null ? void 0 : _b.speed) || 0
        },
        memory: memoryInfo,
        disk: diskInfo,
        network: networkInfo
      };
      this.metricsHistory.push(metrics);
      if (this.metricsHistory.length > this.MAX_HISTORY_SIZE) {
        this.metricsHistory.shift();
      }
      this.checkThresholds(metrics);
      this.emit("system-metrics", metrics);
      return metrics;
    } catch (error) {
      console.error("收集系统指标失败:", error);
      throw error;
    }
  }
  /**
   * 收集进程性能指标
   */
  async collectProcessMetrics(pid, projectId) {
    try {
      const processInfo = await this.getProcessInfo(pid);
      if (!processInfo) {
        return null;
      }
      const metrics = {
        pid,
        projectId,
        timestamp: /* @__PURE__ */ new Date(),
        cpu: processInfo.cpu,
        memory: processInfo.memory,
        uptime: processInfo.uptime,
        status: processInfo.status
      };
      if (!this.processMetricsHistory.has(pid)) {
        this.processMetricsHistory.set(pid, []);
      }
      const history = this.processMetricsHistory.get(pid);
      history.push(metrics);
      if (history.length > this.MAX_HISTORY_SIZE) {
        history.shift();
      }
      this.checkProcessThresholds(metrics);
      this.emit("process-metrics", metrics);
      return metrics;
    } catch (error) {
      return null;
    }
  }
  /**
   * 获取CPU使用率
   */
  async getCpuUsage() {
    return new Promise((resolve) => {
      const startMeasure = os__namespace.cpus();
      setTimeout(() => {
        const endMeasure = os__namespace.cpus();
        let totalIdle = 0;
        let totalTick = 0;
        for (let i = 0; i < startMeasure.length; i++) {
          const startCpu = startMeasure[i];
          const endCpu = endMeasure[i];
          const idle = endCpu.times.idle - startCpu.times.idle;
          const total = Object.values(endCpu.times).reduce((a, b) => a + b) - Object.values(startCpu.times).reduce((a, b) => a + b);
          totalIdle += idle;
          totalTick += total;
        }
        const usage = 100 - totalIdle / totalTick * 100;
        resolve(Math.round(usage * 100) / 100);
      }, 100);
    });
  }
  /**
   * 获取内存信息
   */
  getMemoryInfo() {
    const total = os__namespace.totalmem();
    const free = os__namespace.freemem();
    const used = total - free;
    const usage = used / total * 100;
    return {
      total,
      used,
      free,
      usage: Math.round(usage * 100) / 100
    };
  }
  /**
   * 获取磁盘信息
   */
  async getDiskInfo() {
    try {
      const stats = fs__namespace.statSync(process.cwd());
      return {
        total: 1e12,
        // 1TB
        used: 5e11,
        // 500GB
        free: 5e11,
        // 500GB
        usage: 50
      };
    } catch (error) {
      return {
        total: 0,
        used: 0,
        free: 0,
        usage: 0
      };
    }
  }
  /**
   * 获取网络信息
   */
  async getNetworkInfo() {
    return {
      bytesReceived: 0,
      bytesSent: 0,
      packetsReceived: 0,
      packetsSent: 0
    };
  }
  /**
   * 获取进程信息
   */
  async getProcessInfo(pid) {
    try {
      const { exec } = require("child_process");
      const { promisify } = require("util");
      const execAsync2 = promisify(exec);
      if (process.platform === "win32") {
        try {
          const psCommand = `Get-Process -Id ${pid} | Select-Object ProcessName,WorkingSet,CPU,StartTime | ConvertTo-Json`;
          const { stdout } = await execAsync2(`powershell -Command "${psCommand}"`);
          if (!stdout.trim()) {
            throw new Error("进程不存在");
          }
          const processInfo = JSON.parse(stdout.trim());
          const memoryBytes = processInfo.WorkingSet || 0;
          const cpuTime = processInfo.CPU || 0;
          let uptime = 0;
          if (processInfo.StartTime) {
            const startTime = new Date(processInfo.StartTime);
            uptime = (Date.now() - startTime.getTime()) / 1e3;
          }
          const cpuUsage = Math.min(cpuTime / uptime * 100, 100) || 0;
          return {
            cpu: isNaN(cpuUsage) ? 0 : cpuUsage,
            memory: {
              rss: memoryBytes,
              heapTotal: memoryBytes,
              heapUsed: Math.floor(memoryBytes * 0.8),
              external: Math.floor(memoryBytes * 0.1)
            },
            uptime,
            status: "running"
          };
        } catch (error) {
          try {
            const { stdout } = await execAsync2(`tasklist /FI "PID eq ${pid}" /FO CSV`);
            const lines = stdout.trim().split("\n");
            if (lines.length < 2) {
              return null;
            }
            const processLine = lines[1];
            const fields = processLine.split(",");
            let memoryStr = fields[4] || "0";
            memoryStr = memoryStr.replace(/[",\s]/g, "").replace(/K$/, "");
            const memoryKB = parseInt(memoryStr) || 0;
            const memoryBytes = memoryKB * 1024;
            return {
              cpu: Math.random() * 5,
              // 简化的CPU使用率
              memory: {
                rss: memoryBytes,
                heapTotal: memoryBytes,
                heapUsed: Math.floor(memoryBytes * 0.8),
                external: Math.floor(memoryBytes * 0.1)
              },
              uptime: Date.now() / 1e3,
              status: "running"
            };
          } catch (fallbackError) {
            return null;
          }
        }
      } else {
        try {
          const { stdout } = await execAsync2(`ps -p ${pid} -o pid,pcpu,rss,etime --no-headers`);
          const parts = stdout.trim().split(/\s+/);
          if (parts.length < 4) {
            throw new Error("进程不存在");
          }
          const cpuUsage = parseFloat(parts[1]) || 0;
          const memoryKB = parseInt(parts[2]) || 0;
          const memoryBytes = memoryKB * 1024;
          return {
            cpu: cpuUsage,
            memory: {
              rss: memoryBytes,
              heapTotal: memoryBytes,
              heapUsed: Math.floor(memoryBytes * 0.8),
              external: Math.floor(memoryBytes * 0.1)
            },
            uptime: Date.now() / 1e3,
            // 简化处理
            status: "running"
          };
        } catch (error) {
          return null;
        }
      }
    } catch (error) {
      return null;
    }
  }
  /**
   * 检查系统阈值
   */
  checkThresholds(metrics) {
    if (metrics.cpu.usage >= this.thresholds.cpu.critical) {
      this.createAlert("cpu", "critical", `CPU使用率过高: ${metrics.cpu.usage}%`, metrics.cpu.usage, this.thresholds.cpu.critical);
    } else if (metrics.cpu.usage >= this.thresholds.cpu.warning) {
      this.createAlert("cpu", "warning", `CPU使用率较高: ${metrics.cpu.usage}%`, metrics.cpu.usage, this.thresholds.cpu.warning);
    }
    if (metrics.memory.usage >= this.thresholds.memory.critical) {
      this.createAlert("memory", "critical", `内存使用率过高: ${metrics.memory.usage}%`, metrics.memory.usage, this.thresholds.memory.critical);
    } else if (metrics.memory.usage >= this.thresholds.memory.warning) {
      this.createAlert("memory", "warning", `内存使用率较高: ${metrics.memory.usage}%`, metrics.memory.usage, this.thresholds.memory.warning);
    }
    if (metrics.disk.usage >= this.thresholds.disk.critical) {
      this.createAlert("disk", "critical", `磁盘使用率过高: ${metrics.disk.usage}%`, metrics.disk.usage, this.thresholds.disk.critical);
    } else if (metrics.disk.usage >= this.thresholds.disk.warning) {
      this.createAlert("disk", "warning", `磁盘使用率较高: ${metrics.disk.usage}%`, metrics.disk.usage, this.thresholds.disk.warning);
    }
  }
  /**
   * 检查进程阈值
   */
  checkProcessThresholds(metrics) {
    const memoryMB = metrics.memory.rss / (1024 * 1024);
    if (memoryMB >= this.thresholds.processMemory.critical) {
      this.createAlert("process", "critical", `进程内存使用过高: ${memoryMB.toFixed(1)}MB`, memoryMB, this.thresholds.processMemory.critical, metrics.projectId);
    } else if (memoryMB >= this.thresholds.processMemory.warning) {
      this.createAlert("process", "warning", `进程内存使用较高: ${memoryMB.toFixed(1)}MB`, memoryMB, this.thresholds.processMemory.warning, metrics.projectId);
    }
  }
  /**
   * 创建警报
   */
  createAlert(type, level, message, value, threshold, projectId) {
    const alertKey = `${type}-${level}-${projectId || "system"}`;
    const now = Date.now();
    const lastAlertTime = this.lastAlerts.get(alertKey) || 0;
    const ALERT_COOLDOWN = 3e4;
    if (now - lastAlertTime < ALERT_COOLDOWN) {
      return;
    }
    const alert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      level,
      message,
      value,
      threshold,
      timestamp: /* @__PURE__ */ new Date(),
      projectId
    };
    this.alerts.push(alert);
    this.lastAlerts.set(alertKey, now);
    if (this.alerts.length > 100) {
      this.alerts.shift();
    }
    this.emit("performance-alert", alert);
    console.warn(`性能警报 [${level.toUpperCase()}]: ${message}`);
  }
}
class BatchOperationService extends events.EventEmitter {
  constructor(projectService, gitService, processService, dependencyService) {
    super();
    this.projectService = projectService;
    this.gitService = gitService;
    this.processService = processService;
    this.dependencyService = dependencyService;
    this.operations = /* @__PURE__ */ new Map();
    this.runningOperations = /* @__PURE__ */ new Set();
  }
  /**
   * 执行批量操作
   */
  async executeBatchOperation(type, projectIds, options = {}) {
    const operationId = this.generateOperationId();
    const operation = {
      id: operationId,
      type,
      projectIds,
      status: "pending",
      progress: {
        total: projectIds.length,
        completed: 0,
        failed: 0
      },
      results: [],
      startTime: /* @__PURE__ */ new Date()
    };
    this.operations.set(operationId, operation);
    this.emit("operation-created", operation);
    this.executeOperation(operation, options).catch((error) => {
      console.error(`批量操作 ${operationId} 执行失败:`, error);
      operation.status = "failed";
      this.emit("operation-failed", operation, error);
    });
    return operationId;
  }
  /**
   * 取消批量操作
   */
  async cancelBatchOperation(operationId) {
    const operation = this.operations.get(operationId);
    if (!operation) {
      throw new Error(`操作 ${operationId} 不存在`);
    }
    if (operation.status === "completed" || operation.status === "failed") {
      throw new Error(`操作 ${operationId} 已完成，无法取消`);
    }
    operation.status = "cancelled";
    operation.endTime = /* @__PURE__ */ new Date();
    operation.duration = operation.endTime.getTime() - operation.startTime.getTime();
    this.runningOperations.delete(operationId);
    this.emit("operation-cancelled", operation);
  }
  /**
   * 获取批量操作状态
   */
  getBatchOperation(operationId) {
    return this.operations.get(operationId) || null;
  }
  /**
   * 获取所有批量操作
   */
  getAllBatchOperations() {
    return Array.from(this.operations.values());
  }
  /**
   * 清理已完成的操作
   */
  cleanupCompletedOperations() {
    for (const [id, operation] of this.operations.entries()) {
      if (operation.status === "completed" || operation.status === "failed" || operation.status === "cancelled") {
        this.operations.delete(id);
      }
    }
    this.emit("operations-cleaned");
  }
  /**
   * 执行操作
   */
  async executeOperation(operation, options) {
    var _a;
    const { maxConcurrent = 3, continueOnError = true, timeout = 3e4 } = options;
    operation.status = "running";
    this.runningOperations.add(operation.id);
    this.emit("operation-started", operation);
    const projects = await this.getProjectsByIds(operation.projectIds);
    const chunks = this.chunkArray(projects, maxConcurrent);
    for (const chunk of chunks) {
      if (operation.status === "cancelled") {
        break;
      }
      const promises = chunk.map(
        (project) => this.executeProjectOperation(operation, project, timeout)
      );
      const results = await Promise.allSettled(promises);
      for (let i = 0; i < results.length; i++) {
        const result = results[i];
        const project = chunk[i];
        if (result.status === "fulfilled") {
          operation.results.push(result.value);
          if (result.value.status === "success") {
            operation.progress.completed++;
          } else {
            operation.progress.failed++;
          }
        } else {
          const failedResult = {
            projectId: project.id,
            projectName: project.name,
            status: "failed",
            error: ((_a = result.reason) == null ? void 0 : _a.message) || "未知错误",
            duration: 0
          };
          operation.results.push(failedResult);
          operation.progress.failed++;
        }
        this.emit("operation-progress", operation);
        if (!continueOnError && operation.progress.failed > 0) {
          operation.status = "failed";
          break;
        }
      }
    }
    if (operation.status !== "cancelled") {
      operation.status = operation.progress.failed > 0 ? "failed" : "completed";
    }
    operation.endTime = /* @__PURE__ */ new Date();
    operation.duration = operation.endTime.getTime() - operation.startTime.getTime();
    this.runningOperations.delete(operation.id);
    this.emit("operation-completed", operation);
  }
  /**
   * 执行单个项目操作
   */
  async executeProjectOperation(operation, project, timeout) {
    var _a;
    const startTime = Date.now();
    operation.progress.current = project.name;
    try {
      let message = "";
      switch (operation.type) {
        case "start":
          await this.projectService.startProject(project.id);
          message = "项目启动成功";
          break;
        case "stop":
          await this.projectService.stopProject(project.id);
          message = "项目停止成功";
          break;
        case "restart":
          await this.projectService.restartProject(project.id);
          message = "项目重启成功";
          break;
        case "git-pull":
          if ((_a = project.gitStatus) == null ? void 0 : _a.isRepo) {
            await this.gitService.pull(project.path);
            message = "Git拉取成功";
          } else {
            throw new Error("项目不是Git仓库");
          }
          break;
        case "install-deps":
          await this.dependencyService.installAllDependencies(project.path, project.type);
          message = "依赖安装成功";
          break;
        case "delete":
          await this.projectService.deleteProject(project.id, false);
          message = "项目删除成功";
          break;
        default:
          throw new Error(`不支持的操作类型: ${operation.type}`);
      }
      const duration = Date.now() - startTime;
      return {
        projectId: project.id,
        projectName: project.name,
        status: "success",
        message,
        duration
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      return {
        projectId: project.id,
        projectName: project.name,
        status: "failed",
        error: error instanceof Error ? error.message : "未知错误",
        duration
      };
    }
  }
  /**
   * 根据ID获取项目列表
   */
  async getProjectsByIds(projectIds) {
    const allProjects = await this.projectService.listProjects();
    return allProjects.filter((project) => projectIds.includes(project.id));
  }
  /**
   * 将数组分块
   */
  chunkArray(array, chunkSize) {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }
  /**
   * 生成操作ID
   */
  generateOperationId() {
    return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  /**
   * 获取操作统计信息
   */
  getOperationStats() {
    const operations = Array.from(this.operations.values());
    return {
      total: operations.length,
      running: operations.filter((op) => op.status === "running").length,
      completed: operations.filter((op) => op.status === "completed").length,
      failed: operations.filter((op) => op.status === "failed").length,
      cancelled: operations.filter((op) => op.status === "cancelled").length
    };
  }
  /**
   * 估算操作时间
   */
  estimateOperationTime(type, projectCount) {
    const averageTimes = {
      start: 3e3,
      // 3秒
      stop: 1e3,
      // 1秒
      restart: 4e3,
      // 4秒
      "git-pull": 5e3,
      // 5秒
      "install-deps": 3e4,
      // 30秒
      delete: 500
      // 0.5秒
    };
    const baseTime = averageTimes[type] || 5e3;
    return baseTime * projectCount;
  }
  /**
   * 验证批量操作
   */
  validateBatchOperation(type, projectIds) {
    const errors = [];
    const warnings = [];
    if (projectIds.length === 0) {
      errors.push("未选择任何项目");
    }
    if (projectIds.length > 50) {
      warnings.push("选择的项目数量较多，操作可能需要较长时间");
    }
    const runningOperations = Array.from(this.operations.values()).filter((op) => op.status === "running" && op.type === type);
    if (runningOperations.length > 0) {
      warnings.push(`已有 ${runningOperations.length} 个相同类型的操作正在运行`);
    }
    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }
}
class GitHubService {
  /**
   * 获取GitHub用户的仓库列表
   */
  async getUserRepositories(username, token) {
    const headers = {
      "Accept": "application/vnd.github.v3+json",
      "User-Agent": "Git-Manager"
    };
    if (token) {
      headers["Authorization"] = `token ${token}`;
    }
    try {
      const response = await fetch(`https://api.github.com/users/${username}/repos?per_page=100&sort=updated`, {
        headers
      });
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error(`用户 ${username} 不存在`);
        } else if (response.status === 403) {
          throw new Error("API 请求频率限制，请稍后重试");
        } else {
          throw new Error(`获取仓库失败: ${response.statusText}`);
        }
      }
      const repositories = await response.json();
      return repositories;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error("网络请求失败，请检查网络连接");
    }
  }
  /**
   * 获取Gitee用户的仓库列表
   */
  async getGiteeUserRepositories(username, token) {
    const headers = {
      "Accept": "application/json",
      "User-Agent": "Git-Manager"
    };
    if (token) {
      headers["Authorization"] = `token ${token}`;
    }
    try {
      const response = await fetch(`https://gitee.com/api/v5/users/${username}/repos?per_page=100&sort=updated`, {
        headers
      });
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error(`用户 ${username} 不存在`);
        } else if (response.status === 403) {
          throw new Error("API 请求频率限制，请稍后重试");
        } else {
          throw new Error(`获取仓库失败: ${response.statusText}`);
        }
      }
      const repositories = await response.json();
      return repositories;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error("网络请求失败，请检查网络连接");
    }
  }
  /**
   * 使用令牌获取当前用户的GitHub仓库
   */
  async getAuthenticatedUserRepositories(token) {
    console.log("GitHubService: Getting authenticated user repositories with token");
    const headers = {
      "Accept": "application/vnd.github.v3+json",
      "User-Agent": "Git-Manager",
      "Authorization": `token ${token}`
    };
    try {
      const url = "https://api.github.com/user/repos?per_page=100&sort=updated&affiliation=owner,collaborator,organization_member";
      console.log("GitHubService: Fetching from URL:", url);
      const response = await fetch(url, {
        headers
      });
      console.log("GitHubService: Response status:", response.status);
      if (!response.ok) {
        const errorText = await response.text();
        console.error("GitHubService: API Error:", errorText);
        if (response.status === 401) {
          throw new Error("令牌无效或已过期");
        } else if (response.status === 403) {
          throw new Error("API 请求频率限制，请稍后重试");
        } else {
          throw new Error(`获取仓库失败: ${response.statusText}`);
        }
      }
      const repositories = await response.json();
      console.log("GitHubService: Retrieved repositories count:", repositories.length);
      console.log("GitHubService: First few repositories:", repositories.slice(0, 3).map((r) => ({ name: r.name, full_name: r.full_name })));
      return repositories;
    } catch (error) {
      console.error("GitHubService: Error getting repositories:", error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error("网络请求失败，请检查网络连接");
    }
  }
  /**
   * 使用令牌获取当前用户的Gitee仓库
   */
  async getAuthenticatedGiteeUserRepositories(token) {
    const headers = {
      "Accept": "application/json",
      "User-Agent": "Git-Manager"
    };
    try {
      const response = await fetch(`https://gitee.com/api/v5/user/repos?access_token=${token}&per_page=100&sort=updated`, {
        headers
      });
      if (!response.ok) {
        if (response.status === 401) {
          throw new Error("令牌无效或已过期");
        } else if (response.status === 403) {
          throw new Error("API 请求频率限制，请稍后重试");
        } else {
          throw new Error(`获取仓库失败: ${response.statusText}`);
        }
      }
      const repositories = await response.json();
      return repositories;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error("网络请求失败，请检查网络连接");
    }
  }
  /**
   * 根据平台获取用户仓库
   */
  async getRepositoriesByPlatform(platform, username, token) {
    if (platform === "github") {
      return this.getUserRepositories(username, token);
    } else {
      return this.getGiteeUserRepositories(username, token);
    }
  }
  /**
   * 使用令牌获取当前用户的仓库（不需要用户名）
   */
  async getRepositoriesByToken(platform, token) {
    if (platform === "github") {
      return this.getAuthenticatedUserRepositories(token);
    } else {
      return this.getAuthenticatedGiteeUserRepositories(token);
    }
  }
  /**
   * 克隆仓库到指定目录
   */
  async cloneRepository(repository, targetPath, useSSH = false, maxRetries = 3) {
    const path2 = require("path");
    const fs2 = require("fs").promises;
    console.log(`GitHubService: Cloning repository ${repository.name} to ${targetPath}`);
    try {
      await fs2.access(targetPath);
    } catch {
      await fs2.mkdir(targetPath, { recursive: true });
    }
    const cloneUrl = useSSH ? repository.ssh_url : repository.clone_url;
    const projectPath = path2.join(targetPath, repository.name);
    try {
      await fs2.access(projectPath);
      console.log(`GitHubService: Directory ${projectPath} already exists`);
      try {
        await fs2.access(path2.join(projectPath, ".git"));
        console.log(`GitHubService: ${projectPath} is already a git repository, skipping clone`);
        return;
      } catch {
        console.log(`GitHubService: ${projectPath} exists but is not a git repository, removing and re-cloning`);
        await fs2.rmdir(projectPath, { recursive: true });
      }
    } catch {
    }
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`GitHubService: Clone attempt ${attempt}/${maxRetries} for ${repository.name}`);
        await this.performClone(cloneUrl, projectPath);
        console.log(`GitHubService: Successfully cloned ${repository.name}`);
        return;
      } catch (error) {
        console.error(`GitHubService: Clone attempt ${attempt} failed:`, error);
        if (attempt === maxRetries) {
          throw new Error(`克隆失败 (尝试了 ${maxRetries} 次): ${error instanceof Error ? error.message : "未知错误"}`);
        }
        await new Promise((resolve) => setTimeout(resolve, 2e3 * attempt));
      }
    }
  }
  /**
   * 执行实际的克隆操作
   */
  async performClone(cloneUrl, projectPath) {
    const { spawn } = require("child_process");
    return new Promise((resolve, reject) => {
      const gitArgs = [
        "clone",
        "--depth",
        "1",
        // 浅克隆，只获取最新提交
        "--single-branch",
        // 只克隆默认分支
        cloneUrl,
        projectPath
      ];
      console.log(`GitHubService: Executing git ${gitArgs.join(" ")}`);
      const gitProcess = spawn("git", gitArgs, {
        stdio: "pipe",
        timeout: 6e4
        // 60秒超时
      });
      let errorOutput = "";
      let stdOutput = "";
      gitProcess.stdout.on("data", (data) => {
        stdOutput += data.toString();
      });
      gitProcess.stderr.on("data", (data) => {
        errorOutput += data.toString();
      });
      gitProcess.on("close", (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Git clone failed with code ${code}: ${errorOutput || stdOutput}`));
        }
      });
      gitProcess.on("error", (error) => {
        reject(new Error(`Git process error: ${error.message}`));
      });
      setTimeout(() => {
        gitProcess.kill("SIGTERM");
        reject(new Error("Git clone timeout after 60 seconds"));
      }, 6e4);
    });
  }
}
const execAsync = util.promisify(child_process.exec);
const readFileAsync = util.promisify(fs__namespace.readFile);
const accessAsync = util.promisify(fs__namespace.access);
class ProjectDetectionService {
  /**
   * 检测项目类型和相关信息
   */
  async detectProject(projectPath) {
    const result = {
      type: "other",
      startCommand: "",
      hasEnvironment: false,
      hasDependencies: false,
      environmentInfo: "",
      dependencyInfo: "",
      suggestions: []
    };
    try {
      result.type = await this.detectProjectType(projectPath);
      switch (result.type) {
        case "nodejs":
          await this.detectNodejsProject(projectPath, result);
          break;
        case "python":
          await this.detectPythonProject(projectPath, result);
          break;
        case "shell":
          await this.detectShellProject(projectPath, result);
          break;
        case "docker":
          await this.detectDockerProject(projectPath, result);
          break;
        default:
          await this.detectGenericProject(projectPath, result);
      }
      return result;
    } catch (error) {
      console.error("Project detection failed:", error);
      return result;
    }
  }
  /**
   * 检测项目类型
   */
  async detectProjectType(projectPath) {
    const files = await fs__namespace.promises.readdir(projectPath);
    if (files.includes("package.json")) {
      return "nodejs";
    }
    if (files.includes("requirements.txt") || files.includes("setup.py") || files.includes("pyproject.toml") || files.includes("Pipfile") || files.some((file) => file.endsWith(".py"))) {
      return "python";
    }
    if (files.includes("Dockerfile") || files.includes("docker-compose.yml")) {
      return "docker";
    }
    if (files.some((file) => file.endsWith(".sh") || file.endsWith(".bat"))) {
      return "shell";
    }
    return "other";
  }
  /**
   * 检测Node.js项目
   */
  async detectNodejsProject(projectPath, result) {
    try {
      const packageJsonPath = path__namespace.join(projectPath, "package.json");
      const packageJson = JSON.parse(await readFileAsync(packageJsonPath, "utf8"));
      if (packageJson.scripts) {
        if (packageJson.scripts.start) {
          result.startCommand = "npm start";
        } else if (packageJson.scripts.dev) {
          result.startCommand = "npm run dev";
        } else if (packageJson.scripts.serve) {
          result.startCommand = "npm run serve";
        } else if (packageJson.main) {
          result.startCommand = `node ${packageJson.main}`;
        }
      }
      result.hasDependencies = !!(packageJson.dependencies || packageJson.devDependencies);
      if (result.hasDependencies) {
        const depCount = Object.keys(packageJson.dependencies || {}).length;
        const devDepCount = Object.keys(packageJson.devDependencies || {}).length;
        result.dependencyInfo = `${depCount} 个依赖, ${devDepCount} 个开发依赖`;
      }
      try {
        const { stdout } = await execAsync("node --version");
        result.hasEnvironment = true;
        result.environmentInfo = `Node.js ${stdout.trim()}`;
      } catch (error) {
        result.hasEnvironment = false;
        result.environmentInfo = "Node.js 未安装";
        result.suggestions.push("请安装 Node.js 运行环境");
      }
      try {
        await execAsync("npm --version");
      } catch (error) {
        result.suggestions.push("请安装 npm 包管理器");
      }
      try {
        await accessAsync(path__namespace.join(projectPath, "node_modules"));
      } catch (error) {
        result.suggestions.push("运行 npm install 安装依赖");
      }
    } catch (error) {
      console.error("Failed to detect Node.js project:", error);
    }
  }
  /**
   * 检测Python项目
   */
  async detectPythonProject(projectPath, result) {
    try {
      const files = await fs__namespace.promises.readdir(projectPath);
      const mainFiles = ["main.py", "app.py", "run.py", "start.py", "index.py"];
      const foundMain = mainFiles.find((file) => files.includes(file));
      if (foundMain) {
        result.startCommand = `python ${foundMain}`;
      } else {
        const pyFiles = files.filter((file) => file.endsWith(".py"));
        if (pyFiles.length > 0) {
          result.startCommand = `python ${pyFiles[0]}`;
        }
      }
      if (files.includes("requirements.txt")) {
        result.hasDependencies = true;
        try {
          const requirements = await readFileAsync(path__namespace.join(projectPath, "requirements.txt"), "utf8");
          const depCount = requirements.split("\n").filter((line) => line.trim() && !line.startsWith("#")).length;
          result.dependencyInfo = `requirements.txt (${depCount} 个依赖)`;
        } catch (error) {
          result.dependencyInfo = "requirements.txt";
        }
      }
      try {
        const { stdout } = await execAsync("python --version");
        result.hasEnvironment = true;
        result.environmentInfo = stdout.trim();
      } catch (error) {
        try {
          const { stdout } = await execAsync("python3 --version");
          result.hasEnvironment = true;
          result.environmentInfo = stdout.trim();
          if (result.startCommand.startsWith("python ")) {
            result.startCommand = result.startCommand.replace("python ", "python3 ");
          }
        } catch (error2) {
          result.hasEnvironment = false;
          result.environmentInfo = "Python 未安装";
          result.suggestions.push("请安装 Python 运行环境");
        }
      }
      try {
        await execAsync("pip --version");
      } catch (error) {
        result.suggestions.push("请安装 pip 包管理器");
      }
      if (files.includes("venv") || files.includes(".venv")) {
        result.suggestions.push("检测到虚拟环境，建议激活后运行");
      }
    } catch (error) {
      console.error("Failed to detect Python project:", error);
    }
  }
  /**
   * 检测Shell项目
   */
  async detectShellProject(projectPath, result) {
    try {
      const files = await fs__namespace.promises.readdir(projectPath);
      const shellFiles = files.filter((file) => file.endsWith(".sh") || file.endsWith(".bat"));
      const mainFiles = ["start.sh", "run.sh", "main.sh", "index.sh", "start.bat", "run.bat"];
      const foundMain = mainFiles.find((file) => shellFiles.includes(file));
      if (foundMain) {
        result.startCommand = process.platform === "win32" ? foundMain : `bash ${foundMain}`;
      } else if (shellFiles.length > 0) {
        result.startCommand = process.platform === "win32" ? shellFiles[0] : `bash ${shellFiles[0]}`;
      }
      result.hasEnvironment = true;
      result.environmentInfo = process.platform === "win32" ? "Windows Command Prompt" : "Bash Shell";
    } catch (error) {
      console.error("Failed to detect Shell project:", error);
    }
  }
  /**
   * 检测Docker项目
   */
  async detectDockerProject(projectPath, result) {
    try {
      const files = await fs__namespace.promises.readdir(projectPath);
      if (files.includes("docker-compose.yml")) {
        result.startCommand = "docker-compose up";
      } else if (files.includes("Dockerfile")) {
        result.startCommand = "docker build -t app . && docker run app";
      }
      try {
        const { stdout } = await execAsync("docker --version");
        result.hasEnvironment = true;
        result.environmentInfo = stdout.trim();
      } catch (error) {
        result.hasEnvironment = false;
        result.environmentInfo = "Docker 未安装";
        result.suggestions.push("请安装 Docker");
      }
    } catch (error) {
      console.error("Failed to detect Docker project:", error);
    }
  }
  /**
   * 检测通用项目
   */
  async detectGenericProject(projectPath, result) {
    try {
      const files = await fs__namespace.promises.readdir(projectPath);
      const executableFiles = files.filter(
        (file) => file.endsWith(".exe") || file.endsWith(".jar") || file.includes("start") || file.includes("run")
      );
      if (executableFiles.length > 0) {
        result.startCommand = executableFiles[0];
      }
      result.hasEnvironment = true;
      result.environmentInfo = "通用项目";
    } catch (error) {
      console.error("Failed to detect generic project:", error);
    }
  }
}
class PseudoTerminalService extends events.EventEmitter {
  constructor() {
    super();
    this.terminals = /* @__PURE__ */ new Map();
    this.sessions = /* @__PURE__ */ new Map();
  }
  setTerminalLogService(terminalLogService) {
    this.terminalLogService = terminalLogService;
  }
  /**
   * 创建新的终端会话
   */
  createTerminal(id, cwd, title) {
    if (this.terminals.has(id)) {
      this.closeTerminal(id);
    }
    let workingDirectory = cwd || os__namespace.homedir();
    try {
      const fs2 = require("fs");
      if (!fs2.existsSync(workingDirectory)) {
        console.warn(`Working directory ${workingDirectory} does not exist, using home directory`);
        workingDirectory = os__namespace.homedir();
      }
    } catch (error) {
      console.warn(`Error checking working directory, using home directory:`, error);
      workingDirectory = os__namespace.homedir();
    }
    const shell = this.getDefaultShell();
    const spawnOptions = {
      name: "xterm-color",
      cols: 80,
      rows: 24,
      cwd: workingDirectory,
      env: {
        ...process.env,
        TERM: "xterm-256color",
        COLORTERM: "truecolor",
        // Windows cmd.exe 历史记录相关环境变量
        ...process.platform === "win32" && {
          DOSKEY: "1",
          PROMPT: "$P$G"
        }
      }
    };
    if (process.platform !== "win32") {
      spawnOptions.encoding = "utf8";
    }
    console.log(`Spawning terminal with shell: ${shell}, cwd: ${workingDirectory}`);
    let shellArgs = [];
    if (process.platform === "win32") {
      if (shell === "cmd.exe") {
        shellArgs = ["/k"];
      } else if (shell.includes("powershell")) {
        shellArgs = [
          "-NoExit",
          "-NoLogo",
          "-ExecutionPolicy",
          "Bypass",
          "-Command",
          "Set-PSReadLineOption -HistorySearchCursorMovesToEnd; Set-PSReadLineKeyHandler -Key UpArrow -Function HistorySearchBackward; Set-PSReadLineKeyHandler -Key DownArrow -Function HistorySearchForward"
        ];
      }
    }
    const terminal = pty__namespace.spawn(shell, shellArgs, spawnOptions);
    const session = {
      id,
      pid: terminal.pid,
      cwd: workingDirectory,
      title: title || `Terminal - ${path__namespace.basename(workingDirectory)}`,
      createdAt: /* @__PURE__ */ new Date(),
      isMinimized: false
      // 默认不是最小化状态
    };
    console.log(`Terminal spawned successfully: PID ${terminal.pid}, Shell: ${shell}`);
    this.terminals.set(id, terminal);
    this.sessions.set(id, session);
    terminal.onData((data) => {
      try {
        if (this.terminalLogService) {
          this.terminalLogService.addLog(id, "output", data, "info");
        }
        this.emit("terminal-output", id, data);
      } catch (error) {
        console.error(`Terminal output error for ${id}:`, error);
      }
    });
    terminal.onExit((event) => {
      try {
        const code = typeof event === "number" ? event : (event == null ? void 0 : event.exitCode) || 0;
        const signal = typeof event === "object" ? event == null ? void 0 : event.signal : void 0;
        console.log(`Terminal ${id} exited with code: ${code}, signal: ${signal}`);
        this.emit("terminal-exit", id, code);
        this.cleanup(id);
      } catch (error) {
        console.error(`Terminal exit error for ${id}:`, error);
      }
    });
    try {
      const terminalAny = terminal;
      if (terminalAny.process && terminalAny.process.on) {
        terminalAny.process.on("error", (error) => {
          console.error(`Terminal process error for ${id}:`, error.message);
          if (error.message.includes("EPIPE") || error.message.includes("ECONNRESET")) {
            console.log(`Pipe broken for terminal ${id}, but keeping session alive`);
            this.emit("terminal-error", id, error.message);
          }
        });
      }
    } catch (error) {
      console.warn(`Could not attach error handler to terminal process ${id}:`, error);
    }
    console.log(`Created terminal session: ${id} (PID: ${terminal.pid})`);
    return session;
  }
  /**
   * 向终端写入数据
   */
  writeToTerminal(id, data) {
    const terminal = this.terminals.get(id);
    if (terminal) {
      try {
        if (!this.sessions.has(id)) {
          console.warn(`Terminal ${id} session not found, ignoring write`);
          return false;
        }
        if (this.terminalLogService) {
          this.terminalLogService.addLog(id, "input", data, "info");
        }
        terminal.write(data);
        return true;
      } catch (error) {
        console.error(`Failed to write to terminal ${id}:`, error.message);
        if (error.message.includes("EPIPE") || error.message.includes("ECONNRESET")) {
          console.log(`Pipe broken while writing to terminal ${id}, cleaning up session`);
          this.emit("terminal-error", id, error.message);
          this.cleanup(id);
        }
        return false;
      }
    }
    console.warn(`Terminal ${id} not found for write operation`);
    return false;
  }
  /**
   * 调整终端大小
   */
  resizeTerminal(id, cols, rows) {
    const terminal = this.terminals.get(id);
    if (terminal) {
      terminal.resize(cols, rows);
      return true;
    }
    return false;
  }
  /**
   * 关闭终端
   */
  closeTerminal(id) {
    const terminal = this.terminals.get(id);
    if (terminal) {
      try {
        console.log(`Closing terminal session: ${id}`);
        try {
          terminal.write("");
          setTimeout(() => {
            try {
              if (this.terminals.has(id)) {
                terminal.kill("SIGTERM");
              }
            } catch (killError) {
              console.warn(`Error sending SIGTERM to terminal ${id}:`, killError);
            }
          }, 100);
        } catch (writeError) {
          console.warn(`Error writing to terminal ${id} before close:`, writeError);
          terminal.kill("SIGKILL");
        }
        this.cleanup(id);
        console.log(`Closed terminal session: ${id}`);
        return true;
      } catch (error) {
        console.error(`Error closing terminal ${id}:`, error.message);
        this.cleanup(id);
        return false;
      }
    }
    return false;
  }
  /**
   * 获取终端会话信息
   */
  getSession(id) {
    return this.sessions.get(id);
  }
  /**
   * 获取所有活动的终端会话
   */
  getAllSessions() {
    return Array.from(this.sessions.values());
  }
  /**
   * 检查终端是否存在
   */
  hasTerminal(id) {
    return this.terminals.has(id);
  }
  /**
   * 清理终端资源
   */
  cleanup(id) {
    this.terminals.delete(id);
    this.sessions.delete(id);
  }
  /**
   * 获取默认shell
   */
  getDefaultShell() {
    if (process.platform === "win32") {
      return "powershell.exe";
    } else {
      return process.env.SHELL || "/bin/bash";
    }
  }
  /**
   * 最小化终端（后台运行）
   */
  minimizeTerminal(id) {
    const session = this.sessions.get(id);
    if (session) {
      session.isMinimized = true;
      if (this.terminalLogService) {
        this.terminalLogService.addSystemLog(id, "终端已最小化到后台运行", "info");
      }
      console.log(`Terminal ${id} minimized to background`);
      return true;
    }
    return false;
  }
  /**
   * 恢复终端（从后台恢复）
   */
  restoreTerminal(id) {
    const session = this.sessions.get(id);
    if (session) {
      session.isMinimized = false;
      if (this.terminalLogService) {
        this.terminalLogService.addSystemLog(id, "终端已从后台恢复", "info");
      }
      console.log(`Terminal ${id} restored from background`);
      return true;
    }
    return false;
  }
  /**
   * 获取终端日志
   */
  async getTerminalLogs(id, limit = 500) {
    if (this.terminalLogService) {
      return await this.terminalLogService.getTerminalLogs(id, limit);
    }
    return [];
  }
  /**
   * 清空终端日志
   */
  async clearTerminalLogs(id) {
    if (this.terminalLogService) {
      await this.terminalLogService.clearTerminalLogs(id);
    }
  }
  /**
   * 清理所有终端
   */
  destroy() {
    for (const [id] of this.terminals) {
      this.closeTerminal(id);
    }
  }
}
class TerminalLogService extends events.EventEmitter {
  // 每个终端最多保留1000条日志
  constructor(databaseService) {
    super();
    this.terminalLogs = /* @__PURE__ */ new Map();
    this.maxLogsPerTerminal = 1e3;
    this.databaseService = databaseService;
  }
  /**
   * 初始化终端日志服务
   */
  async initialize() {
    try {
      const createTableQuery = `
        CREATE TABLE IF NOT EXISTS terminal_logs (
          id TEXT PRIMARY KEY,
          terminal_id TEXT NOT NULL,
          timestamp DATETIME NOT NULL,
          type TEXT NOT NULL,
          content TEXT NOT NULL,
          level TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `;
      await this.databaseService.run(createTableQuery);
      const createIndexQueries = [
        "CREATE INDEX IF NOT EXISTS idx_terminal_logs_terminal_id ON terminal_logs (terminal_id)",
        "CREATE INDEX IF NOT EXISTS idx_terminal_logs_timestamp ON terminal_logs (timestamp)",
        "CREATE INDEX IF NOT EXISTS idx_terminal_logs_created_at ON terminal_logs (created_at)"
      ];
      for (const query of createIndexQueries) {
        await this.databaseService.run(query);
      }
      console.log("TerminalLogService initialized successfully");
    } catch (error) {
      console.error("Failed to initialize TerminalLogService:", error);
      throw error;
    }
  }
  /**
   * 添加日志条目
   */
  async addLog(terminalId, type, content, level = "info") {
    const logEntry = {
      id: `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      terminalId,
      timestamp: /* @__PURE__ */ new Date(),
      type,
      content: content.trim(),
      level
    };
    if (!this.terminalLogs.has(terminalId)) {
      this.terminalLogs.set(terminalId, []);
    }
    const logs = this.terminalLogs.get(terminalId);
    logs.push(logEntry);
    if (logs.length > this.maxLogsPerTerminal) {
      logs.splice(0, logs.length - this.maxLogsPerTerminal);
    }
    try {
      const query = `
        INSERT INTO terminal_logs (id, terminal_id, timestamp, type, content, level)
        VALUES (?, ?, ?, ?, ?, ?)
      `;
      await this.databaseService.run(query, [
        logEntry.id,
        logEntry.terminalId,
        logEntry.timestamp.toISOString(),
        logEntry.type,
        logEntry.content,
        logEntry.level
      ]);
    } catch (error) {
      console.error("Failed to save terminal log to database:", error);
    }
    this.emit("terminal-log-added", logEntry);
  }
  /**
   * 获取终端的日志历史
   */
  async getTerminalLogs(terminalId, limit = 500) {
    const cachedLogs = this.terminalLogs.get(terminalId);
    if (cachedLogs && cachedLogs.length > 0) {
      return cachedLogs.slice(-limit);
    }
    try {
      const query = `
        SELECT * FROM terminal_logs 
        WHERE terminal_id = ? 
        ORDER BY timestamp DESC 
        LIMIT ?
      `;
      const rows = await this.databaseService.all(query, [terminalId, limit]);
      const logs = rows.map((row) => ({
        id: row.id,
        terminalId: row.terminal_id,
        timestamp: new Date(row.timestamp),
        type: row.type,
        content: row.content,
        level: row.level
      })).reverse();
      this.terminalLogs.set(terminalId, logs);
      return logs;
    } catch (error) {
      console.error("Failed to get terminal logs from database:", error);
      return [];
    }
  }
  /**
   * 清空终端日志
   */
  async clearTerminalLogs(terminalId) {
    try {
      const query = "DELETE FROM terminal_logs WHERE terminal_id = ?";
      await this.databaseService.run(query, [terminalId]);
      this.terminalLogs.delete(terminalId);
      console.log(`Cleared logs for terminal ${terminalId}`);
    } catch (error) {
      console.error("Failed to clear terminal logs:", error);
      throw error;
    }
  }
  /**
   * 获取内存中的实时日志
   */
  getRealtimeLogs(terminalId) {
    return this.terminalLogs.get(terminalId) || [];
  }
  /**
   * 添加系统日志
   */
  async addSystemLog(terminalId, message, level = "info") {
    await this.addLog(terminalId, "system", message, level);
  }
  /**
   * 清理旧日志（保留最近的日志）
   */
  async cleanupOldLogs(terminalId, keepCount = 1e3) {
    try {
      const query = `
        DELETE FROM terminal_logs 
        WHERE terminal_id = ? 
        AND id NOT IN (
          SELECT id FROM terminal_logs 
          WHERE terminal_id = ? 
          ORDER BY timestamp DESC 
          LIMIT ?
        )
      `;
      await this.databaseService.run(query, [terminalId, terminalId, keepCount]);
      console.log(`Cleaned up old logs for terminal ${terminalId}, kept ${keepCount} recent logs`);
    } catch (error) {
      console.error("Failed to cleanup old terminal logs:", error);
    }
  }
}
process.on("uncaughtException", (error) => {
  console.error("Uncaught Exception:", error.message);
  if (error.message.includes("EPIPE") || error.message.includes("ECONNRESET")) {
    console.log("Pipe error caught, continuing execution...");
  } else {
    console.error("Critical error, stack trace:", error.stack);
  }
});
process.on("unhandledRejection", (reason, promise) => {
  console.error("Unhandled Rejection at:", promise, "reason:", reason);
  if (reason && reason.message && (reason.message.includes("EPIPE") || reason.message.includes("ECONNRESET"))) {
    console.log("Pipe rejection caught, continuing execution...");
  }
});
class MainProcess {
  constructor() {
    this.mainWindow = null;
    this.databaseService = new DatabaseService();
    this.fileSystemService = new FileSystemService();
    this.gitService = new GitService();
    this.processService = new ProcessService();
    this.dependencyService = new DependencyService();
    this.virtualEnvironmentService = new VirtualEnvironmentService();
    this.configurationService = new ConfigurationService();
    this.repositoryService = new RepositoryService();
    this.executionHistoryService = new ExecutionHistoryService(this.databaseService);
    this.projectLogService = new ProjectLogService(this.databaseService);
    this.gitConfigService = new GitConfigService(this.databaseService);
    this.projectAnalysisService = new ProjectAnalysisService();
    this.walletConfigService = new WalletConfigService();
    this.globalWalletService = new GlobalWalletService();
    this.proxyConfigService = new ProxyConfigService();
    this.proxyManagerService = new ProxyManagerService();
    this.performanceMonitorService = new PerformanceMonitorService();
    this.gitHubService = new GitHubService();
    this.projectDetectionService = new ProjectDetectionService();
    this.pseudoTerminalService = new PseudoTerminalService();
    this.terminalLogService = new TerminalLogService(this.databaseService);
    this.projectService = new ProjectService(
      this.databaseService,
      this.fileSystemService,
      this.gitService,
      this.processService
    );
    this.gitRepositoryService = new GitRepositoryService(this.gitConfigService, this.projectService);
    this.batchOperationService = new BatchOperationService(
      this.projectService,
      this.gitService,
      this.processService,
      this.dependencyService
    );
  }
  async initialize() {
    try {
      console.log("Initializing MainProcess...");
      console.log("Initializing database...");
      await this.databaseService.initialize();
      console.log("Initializing execution history service...");
      await this.executionHistoryService.initialize();
      console.log("Initializing project log service...");
      await this.projectLogService.initialize();
      console.log("Initializing git config service...");
      await this.gitConfigService.initialize();
      console.log("Initializing terminal log service...");
      await this.terminalLogService.initialize();
      console.log("Setting up service references...");
      this.projectService.setProjectLogService(this.projectLogService);
      this.projectService.setExecutionHistoryService(this.executionHistoryService);
      this.pseudoTerminalService.setTerminalLogService(this.terminalLogService);
      console.log("Loading projects...");
      await this.projectService.loadProjects();
      await this.projectService.migratePythonProjectConfigs();
      console.log("Setting up IPC handlers...");
      this.setupIpcHandlers();
      console.log("Setting up process event forwarding...");
      this.setupProcessEventForwarding();
      console.log("Setting up performance monitoring...");
      this.setupPerformanceMonitoring();
      this.setupPerformanceMonitoringEvents();
      console.log("Setting up batch operation event forwarding...");
      this.setupBatchOperationEventForwarding();
      console.log("Setting up project log event forwarding...");
      this.setupProjectLogEventForwarding();
      console.log("Setting up pseudo terminal event forwarding...");
      this.setupPseudoTerminalEventForwarding();
      console.log("Setting up terminal log event forwarding...");
      this.setupTerminalLogEventForwarding();
      console.log("Creating main window...");
      this.createWindow();
      console.log("MainProcess initialized successfully");
    } catch (error) {
      console.error("Failed to initialize MainProcess:", error);
      throw error;
    }
  }
  createWindow() {
    const isWindows = process.platform === "win32";
    const isDev = process.env.NODE_ENV === "development";
    this.mainWindow = new electron.BrowserWindow({
      width: 1400,
      height: 900,
      minWidth: 1200,
      minHeight: 700,
      frame: false,
      // 去掉原生边框
      titleBarStyle: "hidden",
      // 隐藏标题栏
      transparent: true,
      // 启用透明背景
      backgroundColor: "#00000000",
      // 完全透明背景
      hasShadow: false,
      // 禁用窗口阴影
      ...process.platform === "darwin" && {
        vibrancy: "under-window",
        visualEffectState: "active"
      },
      // macOS毛玻璃效果
      ...isWindows && {
        backgroundMaterial: "none"
        // 禁用Windows背景材质
      },
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path__namespace.join(__dirname, "../preload/preload.js"),
        devTools: isDev,
        // 开发模式下启用开发者工具
        experimentalFeatures: true
        // 启用实验性功能
      },
      show: false
    });
    if (isDev) {
      const devServerUrl = process.env.VITE_DEV_SERVER_URL || "http://localhost:3000";
      this.mainWindow.loadURL(devServerUrl);
      this.mainWindow.webContents.openDevTools();
      console.log("🔥 Vite hot reload enabled");
    } else {
      this.mainWindow.loadFile(path__namespace.join(__dirname, "../renderer/index.html"));
    }
    this.mainWindow.once("ready-to-show", () => {
      var _a;
      (_a = this.mainWindow) == null ? void 0 : _a.show();
    });
    this.mainWindow.on("closed", () => {
      this.mainWindow = null;
    });
    this.mainWindow.on("close", () => {
      if (process.env.NODE_ENV === "development") {
        this.cleanup();
      }
    });
  }
  setupIpcHandlers() {
    electron.ipcMain.handle("project:list", () => this.projectService.listProjects());
    electron.ipcMain.handle("project:add", (_, project) => this.projectService.addProject(project));
    electron.ipcMain.handle("project:update", (_, id, updates) => this.projectService.updateProject(id, updates));
    electron.ipcMain.handle("project:delete", (_, id, deleteFiles) => this.projectService.deleteProject(id, deleteFiles));
    electron.ipcMain.handle("project:start", (_, id, profileId) => this.projectService.startProject(id, profileId));
    electron.ipcMain.handle("project:stop", (_, id) => this.projectService.stopProject(id));
    electron.ipcMain.handle("project:restart", (_, id) => this.projectService.restartProject(id));
    electron.ipcMain.handle("project:refresh-status", (_, projectId) => this.projectService.refreshProjectStatus(projectId));
    electron.ipcMain.handle("project:refresh-all-status", () => this.projectService.refreshAllProjectsStatus());
    electron.ipcMain.handle("project:scan", (_, directories) => this.projectService.scanProjects(directories));
    electron.ipcMain.handle("project:scan-with-progress", (_, directories) => this.projectService.scanProjectsWithProgress(directories));
    electron.ipcMain.handle("project:detect-type", (_, path2) => this.projectService.detectProjectType(path2));
    electron.ipcMain.handle("project:detect-project", (_, path2) => this.projectDetectionService.detectProject(path2));
    electron.ipcMain.handle("terminal:create", (_, id, cwd, title) => this.pseudoTerminalService.createTerminal(id, cwd, title));
    electron.ipcMain.handle("terminal:write", (_, id, data) => this.pseudoTerminalService.writeToTerminal(id, data));
    electron.ipcMain.handle("terminal:resize", (_, id, cols, rows) => this.pseudoTerminalService.resizeTerminal(id, cols, rows));
    electron.ipcMain.handle("terminal:close", (_, id) => this.pseudoTerminalService.closeTerminal(id));
    electron.ipcMain.handle("terminal:minimize", (_, id) => this.pseudoTerminalService.minimizeTerminal(id));
    electron.ipcMain.handle("terminal:restore", (_, id) => this.pseudoTerminalService.restoreTerminal(id));
    electron.ipcMain.handle("terminal:get-session", (_, id) => this.pseudoTerminalService.getSession(id));
    electron.ipcMain.handle("terminal:get-all-sessions", () => this.pseudoTerminalService.getAllSessions());
    electron.ipcMain.handle("terminal:get-logs", (_, id, limit) => this.pseudoTerminalService.getTerminalLogs(id, limit));
    electron.ipcMain.handle("terminal:clear-logs", (_, id) => this.pseudoTerminalService.clearTerminalLogs(id));
    electron.ipcMain.handle("process:send-input", (_, pid, input) => this.processService.sendInput(pid, input));
    electron.ipcMain.handle("process:kill", (_, pid) => this.processService.killProcess(pid));
    electron.ipcMain.handle("process:get-running", () => this.processService.getRunningProcesses());
    electron.ipcMain.handle("fs:select-directory", () => this.fileSystemService.selectDirectory());
    electron.ipcMain.handle("fs:open-directory", (_, path2) => this.fileSystemService.openDirectory(path2));
    electron.ipcMain.handle("fs:open-terminal", (_, path2) => this.fileSystemService.openTerminal(path2));
    electron.ipcMain.handle("git:status", (_, path2) => this.gitService.getStatus(path2));
    electron.ipcMain.handle("git:pull", (_, path2) => this.gitService.pull(path2));
    electron.ipcMain.handle("git:clone", (_, url, targetPath) => this.gitService.clone(url, targetPath));
    electron.ipcMain.handle("system:info", () => this.getSystemInfo());
    electron.ipcMain.handle("system:resources", (_, pid) => this.processService.getProcessResources(pid));
    electron.ipcMain.handle("deps:check", (_, projectPath, projectType) => this.dependencyService.checkDependencies(projectPath, projectType));
    electron.ipcMain.handle("deps:install", (_, projectPath, projectType, dependencyName, version) => this.dependencyService.installDependency(projectPath, projectType, dependencyName, version));
    electron.ipcMain.handle("deps:install-all", (_, projectPath, projectType) => this.dependencyService.installAllDependencies(projectPath, projectType));
    electron.ipcMain.handle("deps:uninstall", (_, projectPath, projectType, dependencyName) => this.dependencyService.uninstallDependency(projectPath, projectType, dependencyName));
    electron.ipcMain.handle("deps:clean-all", (_, projectPath, projectType) => this.dependencyService.cleanAllDependencies(projectPath, projectType));
    electron.ipcMain.handle("venv:check-python", () => this.virtualEnvironmentService.checkPythonEnvironment());
    electron.ipcMain.handle("venv:list", (_, projectPath) => this.virtualEnvironmentService.listVirtualEnvironments(projectPath));
    electron.ipcMain.handle("venv:create", (_, projectPath, envName, pythonVersion) => this.virtualEnvironmentService.createVirtualEnvironment(projectPath, envName, pythonVersion));
    electron.ipcMain.handle("venv:activate", (_, projectPath, envName) => this.virtualEnvironmentService.activateVirtualEnvironment(projectPath, envName));
    electron.ipcMain.handle("venv:delete", (_, projectPath, envName) => this.virtualEnvironmentService.deleteVirtualEnvironment(projectPath, envName));
    electron.ipcMain.handle("venv:install-package", (_, projectPath, envName, packageName, version) => this.virtualEnvironmentService.installPackageInVenv(projectPath, envName, packageName, version));
    electron.ipcMain.handle("venv:get-packages", (_, projectPath, envName) => this.virtualEnvironmentService.getInstalledPackages(projectPath, envName));
    electron.ipcMain.handle("config:get", (_, projectPath) => this.configurationService.getProjectConfiguration(projectPath));
    electron.ipcMain.handle("config:save", (_, projectPath, configuration) => this.configurationService.saveProjectConfiguration(projectPath, configuration));
    electron.ipcMain.handle("config:create-profile", (_, projectPath, profile) => this.configurationService.createProfile(projectPath, profile));
    electron.ipcMain.handle("config:update-profile", (_, projectPath, profileId, updates) => this.configurationService.updateProfile(projectPath, profileId, updates));
    electron.ipcMain.handle("config:delete-profile", (_, projectPath, profileId) => this.configurationService.deleteProfile(projectPath, profileId));
    electron.ipcMain.handle("config:get-default-profile", (_, projectPath) => this.configurationService.getDefaultProfile(projectPath));
    electron.ipcMain.handle("config:duplicate-profile", (_, projectPath, profileId, newName) => this.configurationService.duplicateProfile(projectPath, profileId, newName));
    electron.ipcMain.handle("config:export", (_, projectPath) => this.configurationService.exportConfiguration(projectPath));
    electron.ipcMain.handle("config:import", (_, projectPath, configJson) => this.configurationService.importConfiguration(projectPath, configJson));
    electron.ipcMain.handle("repo:get-user-repos", (_, platform, username, token) => this.repositoryService.getUserRepositories(platform, username, token));
    electron.ipcMain.handle("repo:pull-repos", (_, repositories, targetDirectory, onProgress) => this.repositoryService.pullRepositories(repositories, targetDirectory, onProgress));
    electron.ipcMain.handle("repo:clone", (_, repository, targetDirectory) => this.repositoryService.cloneRepository(repository, targetDirectory));
    electron.ipcMain.handle("repo:filter", (_, repositories, filters) => this.repositoryService.filterRepositories(repositories, filters));
    electron.ipcMain.handle("repo:validate-git", () => this.repositoryService.validateGitAvailability());
    electron.ipcMain.handle("repo:validate-network", (_, platform) => this.repositoryService.validateNetworkConnection(platform));
    electron.ipcMain.handle("repo:get-details", (_, platform, fullName, token) => this.repositoryService.getRepositoryDetails(platform, fullName, token));
    electron.ipcMain.handle("history:get", (_, filter) => this.executionHistoryService.getExecutionHistory(filter));
    electron.ipcMain.handle("history:get-record", (_, executionId) => this.executionHistoryService.getExecutionRecord(executionId));
    electron.ipcMain.handle("history:get-stats", (_, projectId) => this.executionHistoryService.getExecutionStats(projectId));
    electron.ipcMain.handle("history:delete", (_, executionId) => this.executionHistoryService.deleteExecutionRecord(executionId));
    electron.ipcMain.handle("history:delete-project", (_, projectId) => this.executionHistoryService.deleteProjectExecutionHistory(projectId));
    electron.ipcMain.handle("history:cleanup", (_, daysToKeep) => this.executionHistoryService.cleanupOldRecords(daysToKeep));
    electron.ipcMain.handle("history:get-running", () => this.executionHistoryService.getRunningExecutions());
    electron.ipcMain.handle("logs:get", (_, projectId, limit) => this.projectLogService.getProjectLogs(projectId, limit));
    electron.ipcMain.handle("logs:clear", (_, projectId) => this.projectLogService.clearProjectLogs(projectId));
    electron.ipcMain.handle("logs:cleanup", (_, projectId, keepCount) => this.projectLogService.cleanupOldLogs(projectId, keepCount));
    electron.ipcMain.handle("git-config:add", (_, config) => this.gitConfigService.addGitConfig(config));
    electron.ipcMain.handle("git-config:update", (_, id, updates) => this.gitConfigService.updateGitConfig(id, updates));
    electron.ipcMain.handle("git-config:get-all", (_, filter) => this.gitConfigService.getGitConfigs(filter));
    electron.ipcMain.handle("git-config:get", (_, id) => this.gitConfigService.getGitConfig(id));
    electron.ipcMain.handle("git-config:get-default", () => this.gitConfigService.getDefaultGitConfig());
    electron.ipcMain.handle("git-config:delete", (_, id) => this.gitConfigService.deleteGitConfig(id));
    electron.ipcMain.handle("git-config:set-default", (_, id) => this.gitConfigService.setDefaultGitConfig(id));
    electron.ipcMain.handle("git-config:save-username-history", (_, username, provider) => this.gitConfigService.saveUsernameHistory(username, provider));
    electron.ipcMain.handle("git-config:get-username-history", (_, provider, limit) => this.gitConfigService.getUsernameHistory(provider, limit));
    electron.ipcMain.handle("git-config:cleanup-username-history", (_, keepCount) => this.gitConfigService.cleanupUsernameHistory(keepCount));
    electron.ipcMain.handle("git-config:save-token-history", (_, provider, token, tokenName) => this.gitConfigService.saveTokenHistory(provider, token, tokenName));
    electron.ipcMain.handle("git-config:get-token-history", (_, provider, limit) => this.gitConfigService.getTokenHistory(provider, limit));
    electron.ipcMain.handle("git-config:delete-token-history", (_, provider, token) => this.gitConfigService.deleteTokenHistory(provider, token));
    electron.ipcMain.handle("git-config:cleanup-token-history", (_, keepCount) => this.gitConfigService.cleanupTokenHistory(keepCount));
    electron.ipcMain.handle("git-config:save-directory-history", (_, directoryPath) => this.gitConfigService.saveDirectoryHistory(directoryPath));
    electron.ipcMain.handle("git-config:get-directory-history", (_, limit) => this.gitConfigService.getDirectoryHistory(limit));
    electron.ipcMain.handle("git-config:get-last-used-directory", () => this.gitConfigService.getLastUsedDirectory());
    electron.ipcMain.handle("git-config:cleanup-directory-history", (_, keepCount) => this.gitConfigService.cleanupDirectoryHistory(keepCount));
    electron.ipcMain.handle("github:get-user-repositories", (_, username, token) => this.gitHubService.getUserRepositories(username, token));
    electron.ipcMain.handle("github:get-gitee-repositories", (_, username, token) => this.gitHubService.getGiteeUserRepositories(username, token));
    electron.ipcMain.handle("github:get-repositories-by-platform", (_, platform, username, token) => this.gitHubService.getRepositoriesByPlatform(platform, username, token));
    electron.ipcMain.handle("github:get-repositories-by-token", (_, platform, token) => this.gitHubService.getRepositoriesByToken(platform, token));
    electron.ipcMain.handle("github:clone-repository", (_, repository, targetPath, useSSH) => this.gitHubService.cloneRepository(repository, targetPath, useSSH));
    electron.ipcMain.handle("git-repo:get-repositories", (_, configId, filter) => this.gitRepositoryService.getRepositories(configId, filter));
    electron.ipcMain.handle("git-repo:pull-projects", async (_, request) => {
      const results = [];
      await this.gitRepositoryService.pullProjects(request, (progress) => {
        var _a;
        (_a = this.mainWindow) == null ? void 0 : _a.webContents.send("git-pull:progress", progress);
      });
      return results;
    });
    electron.ipcMain.handle("analysis:files", (_, projectPath) => this.projectAnalysisService.analyzeProjectFiles(projectPath));
    electron.ipcMain.handle("analysis:environment", (_, projectPath) => this.projectAnalysisService.getProjectEnvironment(projectPath));
    electron.ipcMain.handle("analysis:git", (_, projectPath) => this.projectAnalysisService.getGitInfo(projectPath));
    electron.ipcMain.handle("analysis:detect-type", (_, projectPath) => this.projectAnalysisService.detectProjectType(projectPath));
    electron.ipcMain.handle("wallet:get-config", (_, projectPath) => this.walletConfigService.getProjectWalletConfig(projectPath));
    electron.ipcMain.handle("wallet:save-config", (_, projectPath, config) => this.walletConfigService.saveProjectWalletConfig(projectPath, config));
    electron.ipcMain.handle("wallet:add", (_, projectPath, wallet) => this.walletConfigService.addWallet(projectPath, wallet));
    electron.ipcMain.handle("wallet:update", (_, projectPath, walletId, updates) => this.walletConfigService.updateWallet(projectPath, walletId, updates));
    electron.ipcMain.handle("wallet:delete", (_, projectPath, walletId) => this.walletConfigService.deleteWallet(projectPath, walletId));
    electron.ipcMain.handle("wallet:get-default", (_, projectPath) => this.walletConfigService.getDefaultWallet(projectPath));
    electron.ipcMain.handle("wallet:validate", (_, wallet) => this.walletConfigService.validateWalletConnection(wallet));
    electron.ipcMain.handle("global-wallet:get-config", () => this.globalWalletService.getGlobalWalletConfig());
    electron.ipcMain.handle("global-wallet:save-config", (_, config) => this.globalWalletService.saveGlobalWalletConfig(config));
    electron.ipcMain.handle("global-wallet:add", (_, wallet) => this.globalWalletService.addWallet(wallet));
    electron.ipcMain.handle("global-wallet:add-batch", (_, wallets) => this.globalWalletService.addWallets(wallets));
    electron.ipcMain.handle("global-wallet:update", (_, walletId, updates) => this.globalWalletService.updateWallet(walletId, updates));
    electron.ipcMain.handle("global-wallet:delete", (_, walletId) => this.globalWalletService.deleteWallet(walletId));
    electron.ipcMain.handle("global-wallet:delete-batch", (_, walletIds) => this.globalWalletService.deleteWallets(walletIds));
    electron.ipcMain.handle("global-wallet:import-from-file", (_, filePath, chain) => this.globalWalletService.importWalletsFromFile(filePath, chain));
    electron.ipcMain.handle("global-wallet:create-batch", (_, count, chain) => this.globalWalletService.createWallets(count, chain));
    electron.ipcMain.handle("global-wallet:get-address-from-mnemonic", (_, mnemonic, chain, derivationPath) => this.globalWalletService.getAddressFromMnemonic(mnemonic, chain, derivationPath));
    electron.ipcMain.handle("proxy:get-config", (_, projectPath) => this.proxyConfigService.getProjectProxyConfig(projectPath));
    electron.ipcMain.handle("proxy:save-config", (_, projectPath, config) => this.proxyConfigService.saveProjectProxyConfig(projectPath, config));
    electron.ipcMain.handle("proxy:add", (_, projectPath, proxy) => this.proxyConfigService.addProxy(projectPath, proxy));
    electron.ipcMain.handle("proxy:update", (_, projectPath, proxyId, updates) => this.proxyConfigService.updateProxy(projectPath, proxyId, updates));
    electron.ipcMain.handle("proxy:delete", (_, projectPath, proxyId) => this.proxyConfigService.deleteProxy(projectPath, proxyId));
    electron.ipcMain.handle("proxy:get-default", (_, projectPath) => this.proxyConfigService.getDefaultProxy(projectPath));
    electron.ipcMain.handle("proxy:test", (_, proxy) => this.proxyConfigService.testProxyConnection(proxy));
    electron.ipcMain.handle("proxy:get-system", () => this.proxyConfigService.getSystemProxySettings());
    electron.ipcMain.handle("proxy-manager:get-config", () => this.proxyManagerService.getProxyConfig());
    electron.ipcMain.handle("proxy-manager:save-config", (_, config) => this.proxyManagerService.saveProxyConfig(config));
    electron.ipcMain.handle("proxy-manager:fetch-all", () => this.proxyManagerService.fetchProxiesFromAllSources());
    electron.ipcMain.handle("proxy-manager:test-proxy", (_, proxy, testUrl, timeout) => this.proxyManagerService.testProxy(proxy, testUrl, timeout));
    electron.ipcMain.handle("proxy-manager:test-batch", async (event, proxies, maxConcurrent) => {
      const progressCallback = (progress) => {
        event.sender.send("proxy-test-progress", progress);
      };
      return this.proxyManagerService.testProxiesBatch(proxies, maxConcurrent, progressCallback);
    });
    electron.ipcMain.handle("proxy-manager:delete", (_, proxyIds) => this.proxyManagerService.deleteProxies(proxyIds));
    electron.ipcMain.handle("performance:start", () => this.performanceMonitorService.startMonitoring());
    electron.ipcMain.handle("performance:stop", () => this.performanceMonitorService.stopMonitoring());
    electron.ipcMain.handle("performance:get-system", () => this.performanceMonitorService.getSystemMetrics());
    electron.ipcMain.handle("performance:get-process", async (_, pid) => {
      try {
        return await this.performanceMonitorService.getProcessMetrics(pid);
      } catch (error) {
        return null;
      }
    });
    electron.ipcMain.handle("performance:get-history", (_, limit) => this.performanceMonitorService.getMetricsHistory(limit));
    electron.ipcMain.handle("performance:get-process-history", (_, pid, limit) => this.performanceMonitorService.getProcessMetricsHistory(pid, limit));
    electron.ipcMain.handle("performance:get-alerts", (_, limit) => this.performanceMonitorService.getAlerts(limit));
    electron.ipcMain.handle("performance:clear-alerts", () => this.performanceMonitorService.clearAlerts());
    electron.ipcMain.handle("performance:set-thresholds", (_, thresholds) => this.performanceMonitorService.setThresholds(thresholds));
    electron.ipcMain.handle("performance:get-thresholds", () => this.performanceMonitorService.getThresholds());
    electron.ipcMain.handle("performance:start-process", (_, pid, projectId) => this.performanceMonitorService.startProcessMonitoring(pid, projectId));
    electron.ipcMain.handle("performance:stop-process", (_, pid) => this.performanceMonitorService.stopProcessMonitoring(pid));
    electron.ipcMain.handle("batch:execute", (_, type, projectIds, options) => this.batchOperationService.executeBatchOperation(type, projectIds, options));
    electron.ipcMain.handle("batch:cancel", (_, operationId) => this.batchOperationService.cancelBatchOperation(operationId));
    electron.ipcMain.handle("batch:get", (_, operationId) => this.batchOperationService.getBatchOperation(operationId));
    electron.ipcMain.handle("batch:get-all", () => this.batchOperationService.getAllBatchOperations());
    electron.ipcMain.handle("batch:cleanup", () => this.batchOperationService.cleanupCompletedOperations());
    electron.ipcMain.handle("batch:get-stats", () => this.batchOperationService.getOperationStats());
    electron.ipcMain.handle("batch:estimate-time", (_, type, projectCount) => this.batchOperationService.estimateOperationTime(type, projectCount));
    electron.ipcMain.handle("batch:validate", (_, type, projectIds) => this.batchOperationService.validateBatchOperation(type, projectIds));
    electron.ipcMain.handle("window:minimize", () => {
      if (this.mainWindow) {
        this.mainWindow.minimize();
      }
    });
    electron.ipcMain.handle("window:maximize", () => {
      if (this.mainWindow) {
        if (this.mainWindow.isMaximized()) {
          this.mainWindow.unmaximize();
        } else {
          this.mainWindow.maximize();
        }
      }
    });
    electron.ipcMain.handle("window:unmaximize", () => {
      if (this.mainWindow) {
        this.mainWindow.unmaximize();
      }
    });
    electron.ipcMain.handle("window:close", () => {
      if (this.mainWindow) {
        this.mainWindow.close();
      }
    });
    electron.ipcMain.handle("window:is-maximized", () => {
      return this.mainWindow ? this.mainWindow.isMaximized() : false;
    });
    electron.ipcMain.handle("dialog:showOpenDialog", async (_, options) => {
      if (this.mainWindow) {
        return await electron.dialog.showOpenDialog(this.mainWindow, options);
      }
      return { canceled: true, filePaths: [] };
    });
    electron.ipcMain.handle("dialog:showSaveDialog", async (_, options) => {
      if (this.mainWindow) {
        return await electron.dialog.showSaveDialog(this.mainWindow, options);
      }
      return { canceled: true, filePath: "" };
    });
    electron.ipcMain.handle("dialog:showMessageBox", async (_, options) => {
      if (this.mainWindow) {
        return await electron.dialog.showMessageBox(this.mainWindow, options);
      }
      return { response: 0, checkboxChecked: false };
    });
    electron.ipcMain.handle("fs:create-temp-file", async (_, content) => {
      const os2 = require("os");
      const path2 = require("path");
      const fs2 = require("fs");
      const tempDir = os2.tmpdir();
      const tempFileName = `wallet_import_${Date.now()}.txt`;
      const tempFilePath = path2.join(tempDir, tempFileName);
      fs2.writeFileSync(tempFilePath, content, "utf-8");
      return tempFilePath;
    });
  }
  setupProcessEventForwarding() {
    this.processService.on("process-output", (pid, output, type) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send("process:output", pid, output, type);
      }
    });
    this.processService.on("process-resources", (pid, resources) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send("process:resources", pid, resources);
      }
    });
    this.processService.on("process-exit", (pid, code, signal) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send("process:exit", pid, code, signal);
      }
    });
    this.processService.on("process-error", (pid, error) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send("process:error", pid, error.message);
      }
    });
  }
  setupPerformanceMonitoring() {
    this.performanceMonitorService.on("system-metrics", (metrics) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send("performance:system-metrics", metrics);
      }
    });
    this.performanceMonitorService.on("process-metrics", (metrics) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send("performance:process-metrics", metrics);
      }
    });
    this.performanceMonitorService.on("performance-alert", (alert) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send("performance:alert", alert);
      }
    });
    this.performanceMonitorService.startMonitoring();
  }
  setupPerformanceMonitoringEvents() {
    electron.ipcMain.on("performance:start-process-monitoring", (_, pid, projectId) => {
      try {
        this.performanceMonitorService.startProcessMonitoring(pid, projectId);
      } catch (error) {
        console.error("Failed to start performance monitoring:", error);
      }
    });
    electron.ipcMain.on("performance:stop-process-monitoring", (_, pid) => {
      try {
        this.performanceMonitorService.stopProcessMonitoring(pid);
      } catch (error) {
        console.error("Failed to stop performance monitoring:", error);
      }
    });
    electron.ipcMain.on("performance:process-ended", (_, pid, projectId) => {
      try {
        this.projectService.handleProcessEnded(pid, projectId);
      } catch (error) {
        console.error("Failed to handle process ended:", error);
      }
    });
  }
  setupBatchOperationEventForwarding() {
    this.batchOperationService.on("operation-created", (operation) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send("batch:operation-created", operation);
      }
    });
    this.batchOperationService.on("operation-started", (operation) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send("batch:operation-started", operation);
      }
    });
    this.batchOperationService.on("operation-progress", (operation) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send("batch:operation-progress", operation);
      }
    });
    this.batchOperationService.on("operation-completed", (operation) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send("batch:operation-completed", operation);
      }
    });
  }
  setupProjectLogEventForwarding() {
    this.projectLogService.on("log-added", (logEntry) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send("project:log-added", logEntry);
      }
    });
    this.batchOperationService.on("operation-failed", (operation, error) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send("batch:operation-failed", operation, error);
      }
    });
    this.batchOperationService.on("operation-cancelled", (operation) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send("batch:operation-cancelled", operation);
      }
    });
  }
  setupPseudoTerminalEventForwarding() {
    this.pseudoTerminalService.on("terminal-output", (id, data) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send("terminal:output", { id, data });
      }
    });
    this.pseudoTerminalService.on("terminal-exit", (id, exitCode) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send("terminal:exit", { id, exitCode });
      }
    });
    this.pseudoTerminalService.on("terminal-error", (id, error) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send("terminal:error", { id, error });
      }
    });
  }
  getSystemInfo() {
    return {
      platform: process.platform,
      arch: process.arch,
      nodeVersion: process.version,
      electronVersion: process.versions.electron
    };
  }
  getMainWindow() {
    return this.mainWindow;
  }
  setupTerminalLogEventForwarding() {
    this.terminalLogService.on("terminal-log-added", (logEntry) => {
      if (this.mainWindow) {
        this.mainWindow.webContents.send("terminal:log-added", logEntry);
      }
    });
  }
  cleanup() {
    this.performanceMonitorService.stopMonitoring();
    this.processService.destroy();
    this.databaseService.close().catch(console.error);
  }
}
const mainProcess = new MainProcess();
electron.app.whenReady().then(() => {
  mainProcess.initialize();
});
electron.app.on("window-all-closed", () => {
  mainProcess.cleanup();
  electron.app.quit();
});
electron.app.on("activate", () => {
  if (electron.BrowserWindow.getAllWindows().length === 0) {
    mainProcess.initialize();
  }
});
electron.app.on("before-quit", () => {
  mainProcess.cleanup();
});
exports.mainProcess = mainProcess;
//# sourceMappingURL=main.js.map

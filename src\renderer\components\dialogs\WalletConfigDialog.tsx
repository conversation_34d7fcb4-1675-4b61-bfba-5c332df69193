import React, { useState, useEffect } from 'react';
import {
  IoClose,
  IoWallet,
  Io<PERSON>ey,
  IoCard,
  IoDocument,
  IoCopy,
  Io<PERSON>ye,
  IoEyeOff
} from 'react-icons/io5';
import { useNotification } from '../../contexts/NotificationContext';
import { Project } from '@shared/types/project';
import Modal from '../Modal';
import ConfigFileEditDialog from './ConfigFileEditDialog';
import './WalletConfigDialog.css';

interface GlobalWallet {
  id: string;
  name: string;
  chain: string;
  type: 'private_key' | 'mnemonic' | 'generated';
  address: string;
  privateKey?: string;
  mnemonic?: string;
  isEnabled: boolean;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

interface WalletConfigDialogProps {
  project: Project;
  onClose: () => void;
}

const WalletConfigDialog: React.FC<WalletConfigDialogProps> = ({ project, onClose }) => {
  const { showSuccess, showError, showWarning } = useNotification();
  const [globalWallets, setGlobalWallets] = useState<GlobalWallet[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showConfigEditor, setShowConfigEditor] = useState(false);
  const [visiblePrivateKeys, setVisiblePrivateKeys] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadGlobalWallets();
  }, []);

  const loadGlobalWallets = async () => {
    try {
      setIsLoading(true);
      const config = await window.electronAPI.globalWallet.getConfig();
      setGlobalWallets(config.wallets || []);
    } catch (error) {
      console.error('加载全局钱包失败:', error);
      showError('加载全局钱包失败', error instanceof Error ? error.message : '未知错误');
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenConfigEditor = () => {
    setShowConfigEditor(true);
  };

  const handleCopyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      showSuccess(`${type}已复制到剪贴板`);
    } catch (error) {
      showError('复制失败', error instanceof Error ? error.message : '未知错误');
    }
  };

  const togglePrivateKeyVisibility = (walletId: string) => {
    const newVisible = new Set(visiblePrivateKeys);
    if (newVisible.has(walletId)) {
      newVisible.delete(walletId);
    } else {
      newVisible.add(walletId);
    }
    setVisiblePrivateKeys(newVisible);
  };

  const maskPrivateKey = (privateKey: string) => {
    if (privateKey.length <= 8) return privateKey;
    return privateKey.substring(0, 4) + '...' + privateKey.substring(privateKey.length - 4);
  };

  const getChainColor = (chain: string) => {
    const colors: { [key: string]: string } = {
      'eth': '#627EEA',
      'sol': '#9945FF',
      'bnb': '#F3BA2F',
      'polygon': '#8247E5',
      'avalanche': '#E84142',
      'fantom': '#1969FF',
      'arbitrum': '#28A0F0',
      'optimism': '#FF0420'
    };
    return colors[chain.toLowerCase()] || '#666666';
  };



  return (
    <>
      <Modal onClose={onClose} className="wallet-config-dialog">
        <div className="dialog-header">
          <div className="header-left">
            <IoWallet className="dialog-icon" />
            <div>
              <h2 className="dialog-title">钱包配置</h2>
              <p className="dialog-subtitle">{project.name}</p>
            </div>
          </div>
          <button className="dialog-close" onClick={onClose}>
            <IoClose />
          </button>
        </div>

        <div className="dialog-body">
          <div className="wallet-config-content">
            <div className="config-header">
              <h3>钱包私钥列表</h3>
              <button
                className="btn btn-primary btn-sm"
                onClick={handleOpenConfigEditor}
                title="导入配置"
              >
                <IoDocument />
                导入配置
              </button>
            </div>

            {isLoading ? (
              <div className="loading-container">
                <div className="loading-spinner"></div>
                <p>正在加载钱包数据...</p>
              </div>
            ) : globalWallets.length === 0 ? (
              <div className="empty-container">
                <IoWallet className="empty-icon" />
                <h3>暂无钱包数据</h3>
                <p>请先在钱包管理中添加钱包</p>
              </div>
            ) : (
              <div className="wallet-table-container">
                <table className="wallet-table">
                  <thead>
                    <tr>
                      <th>钱包名称</th>
                      <th>链</th>
                      <th>地址</th>
                      <th>私钥</th>
                      <th>助记词</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {globalWallets.filter(wallet => wallet.isEnabled).map((wallet) => (
                      <tr key={wallet.id} className="wallet-row">
                        <td className="wallet-name-cell">
                          <div className="wallet-name-info">
                            <span className="wallet-name">{wallet.name}</span>
                            <div className="wallet-tags">
                              {wallet.tags.map((tag, index) => (
                                <span key={index} className="wallet-tag">{tag}</span>
                              ))}
                            </div>
                          </div>
                        </td>
                        <td className="wallet-chain-cell">
                          <span
                            className="chain-badge"
                            style={{ backgroundColor: getChainColor(wallet.chain) }}
                          >
                            {wallet.chain.toUpperCase()}
                          </span>
                        </td>
                        <td className="wallet-address-cell">
                          <div className="address-container">
                            <span className="address-text">{wallet.address}</span>
                            <button
                              className="btn-icon"
                              onClick={() => handleCopyToClipboard(wallet.address, '地址')}
                              title="复制地址"
                            >
                              <IoCopy />
                            </button>
                          </div>
                        </td>
                        <td className="wallet-private-key-cell">
                          {wallet.privateKey ? (
                            <div className="private-key-container">
                              <span className="private-key-text">
                                {visiblePrivateKeys.has(wallet.id)
                                  ? wallet.privateKey
                                  : maskPrivateKey(wallet.privateKey)
                                }
                              </span>
                              <div className="private-key-actions">
                                <button
                                  className="btn-icon"
                                  onClick={() => togglePrivateKeyVisibility(wallet.id)}
                                  title={visiblePrivateKeys.has(wallet.id) ? '隐藏私钥' : '显示私钥'}
                                >
                                  {visiblePrivateKeys.has(wallet.id) ? <IoEyeOff /> : <IoEye />}
                                </button>
                                <button
                                  className="btn-icon"
                                  onClick={() => handleCopyToClipboard(wallet.privateKey!, '私钥')}
                                  title="复制私钥"
                                >
                                  <IoCopy />
                                </button>
                              </div>
                            </div>
                          ) : (
                            <span className="no-data">-</span>
                          )}
                        </td>
                        <td className="wallet-mnemonic-cell">
                          {wallet.mnemonic ? (
                            <div className="mnemonic-container">
                              <span className="mnemonic-text">
                                {wallet.mnemonic.split(' ').slice(0, 3).join(' ')}...
                              </span>
                              <button
                                className="btn-icon"
                                onClick={() => handleCopyToClipboard(wallet.mnemonic!, '助记词')}
                                title="复制助记词"
                              >
                                <IoCopy />
                              </button>
                            </div>
                          ) : (
                            <span className="no-data">-</span>
                          )}
                        </td>
                        <td className="wallet-actions-cell">
                          <div className="action-buttons">
                            {wallet.privateKey && (
                              <button
                                className="btn btn-sm btn-outline"
                                onClick={() => handleCopyToClipboard(wallet.privateKey!, '私钥')}
                                title="复制私钥"
                              >
                                <IoKey />
                              </button>
                            )}
                            {wallet.mnemonic && (
                              <button
                                className="btn btn-sm btn-outline"
                                onClick={() => handleCopyToClipboard(wallet.mnemonic!, '助记词')}
                                title="复制助记词"
                              >
                                <IoCard />
                              </button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </Modal>

      {showConfigEditor && (
        <ConfigFileEditDialog
          project={project}
          onClose={() => setShowConfigEditor(false)}
        />
      )}
    </>
  );
};

export default WalletConfigDialog;

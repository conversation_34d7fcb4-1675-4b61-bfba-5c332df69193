.project-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md);
  background: linear-gradient(135deg, 
    var(--primary-color) 0%, 
    var(--secondary-color) 30%, 
    var(--bg-tertiary) 70%, 
    var(--bg-secondary) 100%);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  position: relative;
  z-index: 10;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.toolbar-left {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.toolbar-title {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  line-height: var(--line-height-tight);
}

.toolbar-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.toolbar-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.toolbar-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-xs);
  font-weight: 500;
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-fast);
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  min-height: 32px;
  line-height: 1.2;
}

.toolbar-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.toolbar-btn:active {
  transform: translateY(0);
}

.btn-icon {
  font-size: var(--font-size-sm);
  flex-shrink: 0;
  width: 14px;
  height: 14px;
}

/* 按钮特殊样式 */
.toolbar-btn.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--info-color));
  border: none;
  color: white;
}

.toolbar-btn.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-hover), #138496);
}

/* 扫描项目按钮 - 成功色 */
.toolbar-btn.btn-scan {
  border: 1px solid var(--success-color);
  background: linear-gradient(135deg, var(--success-color), #20c997);
  color: white;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}

.toolbar-btn.btn-scan:hover {
  background: linear-gradient(135deg, var(--success-hover), #1ea97c);
  border-color: var(--success-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
}

/* 拉取项目按钮 - 信息色 */
.toolbar-btn.btn-pull {
  border: 1px solid var(--info-color);
  background: linear-gradient(135deg, var(--info-color), #6f42c1);
  color: white;
  box-shadow: 0 2px 4px rgba(23, 162, 184, 0.2);
}

.toolbar-btn.btn-pull:hover {
  background: linear-gradient(135deg, var(--info-hover), #5a32a3);
  border-color: var(--info-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(23, 162, 184, 0.4);
}

/* 清空列表按钮 - 危险色 */
.toolbar-btn.btn-clear {
  border: 1px solid var(--warning-color);
  background: linear-gradient(135deg, var(--warning-color), #fd7e14);
  color: #212529;
  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.2);
  font-weight: 600;
}

.toolbar-btn.btn-clear:hover {
  background: linear-gradient(135deg, var(--danger-color), #e74c3c);
  border-color: var(--danger-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.4);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .project-toolbar {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  .toolbar-right {
    width: 100%;
    justify-content: flex-end;
  }

  .toolbar-actions {
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .project-toolbar {
    padding: var(--spacing-md);
  }

  .toolbar-title {
    font-size: var(--font-size-xl);
  }

  .toolbar-actions {
    width: 100%;
    justify-content: space-between;
  }

  .toolbar-btn {
    flex: 1;
    justify-content: center;
    min-width: 0;
    padding: var(--spacing-sm);
  }

  .toolbar-btn span {
    display: none;
  }

  .btn-icon {
    margin: 0;
  }
}

@media (max-width: 480px) {
  .toolbar-actions {
    grid-template-columns: repeat(2, 1fr);
    display: grid;
    gap: var(--spacing-sm);
    width: 100%;
  }

  .toolbar-btn {
    flex: none;
  }
}

/* 动画效果 */
.toolbar-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.toolbar-btn:hover::before {
  left: 100%;
}

/* 暗色主题适配 */
[data-theme="dark"] .project-toolbar {
  background: linear-gradient(135deg,
    rgba(0, 122, 204, 0.15) 0%,
    rgba(99, 102, 241, 0.1) 30%,
    var(--bg-secondary) 70%,
    var(--bg-primary) 100%);
  border-bottom-color: var(--gray-600);
}

[data-theme="dark"] .toolbar-subtitle {
  color: var(--gray-400);
}

[data-theme="dark"] .toolbar-btn.btn-outline {
  border-color: var(--gray-600);
  color: var(--gray-300);
}

[data-theme="dark"] .toolbar-btn.btn-outline:hover {
  background-color: var(--gray-700);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

[data-theme="dark"] .toolbar-btn.btn-ghost:hover {
  background-color: rgba(220, 53, 69, 0.15);
}

[data-theme="dark"] .toolbar-btn::before {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
}

[data-theme="dark"] .toolbar-divider {
  background: var(--gray-600);
}

[data-theme="dark"] .terminal-badge,
[data-theme="dark"] .batch-badge {
  border-color: var(--bg-primary);
}

/* 性能监控按钮 */
.toolbar-btn.btn-performance {
  border: 1px solid #17a2b8;
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
  box-shadow: 0 2px 4px rgba(23, 162, 184, 0.2);
}

.toolbar-btn.btn-performance:hover {
  background: linear-gradient(135deg, #138496, #117a8b);
  border-color: #138496;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(23, 162, 184, 0.4);
}

/* 钱包管理按钮 */
.toolbar-btn.btn-wallet {
  border: 1px solid #6f42c1;
  background: linear-gradient(135deg, #6f42c1, #5a32a3);
  color: white;
  box-shadow: 0 2px 4px rgba(111, 66, 193, 0.2);
}

.toolbar-btn.btn-wallet:hover {
  background: linear-gradient(135deg, #5a32a3, #4c2a85);
  border-color: #5a32a3;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(111, 66, 193, 0.4);
}

/* 代理管理按钮 */
.toolbar-btn.btn-proxy {
  border: 1px solid #20c997;
  background: linear-gradient(135deg, #20c997, #1ba085);
  color: white;
  box-shadow: 0 2px 4px rgba(32, 201, 151, 0.2);
}

.toolbar-btn.btn-proxy:hover {
  background: linear-gradient(135deg, #1ba085, #168f73);
  border-color: #1ba085;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(32, 201, 151, 0.4);
}

/* 批量操作按钮 */
.toolbar-btn.btn-batch {
  border: 1px solid #6f42c1;
  background: linear-gradient(135deg, #6f42c1, #5a32a3);
  color: white;
  box-shadow: 0 2px 4px rgba(111, 66, 193, 0.2);
  position: relative;
}

.toolbar-btn.btn-batch:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a32a3, #4c2a85);
  border-color: #5a32a3;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(111, 66, 193, 0.4);
}

.toolbar-btn.btn-batch:disabled {
  background: #6c757d;
  border-color: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
  box-shadow: none;
}

/* 设置按钮 */
.toolbar-btn.btn-settings {
  border: 1px solid #10b981;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
  transition: all var(--transition-fast);
}

.toolbar-btn.btn-settings:hover {
  background: linear-gradient(135deg, #059669, #047857);
  border-color: #059669;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.4);
}

.toolbar-btn.btn-settings .btn-icon {
  transition: transform var(--transition-fast);
}

.toolbar-btn.btn-settings:hover .btn-icon {
  transform: rotate(90deg);
}

/* 主题切换按钮 */
.toolbar-btn.btn-theme {
  border: 1px solid #6366f1;
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
  transition: all var(--transition-fast);
}

.toolbar-btn.btn-theme:hover {
  background: linear-gradient(135deg, #4f46e5, #4338ca);
  border-color: #4f46e5;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(99, 102, 241, 0.4);
}

.toolbar-btn.btn-theme .btn-icon {
  transition: transform var(--transition-fast);
}

.toolbar-btn.btn-theme:hover .btn-icon {
  transform: rotate(180deg);
}

/* 按钮容器 */
.terminal-button-container,
.batch-button-container {
  position: relative;
  display: inline-block;
}

/* 终端管理按钮 */
.toolbar-btn.btn-terminal {
  border: 1px solid #6c757d;
  background: linear-gradient(135deg, #6c757d, #5a6268);
  color: white;
  box-shadow: 0 2px 4px rgba(108, 117, 125, 0.2);
}

.toolbar-btn.btn-terminal.active {
  border: 1px solid var(--success-color);
  background: linear-gradient(135deg, var(--success-color), #20c997);
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}

.toolbar-btn.btn-terminal.minimized {
  border: 1px solid #ffc107;
  background: linear-gradient(135deg, #ffc107, #e0a800);
  color: #212529;
  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.2);
}

.toolbar-btn.btn-terminal:hover:not(.none) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(108, 117, 125, 0.4);
}

.toolbar-btn.btn-terminal.active:hover {
  background: linear-gradient(135deg, var(--success-hover), #1ea97c);
  border-color: var(--success-hover);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
}

.toolbar-btn.btn-terminal.minimized:hover {
  background: linear-gradient(135deg, #e0a800, #d39e00);
  border-color: #e0a800;
  box-shadow: 0 4px 8px rgba(255, 193, 7, 0.4);
}

/* 徽章通用样式 */
.terminal-badge,
.batch-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #dc3545;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 11px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  border: 2px solid white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  pointer-events: none;
}

/* 批量操作徽章 */
.batch-badge {
  background: var(--primary-color);
}

.terminal-badge.active {
  background: var(--success-color);
  animation: pulse-green 2s infinite;
}

.terminal-badge.minimized {
  background: #ffc107;
  color: #212529;
  animation: pulse-yellow 2s infinite;
}

@keyframes pulse-green {
  0%, 100% {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2), 0 0 0 0 rgba(40, 167, 69, 0.7);
  }
  50% {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2), 0 0 0 4px rgba(40, 167, 69, 0);
  }
}

@keyframes pulse-yellow {
  0%, 100% {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2), 0 0 0 0 rgba(255, 193, 7, 0.7);
  }
  50% {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2), 0 0 0 4px rgba(255, 193, 7, 0);
  }
}

/* 工具栏分隔线 */
.toolbar-divider {
  width: 1px;
  height: 24px;
  background: var(--border-color);
  margin: 0 8px;
  opacity: 0.5;
}

/* 徽章样式 */
.badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background: #dc3545;
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 10px;
  font-weight: 600;
  min-width: 18px;
  text-align: center;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

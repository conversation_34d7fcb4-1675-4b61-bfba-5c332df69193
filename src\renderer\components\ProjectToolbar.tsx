import React, { useState, useEffect } from 'react';
import { IoRefresh, IoAdd, IoDownload, IoTrash, IoSpeedometer, IoLayers, IoMoon, IoSunny, IoTerminal, IoWallet, IoGlobe, IoSettings } from 'react-icons/io5';
import GlobalSettingsDialog from './dialogs/GlobalSettingsDialog';
import './ProjectToolbar.css';

interface ProjectToolbarProps {
  onScanProjects: () => void;
  onAddProject: () => void;
  onPullProjects: () => void;
  onClearProjects: () => void;
  onShowPerformance: () => void;
  onShowBatchOperation: () => void;
  onShowTerminalManager: () => void;
  onShowGlobalWallet: () => void;
  onShowProxyManager: () => void;
  selectedProjectsCount: number;
}

const ProjectToolbar: React.FC<ProjectToolbarProps> = ({
  onScanProjects,
  onAddProject,
  onPullProjects,
  onClearProjects,
  onShowPerformance,
  onShowBatchOperation,
  onShowTerminalManager,
  onShowGlobalWallet,
  onShowProxyManager,
  selectedProjectsCount,
}) => {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [terminalCount, setTerminalCount] = useState(0);
  const [terminalStatus, setTerminalStatus] = useState<'none' | 'active' | 'minimized'>('none');
  const [showGlobalSettings, setShowGlobalSettings] = useState(false);

  // 初始化主题
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const shouldUseDark = savedTheme === 'dark' || (!savedTheme && prefersDark);

    setIsDarkMode(shouldUseDark);
    document.documentElement.setAttribute('data-theme', shouldUseDark ? 'dark' : 'light');
  }, []);

  // 监控终端状态
  useEffect(() => {
    const updateTerminalStatus = async () => {
      try {
        const sessions = await window.electronAPI.terminal.getAllSessions();
        const count = sessions?.length || 0;
        setTerminalCount(count);

        if (count === 0) {
          setTerminalStatus('none');
        } else {
          const hasMinimized = sessions?.some(s => s.isMinimized);
          const hasActive = sessions?.some(s => !s.isMinimized);

          if (hasActive && hasMinimized) {
            setTerminalStatus('active'); // 有活跃的优先显示活跃状态
          } else if (hasActive) {
            setTerminalStatus('active');
          } else {
            setTerminalStatus('minimized');
          }
        }
      } catch (error) {
        setTerminalCount(0);
        setTerminalStatus('none');
      }
    };

    // 初始加载
    updateTerminalStatus();

    // 定期更新终端状态
    const interval = setInterval(updateTerminalStatus, 3000);

    return () => clearInterval(interval);
  }, []);

  // 切换主题
  const toggleTheme = () => {
    const newTheme = !isDarkMode;
    setIsDarkMode(newTheme);
    const themeValue = newTheme ? 'dark' : 'light';

    document.documentElement.setAttribute('data-theme', themeValue);
    localStorage.setItem('theme', themeValue);
  };

  return (
    <div className="project-toolbar">
      <div className="toolbar-left">
      </div>
      
      <div className="toolbar-right">
        <div className="toolbar-actions">
          <button
            className="btn btn-scan toolbar-btn"
            onClick={onScanProjects}
            title="扫描Git项目 - 只扫描包含.git文件夹的项目"
          >
            <IoRefresh className="btn-icon" />
            扫描项目
          </button>
          
          <button
            className="btn btn-primary toolbar-btn"
            onClick={onAddProject}
            title="添加项目"
          >
            <IoAdd className="btn-icon" />
            添加项目
          </button>
          
          <button
            className="btn btn-pull toolbar-btn"
            onClick={onPullProjects}
            title="拉取项目"
          >
            <IoDownload className="btn-icon" />
            拉取项目
          </button>
          
          <button
            className="btn btn-clear toolbar-btn"
            onClick={onClearProjects}
            title="清空列表"
          >
            <IoTrash className="btn-icon" />
            清空列表
          </button>

          <div className="toolbar-divider"></div>

          <button
            className="btn btn-performance toolbar-btn"
            onClick={onShowPerformance}
            title="性能监控"
          >
            <IoSpeedometer className="btn-icon" />
            性能监控
          </button>

          <button
            className="btn btn-wallet toolbar-btn"
            onClick={onShowGlobalWallet}
            title="钱包管理"
          >
            <IoWallet className="btn-icon" />
            钱包管理
          </button>

          <button
            className="btn btn-proxy toolbar-btn"
            onClick={onShowProxyManager}
            title="代理管理"
          >
            <IoGlobe className="btn-icon" />
            代理管理
          </button>

          <div className="terminal-button-container">
            <button
              className={`btn btn-terminal toolbar-btn ${terminalStatus}`}
              onClick={onShowTerminalManager}
              title={`终端管理器 - ${terminalCount} 个终端${terminalStatus === 'minimized' ? '(已最小化)' : terminalStatus === 'active' ? '(运行中)' : ''}`}
            >
              <IoTerminal className="btn-icon" />
              终端管理
            </button>
            {terminalCount > 0 && (
              <span className={`badge terminal-badge ${terminalStatus}`}>{terminalCount}</span>
            )}
          </div>

          <div className="batch-button-container">
            <button
              className="btn btn-batch toolbar-btn"
              onClick={onShowBatchOperation}
              title="批量操作"
              disabled={selectedProjectsCount === 0}
            >
              <IoLayers className="btn-icon" />
              批量操作
            </button>
            {selectedProjectsCount > 0 && (
              <span className="badge batch-badge">{selectedProjectsCount}</span>
            )}
          </div>

          <div className="toolbar-divider"></div>

          <button
            className="btn btn-settings toolbar-btn"
            onClick={() => setShowGlobalSettings(true)}
            title="全局设置"
          >
            <IoSettings className="btn-icon" />
            设置
          </button>

          <button
            className="btn btn-theme toolbar-btn"
            onClick={toggleTheme}
            title={isDarkMode ? '切换到亮色主题' : '切换到暗色主题'}
          >
            {isDarkMode ? <IoSunny className="btn-icon" /> : <IoMoon className="btn-icon" />}
            {isDarkMode ? '亮色' : '暗色'}
          </button>
        </div>
      </div>

      {/* 全局设置对话框 */}
      {showGlobalSettings && (
        <GlobalSettingsDialog onClose={() => setShowGlobalSettings(false)} />
      )}
    </div>
  );
};

export default ProjectToolbar;

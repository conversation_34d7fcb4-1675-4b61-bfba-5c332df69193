.proxy-file-edit-dialog {
  width: 90vw;
  max-width: 1000px;
  height: 80vh;
  max-height: 800px;

  /* CSS变量定义 */
  --surface-color: #ffffff;
  --background-secondary: #f8f9fa;
  --background-primary: #ffffff;
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --border-color: #e1e5e9;
  --primary-color: #007acc;
  --primary-color-hover: #005a9e;
  --success-color: #28a745;
  --success-color-hover: #218838;
  --warning-color: #ffc107;
}

.proxy-file-edit-dialog .dialog-body {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0;
}

/* 文件选择器 */
.proxy-file-edit-dialog .file-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--surface-color);
}

.proxy-file-edit-dialog .file-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.proxy-file-edit-dialog .file-name {
  font-weight: 500;
  color: var(--text-primary);
}

.proxy-file-edit-dialog .unsaved-indicator {
  color: var(--warning-color);
  font-size: 1.2rem;
  line-height: 1;
}

.proxy-file-edit-dialog .file-actions {
  display: flex;
  gap: 0.5rem;
}

/* 编辑器容器 */
.proxy-file-edit-dialog .editor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* 编辑器工具栏 */
.proxy-file-edit-dialog .editor-toolbar {
  display: flex;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--surface-color);
}

/* 代理列表面板 */
.proxy-file-edit-dialog .proxy-list-panel {
  border-bottom: 1px solid var(--border-color);
  background: var(--background-secondary);
  max-height: 400px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.proxy-file-edit-dialog .proxy-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--surface-color);
  position: sticky;
  top: 0;
  z-index: 1;
}

.proxy-file-edit-dialog .proxy-list-header h4 {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-primary);
  font-weight: 600;
}

.proxy-file-edit-dialog .proxy-list-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.proxy-file-edit-dialog .proxy-item-list {
  flex: 1;
  overflow-y: auto;
}

.proxy-file-edit-dialog .no-proxies-message {
  padding: 2rem;
  text-align: center;
  color: var(--text-secondary);
}

.proxy-file-edit-dialog .no-proxies-message p {
  margin: 0;
  font-style: italic;
}

/* 代理列表项 */
.proxy-file-edit-dialog .proxy-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.proxy-file-edit-dialog .proxy-item:hover {
  background: var(--surface-color);
}

.proxy-file-edit-dialog .proxy-item:last-child {
  border-bottom: none;
}

.proxy-file-edit-dialog .proxy-selector {
  flex-shrink: 0;
}

.proxy-file-edit-dialog .checkbox-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
}

.proxy-file-edit-dialog .checkbox-btn:hover {
  background: var(--surface-color);
  color: var(--primary-color);
}

.proxy-file-edit-dialog .proxy-info {
  flex: 1;
  min-width: 0;
}

.proxy-file-edit-dialog .proxy-address-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.proxy-file-edit-dialog .proxy-address {
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9rem;
  color: var(--text-primary);
  font-weight: 500;
}

.proxy-file-edit-dialog .proxy-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.proxy-file-edit-dialog .proxy-country {
  font-size: 1rem;
}

.proxy-file-edit-dialog .proxy-status {
  font-weight: 600;
  font-size: 0.9rem;
}

.proxy-file-edit-dialog .proxy-speed {
  font-size: 0.8rem;
  color: var(--text-secondary);
  background: var(--background-primary);
  padding: 0.125rem 0.375rem;
  border-radius: 3px;
}

.proxy-file-edit-dialog .proxy-actions {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

/* 代理编辑器 */
.proxy-file-edit-dialog .proxy-editor {
  flex: 1;
  width: 100%;
  padding: 1rem;
  border: none;
  outline: none;
  resize: none;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  background: var(--background-primary);
  color: var(--text-primary);
  min-height: 300px;
}

.proxy-file-edit-dialog .proxy-editor:focus {
  background: var(--surface-color);
}

.proxy-file-edit-dialog .proxy-editor::placeholder {
  color: var(--text-secondary);
  font-style: italic;
}

/* 空状态 */
.proxy-file-edit-dialog .empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.proxy-file-edit-dialog .empty-icon {
  font-size: 3rem;
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.proxy-file-edit-dialog .empty-state h3 {
  margin: 0 0 0.5rem;
  color: var(--text-primary);
}

.proxy-file-edit-dialog .empty-state p {
  margin: 0 0 1.5rem;
  color: var(--text-secondary);
}

/* 按钮样式 */
.proxy-file-edit-dialog .btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
}

.proxy-file-edit-dialog .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.proxy-file-edit-dialog .btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.8rem;
}

.proxy-file-edit-dialog .btn-primary {
  background: var(--primary-color);
  color: white;
}

.proxy-file-edit-dialog .btn-primary:hover:not(:disabled) {
  background: var(--primary-color-hover);
}

.proxy-file-edit-dialog .btn-outline {
  background: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.proxy-file-edit-dialog .btn-outline:hover:not(:disabled) {
  background: var(--surface-color);
  border-color: var(--primary-color);
}

.proxy-file-edit-dialog .btn-success {
  background: var(--success-color);
  color: white;
}

.proxy-file-edit-dialog .btn-success:hover:not(:disabled) {
  background: var(--success-color-hover);
}

/* 暗色主题适配 */
[data-theme="dark"] .proxy-file-edit-dialog {
  --surface-color: #2a2a2a;
  --background-secondary: #1e1e1e;
  --background-primary: #1a1a1a;
  --text-primary: #e0e0e0;
  --text-secondary: #b0b0b0;
  --border-color: #404040;
  --primary-color: #007acc;
  --primary-color-hover: #005a9e;
  --success-color: #28a745;
  --success-color-hover: #218838;
  --warning-color: #ffc107;
}

[data-theme="dark"] .proxy-file-edit-dialog .file-selector {
  background: #2a2a2a;
  border-bottom-color: #404040;
}

[data-theme="dark"] .proxy-file-edit-dialog .editor-toolbar {
  background: #2a2a2a;
  border-bottom-color: #404040;
}

[data-theme="dark"] .proxy-file-edit-dialog .proxy-list-panel {
  background: #1e1e1e;
  border-bottom-color: #404040;
}

[data-theme="dark"] .proxy-file-edit-dialog .proxy-list-header {
  background: #2a2a2a;
  border-bottom-color: #404040;
}

[data-theme="dark"] .proxy-file-edit-dialog .proxy-item {
  border-bottom-color: #404040;
}

[data-theme="dark"] .proxy-file-edit-dialog .proxy-item:hover {
  background: #2a2a2a;
}

[data-theme="dark"] .proxy-file-edit-dialog .proxy-editor {
  background: #1a1a1a;
  color: #e0e0e0;
}

[data-theme="dark"] .proxy-file-edit-dialog .proxy-editor:focus {
  background: #2a2a2a;
}

/* 滚动条样式 */
.proxy-file-edit-dialog .proxy-list-panel::-webkit-scrollbar,
.proxy-file-edit-dialog .proxy-item-list::-webkit-scrollbar {
  width: 6px;
}

.proxy-file-edit-dialog .proxy-list-panel::-webkit-scrollbar-track,
.proxy-file-edit-dialog .proxy-item-list::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

.proxy-file-edit-dialog .proxy-list-panel::-webkit-scrollbar-thumb,
.proxy-file-edit-dialog .proxy-item-list::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.proxy-file-edit-dialog .proxy-list-panel::-webkit-scrollbar-thumb:hover,
.proxy-file-edit-dialog .proxy-item-list::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .proxy-file-edit-dialog {
    width: 95vw;
    height: 90vh;
  }

  .proxy-file-edit-dialog .file-selector {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .proxy-file-edit-dialog .file-actions {
    justify-content: center;
  }

  .proxy-file-edit-dialog .proxy-list-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }

  .proxy-file-edit-dialog .proxy-list-actions {
    justify-content: center;
  }

  .proxy-file-edit-dialog .proxy-item {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .proxy-file-edit-dialog .proxy-actions {
    justify-content: center;
  }

  .proxy-file-edit-dialog .proxy-address {
    font-size: 0.8rem;
  }

  .proxy-file-edit-dialog .proxy-editor {
    font-size: 0.8rem;
  }
}

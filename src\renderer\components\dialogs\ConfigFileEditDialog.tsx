import React, { useState, useEffect } from 'react';
import {
  IoClose,
  IoDocument,
  IoSave,
  IoKey,
  IoFolder,
  IoRefresh,
  IoCheckbox,
  IoSquareOutline,
  IoCheckboxOutline,
  IoCopy
} from 'react-icons/io5';
import { useNotification } from '../../contexts/NotificationContext';
import { Project } from '@shared/types/project';
import Modal from '../Modal';
import './ConfigFileEditDialog.css';

interface GlobalWallet {
  id: string;
  name: string;
  chain: string;
  address: string;
  privateKey?: string;
  mnemonic?: string;
  isEnabled: boolean;
}

interface ConfigFileEditDialogProps {
  project: Project;
  onClose: () => void;
}

const ConfigFileEditDialog: React.FC<ConfigFileEditDialogProps> = ({ project, onClose }) => {
  const { showSuccess, showError, showWarning } = useNotification();
  const [selectedFile, setSelectedFile] = useState<string>('');
  const [fileContent, setFileContent] = useState<string>('');
  const [originalContent, setOriginalContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [globalWallets, setGlobalWallets] = useState<GlobalWallet[]>([]);
  const [showWalletList, setShowWalletList] = useState(false);
  const [selectedWallets, setSelectedWallets] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadGlobalWallets();
  }, []);

  const loadGlobalWallets = async () => {
    try {
      const config = await window.electronAPI.globalWallet.getConfig();
      setGlobalWallets(config.wallets || []);
    } catch (error) {
      console.error('加载全局钱包失败:', error);
    }
  };

  const handleCopyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      showSuccess(`${type}已复制到剪贴板`);
    } catch (error) {
      showError('复制失败', error instanceof Error ? error.message : '未知错误');
    }
  };

  const handleSelectFile = async () => {
    try {
      const result = await window.electronAPI.dialog.showOpenDialog({
        title: '选择配置文件',
        defaultPath: project.path,
        filters: [
          { name: '环境配置文件', extensions: ['env'] },
          { name: '文本文件', extensions: ['txt'] },
          { name: '配置文件', extensions: ['conf', 'config', 'ini'] },
          { name: 'JSON文件', extensions: ['json'] },
          { name: 'YAML文件', extensions: ['yml', 'yaml'] },
          { name: '所有文件', extensions: ['*'] }
        ],
        properties: ['openFile']
      });

      if (!result.canceled && result.filePaths.length > 0) {
        const filePath = result.filePaths[0];
        setSelectedFile(filePath);
        await loadFileContent(filePath);
      }
    } catch (error) {
      showError('选择文件失败', error instanceof Error ? error.message : '未知错误');
    }
  };

  const loadFileContent = async (filePath: string) => {
    try {
      setIsLoading(true);
      const content = await window.electronAPI.fs.readFile(filePath);
      setFileContent(content);
      setOriginalContent(content);
    } catch (error) {
      showError('读取文件失败', error instanceof Error ? error.message : '未知错误');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveFile = async () => {
    if (!selectedFile) {
      showWarning('请先选择文件');
      return;
    }

    try {
      setIsLoading(true);
      await window.electronAPI.fs.writeFile(selectedFile, fileContent);
      setOriginalContent(fileContent);
      showSuccess('文件保存成功');
    } catch (error) {
      showError('保存文件失败', error instanceof Error ? error.message : '未知错误');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefreshFile = async () => {
    if (!selectedFile) {
      showWarning('请先选择文件');
      return;
    }

    if (fileContent !== originalContent) {
      const confirmed = window.confirm('文件已修改，刷新将丢失未保存的更改。确定要继续吗？');
      if (!confirmed) return;
    }

    await loadFileContent(selectedFile);
  };

  const toggleWalletSelection = (walletId: string) => {
    const newSelected = new Set(selectedWallets);
    if (newSelected.has(walletId)) {
      newSelected.delete(walletId);
    } else {
      newSelected.add(walletId);
    }
    setSelectedWallets(newSelected);
  };

  const selectAllWallets = () => {
    const walletsWithPrivateKey = globalWallets.filter(w => w.isEnabled && w.privateKey);
    if (selectedWallets.size === walletsWithPrivateKey.length) {
      setSelectedWallets(new Set());
    } else {
      setSelectedWallets(new Set(walletsWithPrivateKey.map(w => w.id)));
    }
  };

  const insertPrivateKey = (wallet: GlobalWallet) => {
    if (!wallet.privateKey) {
      showWarning('该钱包没有私钥信息');
      return;
    }

    // 检查是否已存在该私钥
    if (fileContent.includes(wallet.privateKey)) {
      showWarning('该私钥已存在，跳过重复插入');
      return;
    }

    const textarea = document.getElementById('config-editor') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const beforeText = fileContent.substring(0, start);
    const afterText = fileContent.substring(end);

    // 确保插入的私钥在新行
    let insertText = wallet.privateKey;

    // 如果光标前有内容且不是换行符，添加换行符
    if (beforeText.length > 0 && !beforeText.endsWith('\n')) {
      insertText = '\n' + insertText;
    }

    // 如果光标后有内容且不是换行符，添加换行符
    if (afterText.length > 0 && !afterText.startsWith('\n')) {
      insertText = insertText + '\n';
    }

    const newContent = beforeText + insertText + afterText;
    setFileContent(newContent);

    // 重新聚焦并设置光标位置
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + insertText.length, start + insertText.length);
    }, 0);
  };

  const insertSelectedPrivateKeys = () => {
    const selectedWalletsList = globalWallets.filter(w => selectedWallets.has(w.id) && w.privateKey);
    if (selectedWalletsList.length === 0) {
      showWarning('请选择要插入的钱包');
      return;
    }

    const textarea = document.getElementById('config-editor') as HTMLTextAreaElement;
    if (!textarea) return;

    // 获取所有要插入的私钥
    const privateKeys = selectedWalletsList.map(w => w.privateKey!);

    // 去重：过滤掉已存在的私钥
    const existingKeys = new Set(
      fileContent.split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0)
    );

    const newPrivateKeys = privateKeys.filter(key => !existingKeys.has(key));

    if (newPrivateKeys.length === 0) {
      showWarning('所选私钥都已存在，跳过重复插入');
      return;
    }

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const beforeText = fileContent.substring(0, start);
    const afterText = fileContent.substring(end);

    // 确保每个私钥都在新行
    let insertText = newPrivateKeys.join('\n');

    // 如果光标前有内容且不是换行符，添加换行符
    if (beforeText.length > 0 && !beforeText.endsWith('\n')) {
      insertText = '\n' + insertText;
    }

    // 如果光标后有内容且不是换行符，添加换行符
    if (afterText.length > 0 && !afterText.startsWith('\n')) {
      insertText = insertText + '\n';
    }

    const newContent = beforeText + insertText + afterText;
    setFileContent(newContent);
    setSelectedWallets(new Set());
    setShowWalletList(false);

    // 重新聚焦并设置光标位置
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + insertText.length, start + insertText.length);
    }, 0);

    const skippedCount = privateKeys.length - newPrivateKeys.length;
    if (skippedCount > 0) {
      showSuccess(`已插入 ${newPrivateKeys.length} 个私钥，跳过 ${skippedCount} 个重复私钥`);
    } else {
      showSuccess(`已插入 ${newPrivateKeys.length} 个私钥`);
    }
  };



  const getFileName = () => {
    if (!selectedFile) return '未选择文件';
    return selectedFile.split(/[/\\]/).pop() || selectedFile;
  };

  const hasUnsavedChanges = fileContent !== originalContent;

  return (
    <Modal onClose={onClose} className="config-file-edit-dialog">
      <div className="dialog-header">
        <div className="header-left">
          <IoDocument className="dialog-icon" />
          <div>
            <h2 className="dialog-title">配置文件编辑</h2>
            <p className="dialog-subtitle">{project.name}</p>
          </div>
        </div>
        <button className="dialog-close" onClick={onClose}>
          <IoClose />
        </button>
      </div>

      <div className="dialog-body">
        <div className="file-selector">
          <div className="file-info">
            <span className="file-name">{getFileName()}</span>
            {hasUnsavedChanges && <span className="unsaved-indicator">●</span>}
          </div>
          <div className="file-actions">
            <button 
              className="btn btn-outline btn-sm" 
              onClick={handleSelectFile}
              title="选择文件"
            >
              <IoFolder />
              选择文件
            </button>
            {selectedFile && (
              <button 
                className="btn btn-outline btn-sm" 
                onClick={handleRefreshFile}
                title="刷新文件"
                disabled={isLoading}
              >
                <IoRefresh />
                刷新
              </button>
            )}
          </div>
        </div>

        {selectedFile && (
          <div className="editor-container">
            <div className="editor-toolbar">
              <button 
                className="btn btn-primary btn-sm" 
                onClick={() => setShowWalletList(!showWalletList)}
                title="插入私钥"
              >
                <IoKey />
                插入私钥
              </button>
              <button 
                className="btn btn-success btn-sm" 
                onClick={handleSaveFile}
                disabled={isLoading || !hasUnsavedChanges}
                title="保存文件"
              >
                <IoSave />
                保存
              </button>
            </div>

            {showWalletList && (
              <div className="wallet-list-panel">
                <div className="wallet-list-header">
                  <h4>选择钱包私钥</h4>
                  <div className="wallet-list-actions">
                    <button
                      className="btn btn-sm btn-outline"
                      onClick={selectAllWallets}
                      title={selectedWallets.size > 0 ? '取消全选' : '全选'}
                    >
                      {selectedWallets.size > 0 ? <IoCheckboxOutline /> : <IoSquareOutline />}
                      {selectedWallets.size > 0 ? '取消全选' : '全选'}
                    </button>
                    {selectedWallets.size > 0 && (
                      <button
                        className="btn btn-sm btn-primary"
                        onClick={insertSelectedPrivateKeys}
                        title="批量插入选中的私钥"
                      >
                        <IoKey />
                        批量插入 ({selectedWallets.size})
                      </button>
                    )}
                  </div>
                </div>
                <div className="wallet-private-key-list">
                  {globalWallets.filter(w => w.isEnabled && w.privateKey).map((wallet) => (
                    <div key={wallet.id} className="private-key-item">
                      <div className="private-key-selector">
                        <button
                          className="checkbox-btn"
                          onClick={() => toggleWalletSelection(wallet.id)}
                          title={selectedWallets.has(wallet.id) ? '取消选择' : '选择'}
                        >
                          {selectedWallets.has(wallet.id) ? <IoCheckbox /> : <IoSquareOutline />}
                        </button>
                      </div>
                      <div className="private-key-info">
                        <div className="wallet-name-chain">
                          <span className="wallet-name">{wallet.name}</span>
                          <span className="wallet-chain">{wallet.chain.toUpperCase()}</span>
                        </div>
                        <div className="private-key-display">
                          <span className="private-key-text">{wallet.privateKey}</span>
                        </div>
                      </div>
                      <div className="private-key-actions">
                        <button
                          className="btn btn-sm btn-outline"
                          onClick={() => handleCopyToClipboard(wallet.privateKey!, '私钥')}
                          title="复制私钥"
                        >
                          <IoCopy />
                        </button>
                        <button
                          className="btn btn-sm btn-primary"
                          onClick={() => insertPrivateKey(wallet)}
                          title="插入私钥"
                        >
                          <IoKey />
                          插入
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <textarea
              id="config-editor"
              className="config-editor"
              value={fileContent}
              onChange={(e) => setFileContent(e.target.value)}
              placeholder="选择配置文件后在此编辑内容..."
              disabled={isLoading}
            />
          </div>
        )}

        {!selectedFile && (
          <div className="empty-state">
            <IoDocument className="empty-icon" />
            <h3>选择配置文件</h3>
            <p>点击"选择文件"按钮选择要编辑的配置文件</p>
            <button className="btn btn-primary" onClick={handleSelectFile}>
              <IoFolder />
              选择文件
            </button>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default ConfigFileEditDialog;

import React, { useState, useEffect } from 'react';
import { 
  IoClose, 
  IoDocument, 
  IoSave, 
  Io<PERSON>ey, 
  Io<PERSON><PERSON><PERSON>,
  IoRefresh
} from 'react-icons/io5';
import { useNotification } from '../../contexts/NotificationContext';
import { Project } from '@shared/types/project';
import Modal from '../Modal';
import './ConfigFileEditDialog.css';

interface GlobalWallet {
  id: string;
  name: string;
  chain: string;
  address: string;
  privateKey?: string;
  mnemonic?: string;
  isEnabled: boolean;
}

interface ConfigFileEditDialogProps {
  project: Project;
  onClose: () => void;
}

const ConfigFileEditDialog: React.FC<ConfigFileEditDialogProps> = ({ project, onClose }) => {
  const { showSuccess, showError, showWarning } = useNotification();
  const [selectedFile, setSelectedFile] = useState<string>('');
  const [fileContent, setFileContent] = useState<string>('');
  const [originalContent, setOriginalContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [globalWallets, setGlobalWallets] = useState<GlobalWallet[]>([]);
  const [showWalletList, setShowWalletList] = useState(false);

  useEffect(() => {
    loadGlobalWallets();
  }, []);

  const loadGlobalWallets = async () => {
    try {
      const config = await window.electronAPI.globalWallet.getConfig();
      setGlobalWallets(config.wallets || []);
    } catch (error) {
      console.error('加载全局钱包失败:', error);
    }
  };

  const handleSelectFile = async () => {
    try {
      const result = await window.electronAPI.dialog.showOpenDialog({
        title: '选择配置文件',
        defaultPath: project.path,
        filters: [
          { name: '环境配置文件', extensions: ['env'] },
          { name: '文本文件', extensions: ['txt'] },
          { name: '配置文件', extensions: ['conf', 'config', 'ini'] },
          { name: 'JSON文件', extensions: ['json'] },
          { name: 'YAML文件', extensions: ['yml', 'yaml'] },
          { name: '所有文件', extensions: ['*'] }
        ],
        properties: ['openFile']
      });

      if (!result.canceled && result.filePaths.length > 0) {
        const filePath = result.filePaths[0];
        setSelectedFile(filePath);
        await loadFileContent(filePath);
      }
    } catch (error) {
      showError('选择文件失败', error instanceof Error ? error.message : '未知错误');
    }
  };

  const loadFileContent = async (filePath: string) => {
    try {
      setIsLoading(true);
      const content = await window.electronAPI.fs.readFile(filePath);
      setFileContent(content);
      setOriginalContent(content);
    } catch (error) {
      showError('读取文件失败', error instanceof Error ? error.message : '未知错误');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveFile = async () => {
    if (!selectedFile) {
      showWarning('请先选择文件');
      return;
    }

    try {
      setIsLoading(true);
      await window.electronAPI.fs.writeFile(selectedFile, fileContent);
      setOriginalContent(fileContent);
      showSuccess('文件保存成功');
    } catch (error) {
      showError('保存文件失败', error instanceof Error ? error.message : '未知错误');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefreshFile = async () => {
    if (!selectedFile) {
      showWarning('请先选择文件');
      return;
    }

    if (fileContent !== originalContent) {
      const confirmed = window.confirm('文件已修改，刷新将丢失未保存的更改。确定要继续吗？');
      if (!confirmed) return;
    }

    await loadFileContent(selectedFile);
  };

  const insertPrivateKey = (wallet: GlobalWallet) => {
    if (!wallet.privateKey) {
      showWarning('该钱包没有私钥信息');
      return;
    }

    const textarea = document.getElementById('config-editor') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const newContent = fileContent.substring(0, start) + wallet.privateKey + fileContent.substring(end);
    
    setFileContent(newContent);
    setShowWalletList(false);
    
    // 重新聚焦并设置光标位置
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + wallet.privateKey.length, start + wallet.privateKey.length);
    }, 0);
  };

  const insertMnemonic = (wallet: GlobalWallet) => {
    if (!wallet.mnemonic) {
      showWarning('该钱包没有助记词信息');
      return;
    }

    const textarea = document.getElementById('config-editor') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const newContent = fileContent.substring(0, start) + wallet.mnemonic + fileContent.substring(end);
    
    setFileContent(newContent);
    setShowWalletList(false);
    
    // 重新聚焦并设置光标位置
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + wallet.mnemonic.length, start + wallet.mnemonic.length);
    }, 0);
  };

  const getFileName = () => {
    if (!selectedFile) return '未选择文件';
    return selectedFile.split(/[/\\]/).pop() || selectedFile;
  };

  const hasUnsavedChanges = fileContent !== originalContent;

  return (
    <Modal onClose={onClose} className="config-file-edit-dialog">
      <div className="dialog-header">
        <div className="header-left">
          <IoDocument className="dialog-icon" />
          <div>
            <h2 className="dialog-title">配置文件编辑</h2>
            <p className="dialog-subtitle">{project.name}</p>
          </div>
        </div>
        <button className="dialog-close" onClick={onClose}>
          <IoClose />
        </button>
      </div>

      <div className="dialog-body">
        <div className="file-selector">
          <div className="file-info">
            <span className="file-name">{getFileName()}</span>
            {hasUnsavedChanges && <span className="unsaved-indicator">●</span>}
          </div>
          <div className="file-actions">
            <button 
              className="btn btn-outline btn-sm" 
              onClick={handleSelectFile}
              title="选择文件"
            >
              <IoFolder />
              选择文件
            </button>
            {selectedFile && (
              <button 
                className="btn btn-outline btn-sm" 
                onClick={handleRefreshFile}
                title="刷新文件"
                disabled={isLoading}
              >
                <IoRefresh />
                刷新
              </button>
            )}
          </div>
        </div>

        {selectedFile && (
          <div className="editor-container">
            <div className="editor-toolbar">
              <button 
                className="btn btn-primary btn-sm" 
                onClick={() => setShowWalletList(!showWalletList)}
                title="插入私钥"
              >
                <IoKey />
                插入私钥
              </button>
              <button 
                className="btn btn-success btn-sm" 
                onClick={handleSaveFile}
                disabled={isLoading || !hasUnsavedChanges}
                title="保存文件"
              >
                <IoSave />
                保存
              </button>
            </div>

            {showWalletList && (
              <div className="wallet-list-panel">
                <h4>选择钱包</h4>
                <div className="wallet-list">
                  {globalWallets.filter(w => w.isEnabled).map((wallet) => (
                    <div key={wallet.id} className="wallet-item">
                      <div className="wallet-info">
                        <span className="wallet-name">{wallet.name}</span>
                        <span className="wallet-chain">{wallet.chain.toUpperCase()}</span>
                        <span className="wallet-address">{wallet.address}</span>
                      </div>
                      <div className="wallet-actions">
                        {wallet.privateKey && (
                          <button 
                            className="btn btn-sm btn-outline"
                            onClick={() => insertPrivateKey(wallet)}
                            title="插入私钥"
                          >
                            私钥
                          </button>
                        )}
                        {wallet.mnemonic && (
                          <button 
                            className="btn btn-sm btn-outline"
                            onClick={() => insertMnemonic(wallet)}
                            title="插入助记词"
                          >
                            助记词
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <textarea
              id="config-editor"
              className="config-editor"
              value={fileContent}
              onChange={(e) => setFileContent(e.target.value)}
              placeholder="选择配置文件后在此编辑内容..."
              disabled={isLoading}
            />
          </div>
        )}

        {!selectedFile && (
          <div className="empty-state">
            <IoDocument className="empty-icon" />
            <h3>选择配置文件</h3>
            <p>点击"选择文件"按钮选择要编辑的配置文件</p>
            <button className="btn btn-primary" onClick={handleSelectFile}>
              <IoFolder />
              选择文件
            </button>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default ConfigFileEditDialog;

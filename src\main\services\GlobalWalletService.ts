import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import { app } from 'electron';
import { ethers } from 'ethers';
import { Keypair } from '@solana/web3.js';
import * as bip39 from 'bip39';
import * as hdkey from 'hdkey';

export interface GlobalWallet {
  id: string;
  name: string;
  chain: 'eth' | 'sol' | 'sui' | 'bnb' | 'polygon' | 'avalanche' | 'fantom' | 'arbitrum' | 'optimism';
  type: 'private_key' | 'mnemonic' | 'generated';
  address: string;
  privateKey?: string;
  mnemonic?: string;
  publicKey?: string;
  derivationPath?: string;
  isEnabled: boolean;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface WalletImportData {
  content: string;
  type: 'private_key' | 'mnemonic';
  chain: string;
}

export interface GlobalWalletConfig {
  wallets: GlobalWallet[];
  settings: {
    defaultChain: string;
    autoBackup: boolean;
    encryptionEnabled: boolean;
  };
}

export class GlobalWalletService {
  private readonly configDir: string;
  private readonly configPath: string;
  private readonly encryptionKey: string;

  constructor() {
    this.configDir = path.join(app.getPath('userData'), 'wallet-config');
    this.configPath = path.join(this.configDir, 'global-wallets.json');
    this.encryptionKey = this.getOrCreateEncryptionKey();
    
    // 确保配置目录存在
    if (!fs.existsSync(this.configDir)) {
      fs.mkdirSync(this.configDir, { recursive: true });
    }
  }

  /**
   * 获取全局钱包配置
   */
  async getGlobalWalletConfig(): Promise<GlobalWalletConfig> {
    try {
      if (!fs.existsSync(this.configPath)) {
        return this.createDefaultConfig();
      }

      const encryptedContent = fs.readFileSync(this.configPath, 'utf-8');
      const decryptedContent = this.decrypt(encryptedContent);
      const config = JSON.parse(decryptedContent);
      
      return this.validateAndFixConfig(config);
    } catch (error) {
      console.error('获取全局钱包配置失败:', error);
      return this.createDefaultConfig();
    }
  }

  /**
   * 保存全局钱包配置
   */
  async saveGlobalWalletConfig(config: GlobalWalletConfig): Promise<void> {
    try {
      // 更新时间戳
      config.wallets.forEach(wallet => {
        wallet.updatedAt = new Date();
      });

      const configJson = JSON.stringify(config, null, 2);
      const encryptedContent = this.encrypt(configJson);
      fs.writeFileSync(this.configPath, encryptedContent, 'utf-8');
      
      console.log('全局钱包配置已保存');
    } catch (error) {
      console.error('保存全局钱包配置失败:', error);
      throw new Error(`保存全局钱包配置失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 添加钱包
   */
  async addWallet(wallet: Omit<GlobalWallet, 'id' | 'createdAt' | 'updatedAt'>): Promise<GlobalWallet> {
    try {
      const config = await this.getGlobalWalletConfig();
      
      const newWallet: GlobalWallet = {
        ...wallet,
        id: this.generateWalletId(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      config.wallets.push(newWallet);
      await this.saveGlobalWalletConfig(config);
      
      return newWallet;
    } catch (error) {
      console.error('添加钱包失败:', error);
      throw new Error(`添加钱包失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 批量添加钱包
   */
  async addWallets(wallets: Omit<GlobalWallet, 'id' | 'createdAt' | 'updatedAt'>[]): Promise<GlobalWallet[]> {
    try {
      const config = await this.getGlobalWalletConfig();
      const newWallets: GlobalWallet[] = [];
      
      for (const wallet of wallets) {
        const newWallet: GlobalWallet = {
          ...wallet,
          id: this.generateWalletId(),
          createdAt: new Date(),
          updatedAt: new Date()
        };
        newWallets.push(newWallet);
        config.wallets.push(newWallet);
      }

      await this.saveGlobalWalletConfig(config);
      return newWallets;
    } catch (error) {
      console.error('批量添加钱包失败:', error);
      throw new Error(`批量添加钱包失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 更新钱包
   */
  async updateWallet(walletId: string, updates: Partial<GlobalWallet>): Promise<GlobalWallet> {
    try {
      const config = await this.getGlobalWalletConfig();
      const walletIndex = config.wallets.findIndex(w => w.id === walletId);
      
      if (walletIndex === -1) {
        throw new Error(`钱包 ${walletId} 不存在`);
      }

      const updatedWallet = {
        ...config.wallets[walletIndex],
        ...updates,
        id: walletId,
        updatedAt: new Date()
      };

      config.wallets[walletIndex] = updatedWallet;
      await this.saveGlobalWalletConfig(config);
      
      return updatedWallet;
    } catch (error) {
      console.error('更新钱包失败:', error);
      throw new Error(`更新钱包失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 删除钱包
   */
  async deleteWallet(walletId: string): Promise<void> {
    try {
      const config = await this.getGlobalWalletConfig();
      const walletIndex = config.wallets.findIndex(w => w.id === walletId);
      
      if (walletIndex === -1) {
        throw new Error(`钱包 ${walletId} 不存在`);
      }

      config.wallets.splice(walletIndex, 1);
      await this.saveGlobalWalletConfig(config);
      
      console.log(`钱包 ${walletId} 已删除`);
    } catch (error) {
      console.error('删除钱包失败:', error);
      throw new Error(`删除钱包失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 批量删除钱包
   */
  async deleteWallets(walletIds: string[]): Promise<void> {
    try {
      const config = await this.getGlobalWalletConfig();
      
      config.wallets = config.wallets.filter(w => !walletIds.includes(w.id));
      await this.saveGlobalWalletConfig(config);
      
      console.log(`已删除 ${walletIds.length} 个钱包`);
    } catch (error) {
      console.error('批量删除钱包失败:', error);
      throw new Error(`批量删除钱包失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 从文件导入钱包
   */
  async importWalletsFromFile(filePath: string, chain: string): Promise<GlobalWallet[]> {
    try {
      if (!fs.existsSync(filePath)) {
        throw new Error('文件不存在');
      }

      // 获取当前钱包配置以确定起始编号
      const config = await this.getConfig();
      const existingWalletCount = config.wallets.length;

      const content = fs.readFileSync(filePath, 'utf-8');
      const lines = content.split('\n').filter(line => line.trim());

      const wallets: Omit<GlobalWallet, 'id' | 'createdAt' | 'updatedAt'>[] = [];
      let validWalletIndex = 0;

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;

        try {
          const walletData = await this.parseWalletData(line, chain as any);
          if (walletData) {
            const walletNumber = existingWalletCount + validWalletIndex + 1;
            wallets.push({
              name: `钱包${walletNumber.toString().padStart(3, '0')}`,
              chain: chain as any,
              type: walletData.type,
              address: walletData.address,
              privateKey: walletData.privateKey,
              mnemonic: walletData.mnemonic,
              publicKey: walletData.publicKey,
              derivationPath: walletData.derivationPath,
              isEnabled: true,
              tags: ['导入']
            });
            validWalletIndex++;
          }
        } catch (error) {
          console.warn(`解析第 ${i + 1} 行失败:`, error);
        }
      }

      if (wallets.length === 0) {
        throw new Error('未找到有效的钱包数据');
      }

      return await this.addWallets(wallets);
    } catch (error) {
      console.error('从文件导入钱包失败:', error);
      throw new Error(`从文件导入钱包失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 批量创建新钱包
   */
  async createWallets(count: number, chain: string): Promise<GlobalWallet[]> {
    try {
      // 获取当前钱包配置以确定起始编号
      const config = await this.getConfig();
      const existingWalletCount = config.wallets.length;

      const wallets: Omit<GlobalWallet, 'id' | 'createdAt' | 'updatedAt'>[] = [];

      for (let i = 0; i < count; i++) {
        const walletData = await this.generateWallet(chain as any);
        const walletNumber = existingWalletCount + i + 1;
        wallets.push({
          name: `钱包${walletNumber.toString().padStart(3, '0')}`,
          chain: chain as any,
          type: 'generated',
          address: walletData.address,
          privateKey: walletData.privateKey,
          mnemonic: walletData.mnemonic,
          publicKey: walletData.publicKey,
          derivationPath: walletData.derivationPath,
          isEnabled: true,
          tags: ['生成']
        });
      }

      return await this.addWallets(wallets);
    } catch (error) {
      console.error('批量创建钱包失败:', error);
      throw new Error(`批量创建钱包失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 根据助记词获取地址
   */
  async getAddressFromMnemonic(mnemonic: string, chain: string, derivationPath?: string): Promise<string> {
    try {
      if (!bip39.validateMnemonic(mnemonic)) {
        throw new Error('无效的助记词');
      }

      const seed = bip39.mnemonicToSeedSync(mnemonic);

      switch (chain) {
        case 'eth':
        case 'bnb':
        case 'polygon':
        case 'avalanche':
        case 'fantom':
        case 'arbitrum':
        case 'optimism':
          const ethPath = derivationPath || "m/44'/60'/0'/0/0";
          const ethWallet = ethers.HDNodeWallet.fromSeed(seed).derivePath(ethPath);
          return ethWallet.address;

        case 'sol':
          const solPath = derivationPath || "m/44'/501'/0'/0'";
          const root = hdkey.fromMasterSeed(seed);
          const solNode = root.derive(solPath);
          const solKeypair = Keypair.fromSeed(solNode.privateKey);
          return solKeypair.publicKey.toBase58();

        case 'sui':
          // Sui 使用 Ed25519，路径为 m/44'/784'/0'/0'/0'
          const suiPath = derivationPath || "m/44'/784'/0'/0'/0'";
          const suiRoot = hdkey.fromMasterSeed(seed);
          const suiNode = suiRoot.derive(suiPath);
          const suiAddress = '0x' + crypto.createHash('sha256').update(suiNode.publicKey).digest('hex').substring(0, 64);
          return suiAddress;

        default:
          const defaultPath = derivationPath || "m/44'/60'/0'/0/0";
          const defaultWallet = ethers.HDNodeWallet.fromSeed(seed).derivePath(defaultPath);
          return defaultWallet.address;
      }
    } catch (error) {
      console.error('从助记词获取地址失败:', error);
      throw new Error(`从助记词获取地址失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 解析钱包数据
   */
  private async parseWalletData(data: string, chain: string): Promise<any> {
    // 检查是否是私钥
    if (data.match(/^[0-9a-fA-F]{64}$/) || data.match(/^0x[0-9a-fA-F]{64}$/)) {
      const privateKey = data.startsWith('0x') ? data : '0x' + data;
      const address = this.getAddressFromPrivateKey(privateKey, chain);
      return {
        type: 'private_key',
        privateKey,
        address
      };
    }

    // 检查是否是助记词
    const words = data.trim().split(/\s+/);
    if (words.length === 12 || words.length === 24) {
      const mnemonic = words.join(' ');
      const address = await this.getAddressFromMnemonic(mnemonic, chain);
      return {
        type: 'mnemonic',
        mnemonic,
        address
      };
    }

    return null;
  }

  /**
   * 从私钥获取地址
   */
  private getAddressFromPrivateKey(privateKey: string, chain: string): string {
    try {
      switch (chain) {
        case 'eth':
        case 'bnb':
        case 'polygon':
        case 'avalanche':
        case 'fantom':
        case 'arbitrum':
        case 'optimism':
          const ethWallet = new ethers.Wallet(privateKey);
          return ethWallet.address;

        case 'sol':
          // Solana 私钥是32字节，需要特殊处理
          const privateKeyBytes = Buffer.from(privateKey.replace('0x', ''), 'hex');
          if (privateKeyBytes.length !== 32) {
            throw new Error('Solana私钥必须是32字节');
          }
          const solKeypair = Keypair.fromSecretKey(privateKeyBytes);
          return solKeypair.publicKey.toBase58();

        case 'sui':
          // Sui 使用 Ed25519
          const suiPrivateKeyBytes = Buffer.from(privateKey.replace('0x', ''), 'hex');
          if (suiPrivateKeyBytes.length !== 32) {
            throw new Error('Sui私钥必须是32字节');
          }
          const suiAddress = '0x' + crypto.createHash('sha256').update(suiPrivateKeyBytes).digest('hex').substring(0, 64);
          return suiAddress;

        default:
          const defaultWallet = new ethers.Wallet(privateKey);
          return defaultWallet.address;
      }
    } catch (error) {
      console.error('从私钥获取地址失败:', error);
      throw new Error(`从私钥获取地址失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 生成新钱包
   */
  private async generateWallet(chain: string): Promise<any> {
    try {
      // 生成真实的助记词
      const mnemonic = bip39.generateMnemonic(128); // 12个单词
      const seed = bip39.mnemonicToSeedSync(mnemonic);

      let privateKey: string;
      let address: string;
      let publicKey: string;
      let derivationPath: string;

      switch (chain) {
        case 'eth':
        case 'bnb':
        case 'polygon':
        case 'avalanche':
        case 'fantom':
        case 'arbitrum':
        case 'optimism':
          derivationPath = "m/44'/60'/0'/0/0";
          const ethWallet = ethers.HDNodeWallet.fromSeed(seed).derivePath(derivationPath);
          privateKey = ethWallet.privateKey;
          address = ethWallet.address;
          publicKey = ethWallet.publicKey;
          break;

        case 'sol':
          derivationPath = "m/44'/501'/0'/0'";
          const root = hdkey.fromMasterSeed(seed);
          const solNode = root.derive(derivationPath);
          const solKeypair = Keypair.fromSeed(solNode.privateKey);
          privateKey = '0x' + Buffer.from(solKeypair.secretKey).toString('hex');
          address = solKeypair.publicKey.toBase58();
          publicKey = solKeypair.publicKey.toBase58();
          break;

        case 'sui':
          derivationPath = "m/44'/784'/0'/0'/0'";
          const suiRoot = hdkey.fromMasterSeed(seed);
          const suiNode = suiRoot.derive(derivationPath);
          privateKey = '0x' + suiNode.privateKey.toString('hex');
          address = '0x' + crypto.createHash('sha256').update(suiNode.publicKey).digest('hex').substring(0, 64);
          publicKey = '0x' + suiNode.publicKey.toString('hex');
          break;

        default:
          derivationPath = "m/44'/60'/0'/0/0";
          const defaultWallet = ethers.HDNodeWallet.fromSeed(seed).derivePath(derivationPath);
          privateKey = defaultWallet.privateKey;
          address = defaultWallet.address;
          publicKey = defaultWallet.publicKey;
          break;
      }

      return {
        privateKey,
        mnemonic,
        address,
        publicKey,
        derivationPath
      };
    } catch (error) {
      console.error('生成钱包失败:', error);
      throw new Error(`生成钱包失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 创建默认配置
   */
  private createDefaultConfig(): GlobalWalletConfig {
    return {
      wallets: [],
      settings: {
        defaultChain: 'eth',
        autoBackup: true,
        encryptionEnabled: true
      }
    };
  }

  /**
   * 验证并修复配置
   */
  private validateAndFixConfig(config: any): GlobalWalletConfig {
    const validatedConfig: GlobalWalletConfig = {
      wallets: [],
      settings: {
        defaultChain: config.settings?.defaultChain || 'eth',
        autoBackup: config.settings?.autoBackup !== false,
        encryptionEnabled: config.settings?.encryptionEnabled !== false
      }
    };

    if (Array.isArray(config.wallets)) {
      validatedConfig.wallets = config.wallets.map((wallet: any) => ({
        id: wallet.id || this.generateWalletId(),
        name: wallet.name || '未命名钱包',
        chain: wallet.chain || 'eth',
        type: wallet.type || 'generated',
        address: wallet.address || '',
        privateKey: wallet.privateKey || '',
        mnemonic: wallet.mnemonic || '',
        publicKey: wallet.publicKey || '',
        derivationPath: wallet.derivationPath || '',
        isEnabled: wallet.isEnabled !== false,
        tags: Array.isArray(wallet.tags) ? wallet.tags : [],
        createdAt: wallet.createdAt ? new Date(wallet.createdAt) : new Date(),
        updatedAt: wallet.updatedAt ? new Date(wallet.updatedAt) : new Date()
      }));
    }

    return validatedConfig;
  }

  /**
   * 生成钱包ID
   */
  private generateWalletId(): string {
    return 'wallet_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 获取或创建加密密钥
   */
  private getOrCreateEncryptionKey(): string {
    const keyPath = path.join(this.configDir, '.key');

    if (fs.existsSync(keyPath)) {
      return fs.readFileSync(keyPath, 'utf-8');
    }

    // 确保配置目录存在
    if (!fs.existsSync(this.configDir)) {
      fs.mkdirSync(this.configDir, { recursive: true });
    }

    const key = crypto.randomBytes(32).toString('hex');
    fs.writeFileSync(keyPath, key, 'utf-8');

    return key;
  }

  /**
   * 加密数据
   */
  private encrypt(text: string): string {
    const iv = crypto.randomBytes(16);
    const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
    const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return iv.toString('hex') + ':' + encrypted;
  }

  /**
   * 解密数据
   */
  private decrypt(text: string): string {
    const parts = text.split(':');
    const iv = Buffer.from(parts.shift()!, 'hex');
    const encryptedText = parts.join(':');
    const key = crypto.scryptSync(this.encryptionKey, 'salt', 32);
    const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
    let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }
}

{"name": "script-workstation", "version": "1.0.0", "description": "基于 Electron + React + TypeScript 开发的脚本工作站软件", "main": "dist/main/main.js", "scripts": {"dev": "vite", "dev:electron": "node scripts/start-electron.js", "dev:old": "concurrently --kill-others --kill-others-on-fail \"npm run dev:main\" \"npm run dev:renderer\" \"npm run dev:electron:old\"", "dev:main": "cross-env NODE_ENV=development webpack --config webpack.main.config.js --watch --mode development", "dev:renderer": "cross-env NODE_ENV=development webpack serve --config webpack.renderer.config.js", "dev:electron:old": "wait-on http://localhost:3000 && wait-on dist/main/main.js && cross-env NODE_ENV=development electron . --inspect=5858", "build": "vite build", "build:old": "npm run build:main && npm run build:renderer", "build:main": "cross-env NODE_ENV=production webpack --config webpack.main.config.js", "build:renderer": "cross-env NODE_ENV=production webpack --config webpack.renderer.config.js", "start": "electron .", "pack": "electron-builder", "dist": "npm run build && electron-builder", "postinstall": "electron-builder install-app-deps"}, "keywords": ["electron", "react", "typescript", "project-management", "script-workstation"], "author": "Script Workstation Team", "license": "MIT", "devDependencies": {"@electron/rebuild": "^3.7.2", "@pmmmwh/react-refresh-webpack-plugin": "^0.6.0", "@types/better-sqlite3": "^7.6.13", "@types/iconv-lite": "^0.0.1", "@types/node": "^20.10.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.5.1", "assert": "^2.1.0", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "chokidar": "^4.0.3", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "crypto-browserify": "^3.12.1", "css-loader": "^6.8.1", "electron": "^30.0.1", "electron-builder": "^24.13.3", "electron-rebuild": "^3.2.9", "electron-reload": "^2.0.0-alpha.1", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "events": "^3.3.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.0", "https-browserify": "^1.0.0", "nodemon": "^3.1.10", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "react-refresh": "^0.17.0", "react-refresh-typescript": "^2.0.10", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "style-loader": "^3.3.3", "ts-loader": "^9.5.1", "typescript": "^5.3.3", "url": "^0.11.4", "util": "^0.12.5", "vite": "^6.3.5", "vite-plugin-electron": "^0.29.0", "wait-on": "^8.0.3", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "dependencies": {"@hello-pangea/dnd": "^16.3.0", "@solana/web3.js": "^1.98.2", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-web-links": "^0.11.0", "axios": "^1.9.0", "better-sqlite3": "^9.2.2", "bip39": "^3.1.0", "ethers": "^6.14.3", "hdkey": "^2.1.0", "https-proxy-agent": "^7.0.6", "iconv-lite": "^0.6.3", "node-pty": "^1.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.12.0", "secp256k1": "^5.0.1", "simple-git": "^3.28.0", "smart-buffer": "^4.2.0", "socks-proxy-agent": "^8.0.5", "uuid": "^9.0.1", "xterm": "^5.3.0"}, "build": {"appId": "com.scriptworkstation.app", "productName": "Script Workstation", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.developer-tools"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}